/**
 * 性能优化相关的自定义Hook
 * 提供各种性能优化功能
 */

import { useState, useEffect, useCallback, useMemo, useRef } from 'react';
import { debounce, throttle, performanceMonitor } from '../utils/performance';
import { DEBOUNCE_CONFIG, VIRTUAL_SCROLL_CONFIG } from '../config/performance';

// 防抖Hook
export function useDebounce<T>(value: T, delay: number = DEBOUNCE_CONFIG.SEARCH): T {
  const [debouncedValue, setDebouncedValue] = useState<T>(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
}

// 防抖回调Hook
export function useDebouncedCallback<T extends (...args: any[]) => any>(
  callback: T,
  delay: number = DEBOUNCE_CONFIG.SEARCH
): T {
  const debouncedCallback = useMemo(
    () => debounce(callback, delay),
    [callback, delay]
  );

  return debouncedCallback as T;
}

// 节流回调Hook
export function useThrottledCallback<T extends (...args: any[]) => any>(
  callback: T,
  delay: number = DEBOUNCE_CONFIG.SCROLL
): T {
  const throttledCallback = useMemo(
    () => throttle(callback, delay),
    [callback, delay]
  );

  return throttledCallback as T;
}

// 虚拟滚动Hook
export function useVirtualScroll<T>(
  items: T[],
  containerHeight: number,
  itemHeight: number = VIRTUAL_SCROLL_CONFIG.ITEM_HEIGHT,
  overscan: number = VIRTUAL_SCROLL_CONFIG.OVERSCAN
) {
  const [scrollTop, setScrollTop] = useState(0);

  const visibleRange = useMemo(() => {
    const visibleStart = Math.floor(scrollTop / itemHeight);
    const visibleEnd = Math.min(
      visibleStart + Math.ceil(containerHeight / itemHeight),
      items.length - 1
    );

    const startIndex = Math.max(0, visibleStart - overscan);
    const endIndex = Math.min(items.length - 1, visibleEnd + overscan);

    return { startIndex, endIndex };
  }, [scrollTop, itemHeight, containerHeight, overscan, items.length]);

  const visibleItems = useMemo(() => {
    return items.slice(visibleRange.startIndex, visibleRange.endIndex + 1);
  }, [items, visibleRange]);

  const totalHeight = items.length * itemHeight;

  const handleScroll = useThrottledCallback((newScrollTop: number) => {
    setScrollTop(newScrollTop);
  }, 16);

  return {
    visibleItems,
    visibleRange,
    totalHeight,
    handleScroll,
  };
}

// 懒加载Hook
export function useLazyLoad(
  threshold: number = 100,
  rootMargin: string = '0px'
) {
  const [isVisible, setIsVisible] = useState(false);
  const [hasLoaded, setHasLoaded] = useState(false);
  const elementRef = useRef<HTMLElement>(null);

  useEffect(() => {
    const element = elementRef.current;
    if (!element || hasLoaded) return;

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
          setHasLoaded(true);
          observer.disconnect();
        }
      },
      {
        threshold: threshold / 100,
        rootMargin,
      }
    );

    observer.observe(element);

    return () => {
      observer.disconnect();
    };
  }, [threshold, rootMargin, hasLoaded]);

  return { elementRef, isVisible, hasLoaded };
}

// 图片懒加载Hook
export function useImageLazyLoad(src: string, placeholder?: string) {
  const [imageSrc, setImageSrc] = useState(placeholder || '');
  const [isLoaded, setIsLoaded] = useState(false);
  const [isError, setIsError] = useState(false);
  const { elementRef, isVisible } = useLazyLoad();

  useEffect(() => {
    if (!isVisible || isLoaded || isError) return;

    const img = new Image();
    img.onload = () => {
      setImageSrc(src);
      setIsLoaded(true);
    };
    img.onerror = () => {
      setIsError(true);
    };
    img.src = src;
  }, [src, isVisible, isLoaded, isError]);

  return { elementRef, imageSrc, isLoaded, isError };
}

// 性能监控Hook
export function usePerformanceMonitor(name: string, dependencies: any[] = []) {
  useEffect(() => {
    performanceMonitor.startMeasure(name);
    
    return () => {
      performanceMonitor.endMeasure(name);
    };
  }, dependencies);

  const startMeasure = useCallback((measureName: string) => {
    performanceMonitor.startMeasure(measureName);
  }, []);

  const endMeasure = useCallback((measureName: string) => {
    return performanceMonitor.endMeasure(measureName);
  }, []);

  return { startMeasure, endMeasure };
}

// 内存使用监控Hook
export function useMemoryMonitor(interval: number = 5000) {
  const [memoryInfo, setMemoryInfo] = useState<MemoryInfo | null>(null);

  useEffect(() => {
    const updateMemoryInfo = () => {
      // @ts-ignore
      if (performance.memory) {
        // @ts-ignore
        setMemoryInfo(performance.memory);
      }
    };

    updateMemoryInfo();
    const intervalId = setInterval(updateMemoryInfo, interval);

    return () => {
      clearInterval(intervalId);
    };
  }, [interval]);

  return memoryInfo;
}

// 网络状态监控Hook
export function useNetworkStatus() {
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const [connectionType, setConnectionType] = useState<string>('unknown');

  useEffect(() => {
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    // 检测连接类型
    // @ts-ignore
    const connection = navigator.connection || navigator.mozConnection || navigator.webkitConnection;
    if (connection) {
      setConnectionType(connection.effectiveType || 'unknown');
      
      const handleConnectionChange = () => {
        setConnectionType(connection.effectiveType || 'unknown');
      };
      
      connection.addEventListener('change', handleConnectionChange);
      
      return () => {
        window.removeEventListener('online', handleOnline);
        window.removeEventListener('offline', handleOffline);
        connection.removeEventListener('change', handleConnectionChange);
      };
    }

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  return { isOnline, connectionType };
}

// 窗口大小Hook
export function useWindowSize() {
  const [windowSize, setWindowSize] = useState({
    width: window.innerWidth,
    height: window.innerHeight,
  });

  const handleResize = useThrottledCallback(() => {
    setWindowSize({
      width: window.innerWidth,
      height: window.innerHeight,
    });
  }, DEBOUNCE_CONFIG.RESIZE);

  useEffect(() => {
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [handleResize]);

  return windowSize;
}

// 滚动位置Hook
export function useScrollPosition(element?: HTMLElement | null) {
  const [scrollPosition, setScrollPosition] = useState({ x: 0, y: 0 });

  const handleScroll = useThrottledCallback(() => {
    if (element) {
      setScrollPosition({
        x: element.scrollLeft,
        y: element.scrollTop,
      });
    } else {
      setScrollPosition({
        x: window.pageXOffset,
        y: window.pageYOffset,
      });
    }
  }, DEBOUNCE_CONFIG.SCROLL);

  useEffect(() => {
    const target = element || window;
    target.addEventListener('scroll', handleScroll);
    
    // 初始化位置
    handleScroll();
    
    return () => target.removeEventListener('scroll', handleScroll);
  }, [element, handleScroll]);

  return scrollPosition;
}

// 可见性Hook
export function useVisibility() {
  const [isVisible, setIsVisible] = useState(!document.hidden);

  useEffect(() => {
    const handleVisibilityChange = () => {
      setIsVisible(!document.hidden);
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    return () => document.removeEventListener('visibilitychange', handleVisibilityChange);
  }, []);

  return isVisible;
}

// 预取Hook
export function usePrefetch<T>(
  prefetchFn: () => Promise<T>,
  delay: number = 200,
  enabled: boolean = true
) {
  const timeoutRef = useRef<NodeJS.Timeout>();
  const [isPrefetching, setIsPrefetching] = useState(false);

  const startPrefetch = useCallback(() => {
    if (!enabled || isPrefetching) return;

    timeoutRef.current = setTimeout(async () => {
      setIsPrefetching(true);
      try {
        await prefetchFn();
      } catch (error) {
        console.warn('Prefetch failed:', error);
      } finally {
        setIsPrefetching(false);
      }
    }, delay);
  }, [prefetchFn, delay, enabled, isPrefetching]);

  const cancelPrefetch = useCallback(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = undefined;
    }
  }, []);

  useEffect(() => {
    return () => {
      cancelPrefetch();
    };
  }, [cancelPrefetch]);

  return { startPrefetch, cancelPrefetch, isPrefetching };
}

// 批量操作Hook
export function useBatchOperation<T>(
  batchSize: number = 10,
  delay: number = 100
) {
  const [queue, setQueue] = useState<T[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const timeoutRef = useRef<NodeJS.Timeout>();

  const addToQueue = useCallback((item: T) => {
    setQueue(prev => [...prev, item]);
  }, []);

  const processBatch = useCallback(async (
    items: T[],
    processor: (batch: T[]) => Promise<void>
  ) => {
    if (items.length === 0 || isProcessing) return;

    setIsProcessing(true);
    
    try {
      const batches = [];
      for (let i = 0; i < items.length; i += batchSize) {
        batches.push(items.slice(i, i + batchSize));
      }

      for (const batch of batches) {
        await processor(batch);
        await new Promise(resolve => setTimeout(resolve, delay));
      }

      setQueue([]);
    } catch (error) {
      console.error('Batch processing failed:', error);
    } finally {
      setIsProcessing(false);
    }
  }, [batchSize, delay, isProcessing]);

  const debouncedProcess = useDebouncedCallback((
    processor: (batch: T[]) => Promise<void>
  ) => {
    processBatch(queue, processor);
  }, delay);

  return {
    queue,
    addToQueue,
    processBatch: debouncedProcess,
    isProcessing,
  };
}
