/**
 * 端到端邮件工作流测试
 * 测试完整的邮件系统功能流程
 */

import { describe, it, expect, beforeAll, afterAll, beforeEach, vi } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { BrowserRouter } from 'react-router-dom';
import { message } from 'antd';
import React from 'react';

// 组件导入
import LoginForm from '../../components/Auth/LoginForm';
import EmailComposer from '../../components/Email/EmailComposer';
import EmailListOptimized from '../../components/Email/EmailListOptimized';

// Mock API服务
import { apiService } from '../../services/api';

// Mock message组件
vi.mock('antd', async () => {
  const actual = await vi.importActual('antd');
  return {
    ...actual,
    message: {
      success: vi.fn(),
      error: vi.fn(),
      warning: vi.fn(),
      info: vi.fn(),
    },
  };
});

// Mock API服务
vi.mock('../../services/api', () => ({
  apiService: {
    login: vi.fn(),
    register: vi.fn(),
    getCurrentUser: vi.fn(),
    logout: vi.fn(),
    getEmails: vi.fn(),
    getEmail: vi.fn(),
    sendEmail: vi.fn(),
    updateEmail: vi.fn(),
    deleteEmail: vi.fn(),
    getFolders: vi.fn(),
    getFolder: vi.fn(),
    searchEmails: vi.fn(),
  },
}));

// 测试工具函数
const createTestQueryClient = () => {
  return new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
        gcTime: 0,
      },
      mutations: {
        retry: false,
      },
    },
  });
};

const renderWithProviders = (component: React.ReactElement) => {
  const queryClient = createTestQueryClient();
  
  return render(
    <QueryClientProvider client={queryClient}>
      <BrowserRouter>
        {component}
      </BrowserRouter>
    </QueryClientProvider>
  );
};

// 模拟数据
const mockUser = {
  id: 'user-1',
  email: '<EMAIL>',
  username: 'testuser',
  displayName: '测试用户',
  createdAt: '2024-01-01T00:00:00Z',
  updatedAt: '2024-01-01T00:00:00Z',
  isActive: true,
};

const mockAuthResponse = {
  user: mockUser,
  token: 'mock-jwt-token',
  refreshToken: 'mock-refresh-token',
};

const mockFolders = [
  {
    id: 'inbox',
    userId: 'user-1',
    name: '收件箱',
    type: 'inbox' as const,
    emailCount: 5,
    unreadCount: 2,
    createdAt: '2024-01-01T00:00:00Z',
  },
  {
    id: 'sent',
    userId: 'user-1',
    name: '已发送',
    type: 'sent' as const,
    emailCount: 3,
    unreadCount: 0,
    createdAt: '2024-01-01T00:00:00Z',
  },
];

const mockEmails = [
  {
    id: 'email-1',
    messageId: 'msg-1',
    userId: 'user-1',
    folderId: 'inbox',
    subject: '测试邮件1',
    senderEmail: '<EMAIL>',
    senderName: '发件人1',
    recipients: ['<EMAIL>'],
    contentText: '这是测试邮件内容1',
    contentHtml: '<p>这是测试邮件内容1</p>',
    attachments: [],
    isRead: false,
    isStarred: false,
    isDeleted: false,
    createdAt: '2024-01-01T10:00:00Z',
  },
  {
    id: 'email-2',
    messageId: 'msg-2',
    userId: 'user-1',
    folderId: 'inbox',
    subject: '测试邮件2',
    senderEmail: '<EMAIL>',
    senderName: '发件人2',
    recipients: ['<EMAIL>'],
    contentText: '这是测试邮件内容2',
    contentHtml: '<p>这是测试邮件内容2</p>',
    attachments: [],
    isRead: true,
    isStarred: true,
    isDeleted: false,
    createdAt: '2024-01-01T11:00:00Z',
  },
];

describe('邮件系统端到端测试', () => {
  let mockApiService: any;

  beforeAll(() => {
    mockApiService = apiService as any;
    
    // 设置localStorage mock
    Object.defineProperty(window, 'localStorage', {
      value: {
        getItem: vi.fn(),
        setItem: vi.fn(),
        removeItem: vi.fn(),
        clear: vi.fn(),
      },
      writable: true,
    });
  });

  beforeEach(() => {
    vi.clearAllMocks();
    localStorage.clear();
  });

  afterAll(() => {
    vi.restoreAllMocks();
  });

  describe('用户认证流程', () => {
    it('应该能够成功登录', async () => {
      const user = userEvent.setup();
      
      // Mock API响应
      mockApiService.login.mockResolvedValue({
        data: { success: true, data: mockAuthResponse },
      } as any);

      renderWithProviders(<LoginForm />);

      // 输入登录信息
      const emailInput = screen.getByPlaceholderText('请输入邮箱');
      const passwordInput = screen.getByPlaceholderText('请输入密码');
      const loginButton = screen.getByRole('button', { name: /登.*录/i });

      await user.type(emailInput, '<EMAIL>');
      await user.type(passwordInput, 'password123');
      await user.click(loginButton);

      // 验证API调用
      await waitFor(() => {
        expect(mockApiService.login).toHaveBeenCalledWith({
          email: '<EMAIL>',
          password: 'password123',
        });
      });
    });

    it('应该处理登录错误', async () => {
      const user = userEvent.setup();
      
      // Mock API错误响应
      mockApiService.login.mockRejectedValue({
        response: {
          status: 401,
          data: { error: '邮箱或密码错误' },
        },
      });

      renderWithProviders(<LoginForm />);

      const emailInput = screen.getByPlaceholderText('请输入邮箱');
      const passwordInput = screen.getByPlaceholderText('请输入密码');
      const loginButton = screen.getByRole('button', { name: /登.*录/i });

      await user.type(emailInput, '<EMAIL>');
      await user.type(passwordInput, 'wrongpassword');
      await user.click(loginButton);

      // 验证错误处理
      await waitFor(() => {
        expect(message.error).toHaveBeenCalled();
      });
    });
  });

  describe('邮件撰写功能', () => {
    beforeEach(() => {
      // Mock已登录状态
      (localStorage.getItem as any).mockImplementation((key: string) => {
        if (key === 'token') return 'mock-jwt-token';
        return null;
      });
    });

    it('应该能够撰写和发送邮件', async () => {
      const user = userEvent.setup();
      
      mockApiService.sendEmail.mockResolvedValue({
        data: { success: true, data: { id: 'new-email-id' } },
      } as any);

      renderWithProviders(<EmailComposer />);

      // 填写邮件信息
      const toInput = screen.getByPlaceholderText('输入收件人邮箱地址，多个地址用逗号分隔');
      const subjectInput = screen.getByPlaceholderText('输入邮件主题');
      const contentTextarea = screen.getByPlaceholderText('输入邮件内容...');
      const sendButton = screen.getByRole('button', { name: /发送/i });

      await user.type(toInput, '<EMAIL>');
      await user.type(subjectInput, '测试邮件主题');
      await user.type(contentTextarea, '这是测试邮件内容');

      await user.click(sendButton);

      // 验证API调用
      await waitFor(() => {
        expect(mockApiService.sendEmail).toHaveBeenCalledWith({
          to: ['<EMAIL>'],
          subject: '测试邮件主题',
          content: '这是测试邮件内容',
          attachments: [],
        });
      });
    });

    it('应该验证必填字段', async () => {
      const user = userEvent.setup();
      
      renderWithProviders(<EmailComposer />);

      const sendButton = screen.getByRole('button', { name: /发送/i });
      await user.click(sendButton);

      // 验证错误消息
      await waitFor(() => {
        expect(message.error).toHaveBeenCalledWith('请输入收件人');
      });
    });
  });
});
