import { useQuery } from '@tanstack/react-query';
import { apiService } from '../services/api';
import { queryKeys, queryOptions } from '../lib/queryKeys';

// 仪表板统计数据类型
interface DashboardStats {
  totalEmails: number;
  unreadEmails: number;
  starredEmails: number;
  sentEmails: number;
  draftEmails: number;
  recentActivity: {
    date: string;
    count: number;
  }[];
  folderStats: {
    folderId: string;
    folderName: string;
    emailCount: number;
    unreadCount: number;
  }[];
}

// 邮件数量统计类型
interface EmailCounts {
  total: number;
  unread: number;
  starred: number;
  sent: number;
  draft: number;
  trash: number;
}

// 获取仪表板统计数据
export const useDashboardStats = () => {
  return useQuery({
    queryKey: queryKeys.stats.dashboard(),
    queryFn: async (): Promise<DashboardStats> => {
      // 这里假设后端有一个统计API，如果没有可以并发调用多个API
      try {
        const response = await apiService.getDashboardStats();
        return response.data.data;
      } catch (error) {
        // 如果没有专门的统计API，可以并发获取各种数据
        const [
          emailsResponse,
          foldersResponse,
        ] = await Promise.all([
          apiService.getEmails({ limit: 1 }), // 只获取总数
          apiService.getFolders(),
        ]);

        const emails = emailsResponse.data.data;
        const folders = foldersResponse.data.data;

        // 计算统计数据
        const totalEmails = emails.total || 0;
        const unreadEmails = emails.data.filter((email: any) => !email.isRead).length;
        const starredEmails = emails.data.filter((email: any) => email.isStarred).length;

        return {
          totalEmails,
          unreadEmails,
          starredEmails,
          sentEmails: 0, // 需要从发件箱获取
          draftEmails: 0, // 需要从草稿箱获取
          recentActivity: [], // 需要额外的API
          folderStats: folders.map((folder: any) => ({
            folderId: folder.id,
            folderName: folder.name,
            emailCount: folder.emailCount || 0,
            unreadCount: folder.unreadCount || 0,
          })),
        };
      }
    },
    ...queryOptions.realtime, // 统计数据需要实时更新
  });
};

// 获取邮件数量统计
export const useEmailCounts = () => {
  return useQuery({
    queryKey: queryKeys.stats.emailCounts(),
    queryFn: async (): Promise<EmailCounts> => {
      try {
        const response = await apiService.getEmailCounts();
        return response.data.data;
      } catch (error) {
        // 如果没有专门的统计API，可以并发获取各种数据
        const [
          allEmails,
          sentEmails,
          draftEmails,
          trashEmails,
        ] = await Promise.all([
          apiService.getEmails({ limit: 1000 }), // 获取所有邮件进行统计
          apiService.getEmails({ folderId: 'sent', limit: 1000 }),
          apiService.getEmails({ folderId: 'draft', limit: 1000 }),
          apiService.getEmails({ folderId: 'trash', limit: 1000 }),
        ]);

        const allEmailsData = allEmails.data.data.data;
        const unreadCount = allEmailsData.filter((email: any) => !email.isRead).length;
        const starredCount = allEmailsData.filter((email: any) => email.isStarred).length;

        return {
          total: allEmailsData.length,
          unread: unreadCount,
          starred: starredCount,
          sent: sentEmails.data.data.data.length,
          draft: draftEmails.data.data.data.length,
          trash: trashEmails.data.data.data.length,
        };
      }
    },
    ...queryOptions.realtime,
  });
};

// 获取文件夹统计信息
export const useFolderEmailCounts = (folderId: string, enabled = true) => {
  return useQuery({
    queryKey: [...queryKeys.folders.stats(folderId), 'emailCounts'],
    queryFn: async () => {
      const response = await apiService.getEmails({ 
        folderId, 
        limit: 1000 // 获取所有邮件进行统计
      });
      
      const emails = response.data.data.data;
      const unreadCount = emails.filter((email: any) => !email.isRead).length;
      const starredCount = emails.filter((email: any) => email.isStarred).length;

      return {
        total: emails.length,
        unread: unreadCount,
        starred: starredCount,
      };
    },
    enabled: enabled && !!folderId,
    ...queryOptions.realtime,
  });
};

// 获取最近活动统计
export const useRecentActivity = (days = 7) => {
  return useQuery({
    queryKey: [...queryKeys.stats.all, 'recentActivity', days],
    queryFn: async () => {
      try {
        const response = await apiService.getRecentActivity(days);
        return response.data.data;
      } catch (error) {
        // 如果没有专门的API，返回模拟数据
        const now = new Date();
        const activity = [];
        
        for (let i = days - 1; i >= 0; i--) {
          const date = new Date(now);
          date.setDate(date.getDate() - i);
          
          activity.push({
            date: date.toISOString().split('T')[0],
            count: Math.floor(Math.random() * 20), // 模拟数据
          });
        }
        
        return activity;
      }
    },
    ...queryOptions.shortTerm,
  });
};

// 获取发送统计
export const useSendingStats = (period: 'week' | 'month' | 'year' = 'week') => {
  return useQuery({
    queryKey: [...queryKeys.stats.all, 'sending', period],
    queryFn: async () => {
      try {
        const response = await apiService.getSendingStats(period);
        return response.data.data;
      } catch (error) {
        // 模拟数据
        return {
          period,
          totalSent: Math.floor(Math.random() * 100),
          averagePerDay: Math.floor(Math.random() * 10),
          peakDay: 'Monday',
          trend: 'up', // 'up' | 'down' | 'stable'
        };
      }
    },
    ...queryOptions.shortTerm,
  });
};

// 获取存储使用情况
export const useStorageStats = () => {
  return useQuery({
    queryKey: [...queryKeys.stats.all, 'storage'],
    queryFn: async () => {
      try {
        const response = await apiService.getStorageStats();
        return response.data.data;
      } catch (error) {
        // 模拟数据
        return {
          used: Math.floor(Math.random() * 1000), // MB
          total: 5000, // MB
          percentage: Math.floor(Math.random() * 100),
          breakdown: {
            emails: Math.floor(Math.random() * 500),
            attachments: Math.floor(Math.random() * 400),
            other: Math.floor(Math.random() * 100),
          },
        };
      }
    },
    ...queryOptions.longTerm, // 存储信息变化较慢
  });
};

// 获取热门联系人统计
export const useTopContacts = (limit = 10) => {
  return useQuery({
    queryKey: [...queryKeys.stats.all, 'topContacts', limit],
    queryFn: async () => {
      try {
        const response = await apiService.getTopContacts(limit);
        return response.data.data;
      } catch (error) {
        // 从邮件数据中计算热门联系人
        const emailsResponse = await apiService.getEmails({ limit: 1000 });
        const emails = emailsResponse.data.data.data;
        
        const contactCounts: Record<string, { email: string; name: string; count: number }> = {};
        
        emails.forEach((email: any) => {
          const senderEmail = email.sender.email;
          const senderName = email.sender.name || senderEmail;
          
          if (contactCounts[senderEmail]) {
            contactCounts[senderEmail].count++;
          } else {
            contactCounts[senderEmail] = {
              email: senderEmail,
              name: senderName,
              count: 1,
            };
          }
        });
        
        return Object.values(contactCounts)
          .sort((a, b) => b.count - a.count)
          .slice(0, limit);
      }
    },
    ...queryOptions.shortTerm,
  });
};
