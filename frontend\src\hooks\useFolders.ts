import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { apiService } from '../services/api';
import { queryKeys, queryOptions, getRelatedQueryKeys } from '../lib/queryKeys';
import { showErrorMessage } from '../utils/errorHandler';
import { message } from 'antd';
import type { Folder } from '../types';

// 获取文件夹列表
export const useFolders = () => {
  return useQuery({
    queryKey: queryKeys.folders.lists(),
    queryFn: async () => {
      const response = await apiService.getFolders();
      return response.data.data;
    },
    ...queryOptions.longTerm, // 文件夹变化较少，使用长期缓存
  });
};

// 获取单个文件夹详情
export const useFolder = (folderId: string, enabled = true) => {
  return useQuery({
    queryKey: queryKeys.folders.detail(folderId),
    queryFn: async () => {
      const response = await apiService.getFolder(folderId);
      return response.data.data;
    },
    enabled: enabled && !!folderId,
    ...queryOptions.shortTerm,
  });
};

// 获取文件夹统计信息
export const useFolderStats = (folderId: string, enabled = true) => {
  return useQuery({
    queryKey: queryKeys.folders.stats(folderId),
    queryFn: async () => {
      const response = await apiService.getFolderStats(folderId);
      return response.data.data;
    },
    enabled: enabled && !!folderId,
    ...queryOptions.realtime, // 统计信息需要实时更新
  });
};

// 创建文件夹
export const useCreateFolder = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (folderData: Partial<Folder>) => {
      const response = await apiService.createFolder(folderData);
      return response.data.data;
    },
    onSuccess: (newFolder) => {
      // 乐观更新文件夹列表
      queryClient.setQueryData(
        queryKeys.folders.lists(),
        (oldFolders: Folder[] | undefined) => {
          if (!oldFolders) return [newFolder];
          return [...oldFolders, newFolder];
        }
      );

      // 失效相关查询
      getRelatedQueryKeys.onFolderChange(newFolder.id).forEach(key => {
        queryClient.invalidateQueries({ queryKey: key });
      });

      message.success('文件夹创建成功');
    },
    onError: (error) => {
      showErrorMessage(error, '创建文件夹失败');
    },
  });
};

// 更新文件夹
export const useUpdateFolder = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ id, updates }: { id: string; updates: Partial<Folder> }) => {
      const response = await apiService.updateFolder(id, updates);
      return response.data.data;
    },
    onMutate: async ({ id, updates }) => {
      // 乐观更新
      await queryClient.cancelQueries({ queryKey: queryKeys.folders.detail(id) });
      
      const previousFolder = queryClient.getQueryData(queryKeys.folders.detail(id));
      
      if (previousFolder) {
        queryClient.setQueryData(queryKeys.folders.detail(id), {
          ...previousFolder,
          ...updates,
        });
      }

      // 更新文件夹列表
      queryClient.setQueryData(
        queryKeys.folders.lists(),
        (oldFolders: Folder[] | undefined) => {
          if (!oldFolders) return oldFolders;
          return oldFolders.map(folder =>
            folder.id === id ? { ...folder, ...updates } : folder
          );
        }
      );

      return { previousFolder };
    },
    onError: (error, { id }, context) => {
      // 回滚乐观更新
      if (context?.previousFolder) {
        queryClient.setQueryData(queryKeys.folders.detail(id), context.previousFolder);
      }
      showErrorMessage(error, '更新文件夹失败');
    },
    onSettled: (data, error, { id }) => {
      // 重新获取数据确保一致性
      queryClient.invalidateQueries({ queryKey: queryKeys.folders.detail(id) });
      queryClient.invalidateQueries({ queryKey: queryKeys.folders.lists() });
    },
    onSuccess: () => {
      message.success('文件夹更新成功');
    },
  });
};

// 删除文件夹
export const useDeleteFolder = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (folderId: string) => {
      await apiService.deleteFolder(folderId);
      return folderId;
    },
    onMutate: async (folderId) => {
      // 乐观更新 - 从列表中移除文件夹
      queryClient.setQueryData(
        queryKeys.folders.lists(),
        (oldFolders: Folder[] | undefined) => {
          if (!oldFolders) return oldFolders;
          return oldFolders.filter(folder => folder.id !== folderId);
        }
      );

      return { folderId };
    },
    onSuccess: (folderId) => {
      // 移除相关缓存
      queryClient.removeQueries({ queryKey: queryKeys.folders.detail(folderId) });
      queryClient.removeQueries({ queryKey: queryKeys.folders.stats(folderId) });
      
      // 失效相关查询
      getRelatedQueryKeys.onFolderChange(folderId).forEach(key => {
        queryClient.invalidateQueries({ queryKey: key });
      });

      message.success('文件夹删除成功');
    },
    onError: (error, folderId, context) => {
      // 重新获取数据
      queryClient.invalidateQueries({ queryKey: queryKeys.folders.lists() });
      showErrorMessage(error, '删除文件夹失败');
    },
  });
};

// 移动邮件到文件夹
export const useMoveEmailToFolder = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ 
      emailId, 
      targetFolderId 
    }: { 
      emailId: string; 
      targetFolderId: string; 
    }) => {
      const response = await apiService.updateEmail(emailId, { folderId: targetFolderId });
      return response.data.data;
    },
    onSuccess: (updatedEmail) => {
      // 失效相关查询
      getRelatedQueryKeys.onEmailChange(updatedEmail.id, updatedEmail.folderId).forEach(key => {
        queryClient.invalidateQueries({ queryKey: key });
      });

      message.success('邮件移动成功');
    },
    onError: (error) => {
      showErrorMessage(error, '移动邮件失败');
    },
  });
};

// 批量移动邮件到文件夹
export const useBatchMoveEmailsToFolder = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ 
      emailIds, 
      targetFolderId 
    }: { 
      emailIds: string[]; 
      targetFolderId: string; 
    }) => {
      const promises = emailIds.map(id => 
        apiService.updateEmail(id, { folderId: targetFolderId })
      );
      const responses = await Promise.all(promises);
      return responses.map(response => response.data.data);
    },
    onSuccess: (updatedEmails) => {
      // 失效相关查询
      getRelatedQueryKeys.onEmailChange().forEach(key => {
        queryClient.invalidateQueries({ queryKey: key });
      });

      message.success(`成功移动 ${updatedEmails.length} 封邮件`);
    },
    onError: (error) => {
      showErrorMessage(error, '批量移动邮件失败');
    },
  });
};

// 预取文件夹详情
export const usePrefetchFolder = () => {
  const queryClient = useQueryClient();

  return (folderId: string) => {
    queryClient.prefetchQuery({
      queryKey: queryKeys.folders.detail(folderId),
      queryFn: async () => {
        const response = await apiService.getFolder(folderId);
        return response.data.data;
      },
      ...queryOptions.shortTerm,
    });
  };
};

// 预取文件夹统计信息
export const usePrefetchFolderStats = () => {
  const queryClient = useQueryClient();

  return (folderId: string) => {
    queryClient.prefetchQuery({
      queryKey: queryKeys.folders.stats(folderId),
      queryFn: async () => {
        const response = await apiService.getFolderStats(folderId);
        return response.data.data;
      },
      ...queryOptions.realtime,
    });
  };
};
