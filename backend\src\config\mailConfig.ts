/**
 * 邮件服务配置
 */

import { MailConfig } from '../services/realMailService';

export const getMailConfig = (): MailConfig => {
  return {
    smtp: {
      host: process.env.SMTP_HOST || 'localhost',
      port: parseInt(process.env.SMTP_PORT || '587'),
      secure: process.env.SMTP_SECURE === 'true',
      auth: {
        user: process.env.SMTP_USER || '',
        pass: process.env.SMTP_PASS || '',
      },
    },
    imap: {
      host: process.env.IMAP_HOST || 'localhost',
      port: parseInt(process.env.IMAP_PORT || '993'),
      secure: process.env.IMAP_SECURE !== 'false',
      auth: {
        user: process.env.IMAP_USER || '',
        pass: process.env.IMAP_PASS || '',
      },
    },
    database: {
      host: process.env.DB_HOST || 'localhost',
      port: parseInt(process.env.DB_PORT || '3306'),
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '',
      database: process.env.DB_NAME || 'email_system',
    },
  };
};

// 邮件服务单例
let mailServiceInstance: any = null;

export const getMailService = async () => {
  if (!mailServiceInstance) {
    const { RealMailService } = await import('../services/realMailService');
    mailServiceInstance = new RealMailService(getMailConfig());
  }
  return mailServiceInstance;
};
