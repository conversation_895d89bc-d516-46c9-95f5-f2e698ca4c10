-- 邮件系统数据库设计
-- 支持 Postfix + Dovecot + MySQL 架构

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- 创建数据库
CREATE DATABASE IF NOT EXISTS `email_system` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE `email_system`;

-- ================================
-- 1. 用户管理表
-- ================================

-- 用户基础信息表
CREATE TABLE `users` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `username` varchar(255) NOT NULL COMMENT '用户名',
  `email` varchar(255) NOT NULL COMMENT '邮箱地址',
  `password_hash` varchar(255) NOT NULL COMMENT '密码哈希',
  `display_name` varchar(255) DEFAULT NULL COMMENT '显示名称',
  `avatar_url` varchar(500) DEFAULT NULL COMMENT '头像URL',
  `status` enum('active','inactive','suspended') DEFAULT 'active' COMMENT '用户状态',
  `email_verified` tinyint(1) DEFAULT 0 COMMENT '邮箱是否验证',
  `email_verified_at` timestamp NULL DEFAULT NULL COMMENT '邮箱验证时间',
  `last_login_at` timestamp NULL DEFAULT NULL COMMENT '最后登录时间',
  `last_login_ip` varchar(45) DEFAULT NULL COMMENT '最后登录IP',
  `settings` json DEFAULT NULL COMMENT '用户设置',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_username` (`username`),
  UNIQUE KEY `uk_email` (`email`),
  KEY `idx_status` (`status`),
  KEY `idx_email_verified` (`email_verified`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户基础信息表';

-- 邮件域名表
CREATE TABLE `mail_domains` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `domain` varchar(255) NOT NULL COMMENT '域名',
  `description` varchar(500) DEFAULT NULL COMMENT '域名描述',
  `status` enum('active','inactive') DEFAULT 'active' COMMENT '域名状态',
  `max_users` int(11) DEFAULT 0 COMMENT '最大用户数，0表示无限制',
  `current_users` int(11) DEFAULT 0 COMMENT '当前用户数',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_domain` (`domain`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='邮件域名表';

-- 邮箱账户表 (Postfix/Dovecot 使用)
CREATE TABLE `mail_accounts` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint(20) unsigned NOT NULL COMMENT '用户ID',
  `domain_id` bigint(20) unsigned NOT NULL COMMENT '域名ID',
  `email` varchar(255) NOT NULL COMMENT '完整邮箱地址',
  `password` varchar(255) NOT NULL COMMENT 'Dovecot密码哈希',
  `quota` bigint(20) DEFAULT 0 COMMENT '邮箱配额(字节)，0表示无限制',
  `used_quota` bigint(20) DEFAULT 0 COMMENT '已使用配额(字节)',
  `status` enum('active','inactive') DEFAULT 'active' COMMENT '账户状态',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_email` (`email`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_domain_id` (`domain_id`),
  KEY `idx_status` (`status`),
  CONSTRAINT `fk_mail_accounts_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_mail_accounts_domain` FOREIGN KEY (`domain_id`) REFERENCES `mail_domains` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='邮箱账户表';

-- ================================
-- 2. 邮件文件夹管理
-- ================================

-- 邮件文件夹表
CREATE TABLE `mail_folders` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint(20) unsigned NOT NULL COMMENT '用户ID',
  `name` varchar(255) NOT NULL COMMENT '文件夹名称',
  `display_name` varchar(255) NOT NULL COMMENT '显示名称',
  `type` enum('inbox','sent','drafts','trash','spam','custom') NOT NULL COMMENT '文件夹类型',
  `parent_id` bigint(20) unsigned DEFAULT NULL COMMENT '父文件夹ID',
  `sort_order` int(11) DEFAULT 0 COMMENT '排序顺序',
  `email_count` int(11) DEFAULT 0 COMMENT '邮件数量',
  `unread_count` int(11) DEFAULT 0 COMMENT '未读邮件数量',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_name` (`user_id`, `name`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_type` (`type`),
  CONSTRAINT `fk_mail_folders_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_mail_folders_parent` FOREIGN KEY (`parent_id`) REFERENCES `mail_folders` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='邮件文件夹表';

-- ================================
-- 3. 邮件存储表
-- ================================

-- 邮件主表
CREATE TABLE `emails` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `message_id` varchar(255) NOT NULL COMMENT '邮件Message-ID',
  `thread_id` varchar(255) DEFAULT NULL COMMENT '会话线程ID',
  `subject` varchar(500) DEFAULT NULL COMMENT '邮件主题',
  `from_address` varchar(255) NOT NULL COMMENT '发件人地址',
  `from_name` varchar(255) DEFAULT NULL COMMENT '发件人姓名',
  `reply_to` varchar(255) DEFAULT NULL COMMENT '回复地址',
  `to_addresses` text COMMENT '收件人地址列表(JSON)',
  `cc_addresses` text COMMENT '抄送地址列表(JSON)',
  `bcc_addresses` text COMMENT '密送地址列表(JSON)',
  `date_sent` timestamp NULL DEFAULT NULL COMMENT '发送时间',
  `date_received` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '接收时间',
  `size` int(11) DEFAULT 0 COMMENT '邮件大小(字节)',
  `has_attachments` tinyint(1) DEFAULT 0 COMMENT '是否有附件',
  `attachment_count` int(11) DEFAULT 0 COMMENT '附件数量',
  `priority` enum('low','normal','high') DEFAULT 'normal' COMMENT '优先级',
  `content_type` varchar(100) DEFAULT 'text/plain' COMMENT '内容类型',
  `charset` varchar(50) DEFAULT 'utf-8' COMMENT '字符编码',
  `raw_headers` text COMMENT '原始邮件头',
  `body_text` longtext COMMENT '纯文本内容',
  `body_html` longtext COMMENT 'HTML内容',
  `spam_score` decimal(5,2) DEFAULT 0.00 COMMENT '垃圾邮件评分',
  `is_spam` tinyint(1) DEFAULT 0 COMMENT '是否为垃圾邮件',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_message_id` (`message_id`),
  KEY `idx_thread_id` (`thread_id`),
  KEY `idx_from_address` (`from_address`),
  KEY `idx_date_sent` (`date_sent`),
  KEY `idx_date_received` (`date_received`),
  KEY `idx_subject` (`subject`(100)),
  KEY `idx_has_attachments` (`has_attachments`),
  KEY `idx_is_spam` (`is_spam`),
  FULLTEXT KEY `ft_subject_body` (`subject`, `body_text`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='邮件主表';

-- 用户邮件关联表 (一封邮件可能属于多个用户)
CREATE TABLE `user_emails` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint(20) unsigned NOT NULL COMMENT '用户ID',
  `email_id` bigint(20) unsigned NOT NULL COMMENT '邮件ID',
  `folder_id` bigint(20) unsigned NOT NULL COMMENT '文件夹ID',
  `is_read` tinyint(1) DEFAULT 0 COMMENT '是否已读',
  `is_starred` tinyint(1) DEFAULT 0 COMMENT '是否标星',
  `is_flagged` tinyint(1) DEFAULT 0 COMMENT '是否标记',
  `is_deleted` tinyint(1) DEFAULT 0 COMMENT '是否删除',
  `labels` json DEFAULT NULL COMMENT '标签列表',
  `read_at` timestamp NULL DEFAULT NULL COMMENT '阅读时间',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_email` (`user_id`, `email_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_email_id` (`email_id`),
  KEY `idx_folder_id` (`folder_id`),
  KEY `idx_is_read` (`is_read`),
  KEY `idx_is_starred` (`is_starred`),
  KEY `idx_is_deleted` (`is_deleted`),
  CONSTRAINT `fk_user_emails_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_user_emails_email` FOREIGN KEY (`email_id`) REFERENCES `emails` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_user_emails_folder` FOREIGN KEY (`folder_id`) REFERENCES `mail_folders` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户邮件关联表';

-- ================================
-- 4. 附件管理表
-- ================================

-- 邮件附件表
CREATE TABLE `email_attachments` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `email_id` bigint(20) unsigned NOT NULL COMMENT '邮件ID',
  `filename` varchar(255) NOT NULL COMMENT '文件名',
  `original_filename` varchar(255) NOT NULL COMMENT '原始文件名',
  `content_type` varchar(100) NOT NULL COMMENT '文件类型',
  `size` int(11) NOT NULL COMMENT '文件大小(字节)',
  `file_path` varchar(500) NOT NULL COMMENT '文件存储路径',
  `file_hash` varchar(64) DEFAULT NULL COMMENT '文件哈希值',
  `is_inline` tinyint(1) DEFAULT 0 COMMENT '是否为内联附件',
  `content_id` varchar(255) DEFAULT NULL COMMENT '内容ID(用于内联)',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_email_id` (`email_id`),
  KEY `idx_filename` (`filename`),
  KEY `idx_content_type` (`content_type`),
  KEY `idx_file_hash` (`file_hash`),
  CONSTRAINT `fk_email_attachments_email` FOREIGN KEY (`email_id`) REFERENCES `emails` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='邮件附件表';

-- ================================
-- 5. 联系人管理表
-- ================================

-- 联系人表
CREATE TABLE `contacts` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint(20) unsigned NOT NULL COMMENT '用户ID',
  `name` varchar(255) NOT NULL COMMENT '联系人姓名',
  `email` varchar(255) NOT NULL COMMENT '邮箱地址',
  `phone` varchar(50) DEFAULT NULL COMMENT '电话号码',
  `company` varchar(255) DEFAULT NULL COMMENT '公司',
  `notes` text COMMENT '备注',
  `avatar_url` varchar(500) DEFAULT NULL COMMENT '头像URL',
  `is_favorite` tinyint(1) DEFAULT 0 COMMENT '是否收藏',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_email` (`user_id`, `email`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_name` (`name`),
  KEY `idx_email` (`email`),
  KEY `idx_is_favorite` (`is_favorite`),
  CONSTRAINT `fk_contacts_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='联系人表';

-- ================================
-- 6. Postfix 相关表
-- ================================

-- 邮件别名表
CREATE TABLE `mail_aliases` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `domain_id` bigint(20) unsigned NOT NULL COMMENT '域名ID',
  `source` varchar(255) NOT NULL COMMENT '别名地址',
  `destination` text NOT NULL COMMENT '目标地址列表',
  `status` enum('active','inactive') DEFAULT 'active' COMMENT '状态',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_source` (`source`),
  KEY `idx_domain_id` (`domain_id`),
  KEY `idx_status` (`status`),
  CONSTRAINT `fk_mail_aliases_domain` FOREIGN KEY (`domain_id`) REFERENCES `mail_domains` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='邮件别名表';

-- 邮件传输表 (Postfix transport)
CREATE TABLE `mail_transports` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `domain` varchar(255) NOT NULL COMMENT '域名',
  `transport` varchar(255) NOT NULL COMMENT '传输方式',
  `status` enum('active','inactive') DEFAULT 'active' COMMENT '状态',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_domain` (`domain`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='邮件传输表';

-- ================================
-- 7. 系统日志表
-- ================================

-- 邮件发送日志表
CREATE TABLE `mail_send_logs` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint(20) unsigned DEFAULT NULL COMMENT '用户ID',
  `from_address` varchar(255) NOT NULL COMMENT '发件人',
  `to_address` varchar(255) NOT NULL COMMENT '收件人',
  `subject` varchar(500) DEFAULT NULL COMMENT '主题',
  `status` enum('pending','sent','failed','bounced') DEFAULT 'pending' COMMENT '发送状态',
  `error_message` text COMMENT '错误信息',
  `smtp_response` text COMMENT 'SMTP响应',
  `sent_at` timestamp NULL DEFAULT NULL COMMENT '发送时间',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_from_address` (`from_address`),
  KEY `idx_to_address` (`to_address`),
  KEY `idx_status` (`status`),
  KEY `idx_sent_at` (`sent_at`),
  CONSTRAINT `fk_mail_send_logs_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='邮件发送日志表';

-- 系统操作日志表
CREATE TABLE `system_logs` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint(20) unsigned DEFAULT NULL COMMENT '用户ID',
  `action` varchar(100) NOT NULL COMMENT '操作类型',
  `resource_type` varchar(50) DEFAULT NULL COMMENT '资源类型',
  `resource_id` bigint(20) unsigned DEFAULT NULL COMMENT '资源ID',
  `description` varchar(500) DEFAULT NULL COMMENT '操作描述',
  `ip_address` varchar(45) DEFAULT NULL COMMENT 'IP地址',
  `user_agent` varchar(500) DEFAULT NULL COMMENT '用户代理',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_action` (`action`),
  KEY `idx_resource` (`resource_type`, `resource_id`),
  KEY `idx_created_at` (`created_at`),
  CONSTRAINT `fk_system_logs_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统操作日志表';

-- ================================
-- 8. 视图定义
-- ================================

-- 用户邮箱统计视图
CREATE VIEW `v_user_mailbox_stats` AS
SELECT
    u.id as user_id,
    u.username,
    u.email,
    ma.email as mailbox_email,
    ma.quota,
    ma.used_quota,
    ROUND((ma.used_quota / ma.quota) * 100, 2) as quota_usage_percent,
    (SELECT COUNT(*) FROM user_emails ue JOIN mail_folders mf ON ue.folder_id = mf.id
     WHERE mf.user_id = u.id AND ue.is_deleted = 0) as total_emails,
    (SELECT COUNT(*) FROM user_emails ue JOIN mail_folders mf ON ue.folder_id = mf.id
     WHERE mf.user_id = u.id AND ue.is_read = 0 AND ue.is_deleted = 0) as unread_emails
FROM users u
LEFT JOIN mail_accounts ma ON u.id = ma.user_id
WHERE u.status = 'active' AND ma.status = 'active';

-- 邮件文件夹统计视图
CREATE VIEW `v_folder_stats` AS
SELECT
    mf.id as folder_id,
    mf.user_id,
    mf.name,
    mf.display_name,
    mf.type,
    COUNT(ue.id) as email_count,
    SUM(CASE WHEN ue.is_read = 0 THEN 1 ELSE 0 END) as unread_count,
    SUM(CASE WHEN ue.is_starred = 1 THEN 1 ELSE 0 END) as starred_count
FROM mail_folders mf
LEFT JOIN user_emails ue ON mf.id = ue.folder_id AND ue.is_deleted = 0
GROUP BY mf.id, mf.user_id, mf.name, mf.display_name, mf.type;

-- ================================
-- 9. 初始化数据
-- ================================

-- 插入默认域名
INSERT INTO `mail_domains` (`domain`, `description`, `status`) VALUES
('localhost', '本地测试域名', 'active'),
('example.com', '示例域名', 'active');

-- 插入默认管理员用户
INSERT INTO `users` (`username`, `email`, `password_hash`, `display_name`, `status`, `email_verified`) VALUES
('admin', 'admin@localhost', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj3bp.Gm.F5e', '系统管理员', 'active', 1);

-- 获取插入的用户ID和域名ID
SET @admin_user_id = LAST_INSERT_ID();
SET @localhost_domain_id = (SELECT id FROM mail_domains WHERE domain = 'localhost');

-- 为管理员创建邮箱账户
INSERT INTO `mail_accounts` (`user_id`, `domain_id`, `email`, `password`, `quota`, `status`) VALUES
(@admin_user_id, @localhost_domain_id, 'admin@localhost', '{PLAIN}admin123', **********, 'active');

-- 为管理员创建默认文件夹
INSERT INTO `mail_folders` (`user_id`, `name`, `display_name`, `type`, `sort_order`) VALUES
(@admin_user_id, 'INBOX', '收件箱', 'inbox', 1),
(@admin_user_id, 'Sent', '已发送', 'sent', 2),
(@admin_user_id, 'Drafts', '草稿箱', 'drafts', 3),
(@admin_user_id, 'Trash', '垃圾箱', 'trash', 4),
(@admin_user_id, 'Spam', '垃圾邮件', 'spam', 5);

-- ================================
-- 10. 索引优化
-- ================================

-- 为大表添加分区 (可选，适用于大量数据)
-- ALTER TABLE emails PARTITION BY RANGE (YEAR(date_received)) (
--     PARTITION p2023 VALUES LESS THAN (2024),
--     PARTITION p2024 VALUES LESS THAN (2025),
--     PARTITION p2025 VALUES LESS THAN (2026),
--     PARTITION p_future VALUES LESS THAN MAXVALUE
-- );

-- ================================
-- 11. 存储过程
-- ================================

DELIMITER $$

-- 创建用户邮箱的存储过程
CREATE PROCEDURE `CreateUserMailbox`(
    IN p_username VARCHAR(255),
    IN p_email VARCHAR(255),
    IN p_password VARCHAR(255),
    IN p_display_name VARCHAR(255),
    IN p_domain VARCHAR(255),
    IN p_quota BIGINT
)
BEGIN
    DECLARE v_user_id BIGINT;
    DECLARE v_domain_id BIGINT;
    DECLARE v_mail_password VARCHAR(255);

    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        RESIGNAL;
    END;

    START TRANSACTION;

    -- 获取域名ID
    SELECT id INTO v_domain_id FROM mail_domains WHERE domain = p_domain AND status = 'active';
    IF v_domain_id IS NULL THEN
        SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = '域名不存在或未激活';
    END IF;

    -- 创建用户
    INSERT INTO users (username, email, password_hash, display_name, status, email_verified)
    VALUES (p_username, p_email, p_password, p_display_name, 'active', 1);

    SET v_user_id = LAST_INSERT_ID();

    -- 生成Dovecot密码哈希
    SET v_mail_password = CONCAT('{PLAIN}', SUBSTRING(p_password, 1, 50));

    -- 创建邮箱账户
    INSERT INTO mail_accounts (user_id, domain_id, email, password, quota, status)
    VALUES (v_user_id, v_domain_id, p_email, v_mail_password, p_quota, 'active');

    -- 创建默认文件夹
    INSERT INTO mail_folders (user_id, name, display_name, type, sort_order) VALUES
    (v_user_id, 'INBOX', '收件箱', 'inbox', 1),
    (v_user_id, 'Sent', '已发送', 'sent', 2),
    (v_user_id, 'Drafts', '草稿箱', 'drafts', 3),
    (v_user_id, 'Trash', '垃圾箱', 'trash', 4),
    (v_user_id, 'Spam', '垃圾邮件', 'spam', 5);

    COMMIT;

    SELECT v_user_id as user_id, '用户邮箱创建成功' as message;
END$$

-- 更新文件夹统计的存储过程
CREATE PROCEDURE `UpdateFolderStats`(IN p_folder_id BIGINT)
BEGIN
    UPDATE mail_folders
    SET
        email_count = (
            SELECT COUNT(*)
            FROM user_emails
            WHERE folder_id = p_folder_id AND is_deleted = 0
        ),
        unread_count = (
            SELECT COUNT(*)
            FROM user_emails
            WHERE folder_id = p_folder_id AND is_read = 0 AND is_deleted = 0
        )
    WHERE id = p_folder_id;
END$$

-- 清理已删除邮件的存储过程
CREATE PROCEDURE `CleanupDeletedEmails`(IN p_days_old INT)
BEGIN
    DECLARE v_cutoff_date TIMESTAMP;

    SET v_cutoff_date = DATE_SUB(NOW(), INTERVAL p_days_old DAY);

    -- 删除标记为删除且超过指定天数的邮件关联
    DELETE FROM user_emails
    WHERE is_deleted = 1 AND updated_at < v_cutoff_date;

    -- 删除没有任何用户关联的邮件
    DELETE e FROM emails e
    LEFT JOIN user_emails ue ON e.id = ue.email_id
    WHERE ue.email_id IS NULL;

    SELECT ROW_COUNT() as deleted_count, '清理完成' as message;
END$$

DELIMITER ;

-- ================================
-- 12. 触发器
-- ================================

DELIMITER $$

-- 用户邮件状态更新触发器
CREATE TRIGGER `tr_user_emails_after_update`
AFTER UPDATE ON `user_emails`
FOR EACH ROW
BEGIN
    -- 更新文件夹统计
    CALL UpdateFolderStats(NEW.folder_id);

    -- 如果文件夹发生变化，也更新旧文件夹统计
    IF OLD.folder_id != NEW.folder_id THEN
        CALL UpdateFolderStats(OLD.folder_id);
    END IF;
END$$

-- 用户邮件插入触发器
CREATE TRIGGER `tr_user_emails_after_insert`
AFTER INSERT ON `user_emails`
FOR EACH ROW
BEGIN
    CALL UpdateFolderStats(NEW.folder_id);
END$$

-- 用户邮件删除触发器
CREATE TRIGGER `tr_user_emails_after_delete`
AFTER DELETE ON `user_emails`
FOR EACH ROW
BEGIN
    CALL UpdateFolderStats(OLD.folder_id);
END$$

DELIMITER ;

SET FOREIGN_KEY_CHECKS = 1;
