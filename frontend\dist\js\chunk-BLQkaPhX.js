import{R as t}from"./chunk-CkllRWJx.js";const e=t=>{let e;const n=new Set,s=(t,s)=>{const c="function"==typeof t?t(e):t;if(!Object.is(c,e)){const t=e;e=(null!=s?s:"object"!=typeof c||null===c)?c:Object.assign({},e,c),n.forEach((n=>n(e,t)))}},c=()=>e,o={setState:s,getState:c,getInitialState:()=>a,subscribe:t=>(n.add(t),()=>n.delete(t))},a=e=t(s,c,o);return o},n=t=>t;const s=s=>{const c=(t=>t?e(t):e)(s),o=e=>function(e,s=n){const c=t.useSyncExternalStore(e.subscribe,(()=>s(e.getState())),(()=>s(e.getInitialState())));return t.useDebugValue(c),c}(c,e);return Object.assign(o,c),o},c=t=>s;export{c};
