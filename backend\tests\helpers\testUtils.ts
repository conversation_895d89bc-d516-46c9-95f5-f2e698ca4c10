import request from 'supertest';
import app from '../../src/app';
import { memoryDB } from '../../src/database/memoryStore';
import { generateTokens } from '../../src/middleware/auth';
import { User } from '../../src/types';

export class TestHelper {
  static app = app;

  // 创建测试用户并返回认证token
  static async createTestUser(userData?: Partial<User>): Promise<{ user: User; token: string }> {
    const defaultUserData = {
      email: `test${Date.now()}@example.com`,
      username: `testuser${Date.now()}`,
      displayName: '测试用户',
      passwordHash: 'hashedpassword',
      isActive: true,
    };

    const user = await memoryDB.createUser({ ...defaultUserData, ...userData });

    // 为测试用户创建默认文件夹
    await this.createDefaultFoldersForUser(user.id);

    const { accessToken } = generateTokens({
      id: user.id,
      email: user.email,
      username: user.username,
    });

    return { user, token: accessToken };
  }

  // 为用户创建默认文件夹
  static async createDefaultFoldersForUser(userId: string): Promise<void> {
    const store = memoryDB.getStore();

    const folders = [
      {
        id: `inbox-${userId}`,
        userId,
        name: '收件箱',
        type: 'inbox' as const,
        emailCount: 0,
        unreadCount: 0,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        id: `sent-${userId}`,
        userId,
        name: '已发送',
        type: 'sent' as const,
        emailCount: 0,
        unreadCount: 0,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        id: `draft-${userId}`,
        userId,
        name: '草稿箱',
        type: 'draft' as const,
        emailCount: 0,
        unreadCount: 0,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        id: `trash-${userId}`,
        userId,
        name: '垃圾箱',
        type: 'trash' as const,
        emailCount: 0,
        unreadCount: 0,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    ];

    folders.forEach(folder => {
      store.folders.set(folder.id, folder);
    });
  }

  // 获取认证头
  static getAuthHeader(token: string): { Authorization: string } {
    return { Authorization: `Bearer ${token}` };
  }

  // 清理测试数据
  static async cleanupTestData(): Promise<void> {
    const store = memoryDB.getStore();
    
    // 清除除了默认用户外的所有用户
    for (const [id, user] of store.users.entries()) {
      if (user.email !== '<EMAIL>') {
        store.users.delete(id);
      }
    }

    // 清除测试邮件（保留示例邮件）
    for (const [id, email] of store.emails.entries()) {
      if (email.userId !== 'user-1') {
        store.emails.delete(id);
      }
    }
  }

  // 创建测试邮件
  static async createTestEmail(userId: string, emailData?: any) {
    const defaultEmailData = {
      messageId: `test-msg-${Date.now()}`,
      userId,
      folderId: emailData?.folderId || `inbox-${userId}`,
      subject: '测试邮件',
      senderEmail: '<EMAIL>',
      senderName: '发送者',
      recipients: ['<EMAIL>'],
      ccRecipients: [],
      bccRecipients: [],
      contentText: '这是一封测试邮件',
      contentHtml: '<p>这是一封测试邮件</p>',
      attachments: [],
      isRead: false,
      isStarred: false,
      isDeleted: false,
    };

    return await memoryDB.createEmail({ ...defaultEmailData, ...emailData });
  }

  // API请求助手
  static async makeRequest(method: 'get' | 'post' | 'put' | 'delete', path: string, token?: string, data?: any) {
    const req = request(this.app)[method](path);
    
    if (token) {
      req.set('Authorization', `Bearer ${token}`);
    }
    
    if (data) {
      req.send(data);
    }
    
    return req;
  }

  // 验证API响应格式
  static validateApiResponse(response: any, expectedStatus: number = 200) {
    expect(response.status).toBe(expectedStatus);
    expect(response.body).toHaveProperty('success');
    expect(response.body).toHaveProperty('data');
    expect(response.body).toHaveProperty('message');
  }

  // 验证错误响应格式
  static validateErrorResponse(response: any, expectedStatus: number) {
    expect(response.status).toBe(expectedStatus);
    expect(response.body).toHaveProperty('success', false);
    expect(response.body).toHaveProperty('error');
  }
}
