{"level":"info","message":"Created directory: C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\uploads","service":"email-system-backend","timestamp":"2025-06-12 21:18:37"}
{"environment":"development","level":"info","message":"🚀 邮箱系统后端服务启动成功","port":"3001","service":"email-system-backend","timestamp":"2025-06-12 21:18:37"}
{"level":"info","message":"📧 API文档地址: http://localhost:3001/health","service":"email-system-backend","timestamp":"2025-06-12 21:18:37"}
{"level":"info","message":"🌐 前端地址: http://localhost:5173","service":"email-system-backend","timestamp":"2025-06-12 21:18:37"}
{"duration":"3ms","ip":"::1","level":"info","message":"HTTP Request","method":"GET","service":"email-system-backend","status":200,"timestamp":"2025-06-12 21:18:54","url":"/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Request","method":"GET","service":"email-system-backend","status":404,"timestamp":"2025-06-12 21:18:54","url":"/favicon.ico","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"duration":"1ms","ip":"::1","level":"info","message":"HTTP Request","method":"GET","service":"email-system-backend","status":200,"timestamp":"2025-06-12 21:19:06","url":"/health","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.22621.4391"}
{"duration":"0ms","ip":"::1","level":"info","message":"HTTP Request","method":"GET","service":"email-system-backend","status":200,"timestamp":"2025-06-12 21:19:11","url":"/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"0ms","ip":"::1","level":"warn","message":"HTTP Request","method":"GET","service":"email-system-backend","status":404,"timestamp":"2025-06-12 21:19:12","url":"/favicon.ico","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"收到 SIGINT 信号，开始优雅关闭服务器...","service":"email-system-backend","timestamp":"2025-06-12 21:19:40"}
{"environment":"development","level":"info","message":"🚀 邮箱系统后端服务启动成功","port":"3001","service":"email-system-backend","timestamp":"2025-06-12 21:22:56"}
{"level":"info","message":"📧 API文档地址: http://localhost:3001/health","service":"email-system-backend","timestamp":"2025-06-12 21:22:56"}
{"level":"info","message":"🌐 前端地址: http://localhost:5173","service":"email-system-backend","timestamp":"2025-06-12 21:22:56"}
{"duration":"3ms","ip":"::1","level":"info","message":"HTTP Request","method":"GET","service":"email-system-backend","status":200,"timestamp":"2025-06-12 21:23:01","url":"/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"email":"<EMAIL>","level":"info","message":"User logged in successfully","service":"email-system-backend","timestamp":"2025-06-12 21:24:29","userId":"user-1"}
{"duration":"239ms","ip":"::1","level":"info","message":"HTTP Request","method":"POST","service":"email-system-backend","status":200,"timestamp":"2025-06-12 21:24:29","url":"/login","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.22621.4391"}
{"email":"<EMAIL>","level":"info","message":"User logged in successfully","service":"email-system-backend","timestamp":"2025-06-12 21:24:38","userId":"user-1"}
{"duration":"231ms","ip":"::1","level":"info","message":"HTTP Request","method":"POST","service":"email-system-backend","status":200,"timestamp":"2025-06-12 21:24:38","url":"/login","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.22621.4391"}
{"environment":"development","level":"info","message":"🚀 邮箱系统后端服务启动成功","port":"3001","service":"email-system-backend","timestamp":"2025-06-12 21:27:19"}
{"level":"info","message":"📧 API文档地址: http://localhost:3001/health","service":"email-system-backend","timestamp":"2025-06-12 21:27:19"}
{"level":"info","message":"🌐 前端地址: http://localhost:5173","service":"email-system-backend","timestamp":"2025-06-12 21:27:19"}
{"environment":"development","level":"info","message":"🚀 邮箱系统后端服务启动成功","port":"3001","service":"email-system-backend","timestamp":"2025-06-12 21:30:10"}
{"level":"info","message":"📧 API文档地址: http://localhost:3001/health","service":"email-system-backend","timestamp":"2025-06-12 21:30:10"}
{"level":"info","message":"🌐 前端地址: http://localhost:5173","service":"email-system-backend","timestamp":"2025-06-12 21:30:10"}
{"environment":"development","level":"info","message":"🚀 邮箱系统后端服务启动成功","port":"3001","service":"email-system-backend","timestamp":"2025-06-12 21:30:31"}
{"level":"info","message":"📧 API文档地址: http://localhost:3001/health","service":"email-system-backend","timestamp":"2025-06-12 21:30:31"}
{"level":"info","message":"🌐 前端地址: http://localhost:5173","service":"email-system-backend","timestamp":"2025-06-12 21:30:31"}
{"environment":"development","level":"info","message":"🚀 邮箱系统后端服务启动成功","port":"3001","service":"email-system-backend","timestamp":"2025-06-12 21:30:54"}
{"level":"info","message":"📧 API文档地址: http://localhost:3001/health","service":"email-system-backend","timestamp":"2025-06-12 21:30:54"}
{"level":"info","message":"🌐 前端地址: http://localhost:5173","service":"email-system-backend","timestamp":"2025-06-12 21:30:54"}
{"environment":"development","level":"info","message":"🚀 邮箱系统后端服务启动成功","port":"3001","service":"email-system-backend","timestamp":"2025-06-12 21:31:12"}
{"level":"info","message":"📧 API文档地址: http://localhost:3001/health","service":"email-system-backend","timestamp":"2025-06-12 21:31:12"}
{"level":"info","message":"🌐 前端地址: http://localhost:5173","service":"email-system-backend","timestamp":"2025-06-12 21:31:12"}
{"environment":"development","level":"info","message":"🚀 邮箱系统后端服务启动成功","port":"3001","service":"email-system-backend","timestamp":"2025-06-12 21:31:37"}
{"level":"info","message":"📧 API文档地址: http://localhost:3001/health","service":"email-system-backend","timestamp":"2025-06-12 21:31:37"}
{"level":"info","message":"🌐 前端地址: http://localhost:5173","service":"email-system-backend","timestamp":"2025-06-12 21:31:37"}
{"environment":"development","level":"info","message":"🚀 邮箱系统后端服务启动成功","port":"3001","service":"email-system-backend","timestamp":"2025-06-12 21:32:13"}
{"level":"info","message":"📧 API文档地址: http://localhost:3001/health","service":"email-system-backend","timestamp":"2025-06-12 21:32:13"}
{"level":"info","message":"🌐 前端地址: http://localhost:5173","service":"email-system-backend","timestamp":"2025-06-12 21:32:13"}
{"environment":"development","level":"info","message":"🚀 邮箱系统后端服务启动成功","port":"3001","service":"email-system-backend","timestamp":"2025-06-12 21:32:48"}
{"level":"info","message":"📧 API文档地址: http://localhost:3001/health","service":"email-system-backend","timestamp":"2025-06-12 21:32:48"}
{"level":"info","message":"🌐 前端地址: http://localhost:5173","service":"email-system-backend","timestamp":"2025-06-12 21:32:48"}
{"environment":"development","level":"info","message":"🚀 邮箱系统后端服务启动成功","port":"3001","service":"email-system-backend","timestamp":"2025-06-12 21:33:21"}
{"level":"info","message":"📧 API文档地址: http://localhost:3001/health","service":"email-system-backend","timestamp":"2025-06-12 21:33:21"}
{"level":"info","message":"🌐 前端地址: http://localhost:5173","service":"email-system-backend","timestamp":"2025-06-12 21:33:21"}
{"environment":"development","level":"info","message":"🚀 邮箱系统后端服务启动成功","port":"3001","service":"email-system-backend","timestamp":"2025-06-12 21:33:40"}
{"level":"info","message":"📧 API文档地址: http://localhost:3001/health","service":"email-system-backend","timestamp":"2025-06-12 21:33:40"}
{"level":"info","message":"🌐 前端地址: http://localhost:5173","service":"email-system-backend","timestamp":"2025-06-12 21:33:40"}
{"environment":"development","level":"info","message":"🚀 邮箱系统后端服务启动成功","port":"3001","service":"email-system-backend","timestamp":"2025-06-12 21:34:43"}
{"level":"info","message":"📧 API文档地址: http://localhost:3001/health","service":"email-system-backend","timestamp":"2025-06-12 21:34:43"}
{"level":"info","message":"🌐 前端地址: http://localhost:5173","service":"email-system-backend","timestamp":"2025-06-12 21:34:43"}
{"environment":"development","level":"info","message":"🚀 邮箱系统后端服务启动成功","port":"3001","service":"email-system-backend","timestamp":"2025-06-12 21:34:50"}
{"level":"info","message":"📧 API文档地址: http://localhost:3001/health","service":"email-system-backend","timestamp":"2025-06-12 21:34:50"}
{"level":"info","message":"🌐 前端地址: http://localhost:5173","service":"email-system-backend","timestamp":"2025-06-12 21:34:50"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 访问令牌缺失","method":"GET","service":"email-system-backend","stack":"Error: 访问令牌缺失\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:18:13)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at requestLogger (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\utils\\logger.ts:84:3)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at urlencodedParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\urlencoded.js:68:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at jsonParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\json.js:100:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\express-rate-limit\\dist\\index.cjs:822:7\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\express-rate-limit\\dist\\index.cjs:704:5","timestamp":"2025-06-12 21:35:03","url":"/api/folders"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 访问令牌缺失","method":"GET","service":"email-system-backend","stack":"Error: 访问令牌缺失\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:18:13)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at requestLogger (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\utils\\logger.ts:84:3)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at urlencodedParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\urlencoded.js:68:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at jsonParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\json.js:100:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\express-rate-limit\\dist\\index.cjs:822:7\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\express-rate-limit\\dist\\index.cjs:704:5","timestamp":"2025-06-12 21:35:03","url":"/api/emails"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 所有字段都是必填的","method":"POST","service":"email-system-backend","stack":"Error: 所有字段都是必填的\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\authController.ts:15:11\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\errorHandler.ts:66:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at requestLogger (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\utils\\logger.ts:84:3)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at urlencodedParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\urlencoded.js:57:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\read.js:132:5\n    at AsyncResource.runInAsyncScope (node:async_hooks:206:9)\n    at invokeCallback (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\raw-body\\index.js:238:16)\n    at done (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at endReadableNT (node:internal/streams/readable:1696:12)\n    at processTicksAndRejections (node:internal/process/task_queues:82:21)","timestamp":"2025-06-12 21:35:03","url":"/api/auth/register"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 邮件不存在","method":"GET","service":"email-system-backend","stack":"Error: 邮件不存在\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\emailController.ts:55:11\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 21:35:03","url":"/api/emails/non-existent-id"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 无权访问此文件夹","method":"GET","service":"email-system-backend","stack":"Error: 无权访问此文件夹\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\folderController.ts:53:11\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 21:35:03","url":"/api/folders/inbox"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 该邮箱已被注册","method":"POST","service":"email-system-backend","stack":"Error: 该邮箱已被注册\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\authController.ts:32:11\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 21:35:03","url":"/api/auth/register"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 邮箱格式无效","method":"POST","service":"email-system-backend","stack":"Error: 邮箱格式无效\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\authController.ts:21:11\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\errorHandler.ts:66:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at requestLogger (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\utils\\logger.ts:84:3)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at urlencodedParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\urlencoded.js:57:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\read.js:132:5\n    at AsyncResource.runInAsyncScope (node:async_hooks:206:9)\n    at invokeCallback (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\raw-body\\index.js:238:16)\n    at done (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at endReadableNT (node:internal/streams/readable:1696:12)\n    at processTicksAndRejections (node:internal/process/task_queues:82:21)","timestamp":"2025-06-12 21:35:03","url":"/api/auth/register"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 无权访问此文件夹","method":"GET","service":"email-system-backend","stack":"Error: 无权访问此文件夹\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\folderController.ts:53:11\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 21:35:03","url":"/api/folders/inbox"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 无权访问此邮件","method":"GET","service":"email-system-backend","stack":"Error: 无权访问此邮件\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\emailController.ts:60:11\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 21:35:03","url":"/api/emails/e3650618-2cb0-48a8-8d17-371a2a5258d9"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 文件夹不存在","method":"GET","service":"email-system-backend","stack":"Error: 文件夹不存在\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\folderController.ts:48:11\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 21:35:03","url":"/api/folders/non-existent-folder"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 无权访问此文件夹","method":"GET","service":"email-system-backend","stack":"Error: 无权访问此文件夹\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\folderController.ts:53:11\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 21:35:03","url":"/api/folders/inbox"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 访问令牌缺失","method":"GET","service":"email-system-backend","stack":"Error: 访问令牌缺失\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:18:13)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at requestLogger (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\utils\\logger.ts:84:3)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at urlencodedParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\urlencoded.js:68:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at jsonParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\json.js:100:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\express-rate-limit\\dist\\index.cjs:822:7\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\express-rate-limit\\dist\\index.cjs:704:5","timestamp":"2025-06-12 21:35:03","url":"/api/folders/inbox"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 访问令牌缺失","method":"POST","service":"email-system-backend","stack":"Error: 访问令牌缺失\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:18:13)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at requestLogger (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\utils\\logger.ts:84:3)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at urlencodedParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\urlencoded.js:57:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\read.js:132:5\n    at AsyncResource.runInAsyncScope (node:async_hooks:206:9)\n    at invokeCallback (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\raw-body\\index.js:238:16)\n    at done (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at endReadableNT (node:internal/streams/readable:1696:12)\n    at processTicksAndRejections (node:internal/process/task_queues:82:21)","timestamp":"2025-06-12 21:35:03","url":"/api/emails/search"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 无权访问此文件夹","method":"GET","service":"email-system-backend","stack":"Error: 无权访问此文件夹\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\folderController.ts:53:11\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 21:35:03","url":"/api/folders/inbox"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 无权访问此文件夹","method":"GET","service":"email-system-backend","stack":"Error: 无权访问此文件夹\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\folderController.ts:53:11\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 21:35:03","url":"/api/folders/inbox"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 请提供至少一个搜索条件","method":"POST","service":"email-system-backend","stack":"Error: 请提供至少一个搜索条件\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\emailController.ts:221:11\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\errorHandler.ts:66:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:41:5)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 21:35:03","url":"/api/emails/search"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 收件人不能为空","method":"POST","service":"email-system-backend","stack":"Error: 收件人不能为空\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\emailController.ts:88:11\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\errorHandler.ts:66:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:41:5)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 21:35:03","url":"/api/emails"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 无权访问此文件夹","method":"GET","service":"email-system-backend","stack":"Error: 无权访问此文件夹\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\folderController.ts:53:11\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 21:35:03","url":"/api/folders/trash"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 请提供至少一个搜索条件","method":"POST","service":"email-system-backend","stack":"Error: 请提供至少一个搜索条件\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\emailController.ts:221:11\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\errorHandler.ts:66:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:41:5)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 21:35:03","url":"/api/emails/search"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 邮件主题不能为空","method":"POST","service":"email-system-backend","stack":"Error: 邮件主题不能为空\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\emailController.ts:92:11\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\errorHandler.ts:66:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:41:5)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 21:35:03","url":"/api/emails"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 邮件内容不能为空","method":"POST","service":"email-system-backend","stack":"Error: 邮件内容不能为空\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\emailController.ts:96:11\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\errorHandler.ts:66:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:41:5)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 21:35:03","url":"/api/emails"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 无权访问此文件夹","method":"GET","service":"email-system-backend","stack":"Error: 无权访问此文件夹\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\folderController.ts:53:11\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 21:35:03","url":"/api/folders/inbox"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 无权访问此文件夹","method":"GET","service":"email-system-backend","stack":"Error: 无权访问此文件夹\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\folderController.ts:53:11\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 21:35:03","url":"/api/folders/trash"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 邮件不存在","method":"PUT","service":"email-system-backend","stack":"Error: 邮件不存在\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\emailController.ts:150:11\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 21:35:03","url":"/api/emails/non-existent-id"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 邮件不存在","method":"DELETE","service":"email-system-backend","stack":"Error: 邮件不存在\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\emailController.ts:189:11\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 21:35:03","url":"/api/emails/non-existent-id"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 无权删除此邮件","method":"DELETE","service":"email-system-backend","stack":"Error: 无权删除此邮件\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\emailController.ts:194:11\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 21:35:03","url":"/api/emails/a86cdc2d-9222-4cb4-9592-1d55ffa0989f"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 邮箱或密码错误","method":"POST","service":"email-system-backend","stack":"Error: 邮箱或密码错误\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\authController.ts:104:11\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 21:35:03","url":"/api/auth/login"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 邮箱或密码错误","method":"POST","service":"email-system-backend","stack":"Error: 邮箱或密码错误\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\authController.ts:115:11","timestamp":"2025-06-12 21:35:03","url":"/api/auth/login"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 邮箱和密码都是必填的","method":"POST","service":"email-system-backend","stack":"Error: 邮箱和密码都是必填的\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\authController.ts:98:11\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\errorHandler.ts:66:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at requestLogger (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\utils\\logger.ts:84:3)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at urlencodedParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\urlencoded.js:57:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\read.js:132:5\n    at AsyncResource.runInAsyncScope (node:async_hooks:206:9)\n    at invokeCallback (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\raw-body\\index.js:238:16)\n    at done (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at endReadableNT (node:internal/streams/readable:1696:12)\n    at processTicksAndRejections (node:internal/process/task_queues:82:21)","timestamp":"2025-06-12 21:35:03","url":"/api/auth/login"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 访问令牌缺失","method":"GET","service":"email-system-backend","stack":"Error: 访问令牌缺失\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:18:13)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at requestLogger (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\utils\\logger.ts:84:3)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at urlencodedParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\urlencoded.js:68:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at jsonParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\json.js:100:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\express-rate-limit\\dist\\index.cjs:822:7\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\express-rate-limit\\dist\\index.cjs:704:5","timestamp":"2025-06-12 21:35:03","url":"/api/auth/me"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 无效的访问令牌","method":"GET","service":"email-system-backend","stack":"Error: 无效的访问令牌\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:44:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at requestLogger (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\utils\\logger.ts:84:3)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at urlencodedParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\urlencoded.js:68:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at jsonParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\json.js:100:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\express-rate-limit\\dist\\index.cjs:822:7\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\express-rate-limit\\dist\\index.cjs:704:5","timestamp":"2025-06-12 21:35:03","url":"/api/auth/me"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 访问令牌缺失","method":"POST","service":"email-system-backend","stack":"Error: 访问令牌缺失\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:18:13)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at requestLogger (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\utils\\logger.ts:84:3)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at urlencodedParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\urlencoded.js:77:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at jsonParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\json.js:109:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\express-rate-limit\\dist\\index.cjs:822:7\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\express-rate-limit\\dist\\index.cjs:704:5","timestamp":"2025-06-12 21:35:03","url":"/api/auth/logout"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 刷新令牌缺失","method":"POST","service":"email-system-backend","stack":"Error: 刷新令牌缺失\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\authController.ts:188:11\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\errorHandler.ts:66:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at requestLogger (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\utils\\logger.ts:84:3)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at urlencodedParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\urlencoded.js:57:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\read.js:132:5\n    at AsyncResource.runInAsyncScope (node:async_hooks:206:9)\n    at invokeCallback (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\raw-body\\index.js:238:16)\n    at done (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at endReadableNT (node:internal/streams/readable:1696:12)\n    at processTicksAndRejections (node:internal/process/task_queues:82:21)","timestamp":"2025-06-12 21:35:03","url":"/api/auth/refresh"}
{"environment":"development","level":"info","message":"🚀 邮箱系统后端服务启动成功","port":"3001","service":"email-system-backend","timestamp":"2025-06-12 21:35:35"}
{"level":"info","message":"📧 API文档地址: http://localhost:3001/health","service":"email-system-backend","timestamp":"2025-06-12 21:35:35"}
{"level":"info","message":"🌐 前端地址: http://localhost:5173","service":"email-system-backend","timestamp":"2025-06-12 21:35:35"}
{"environment":"development","level":"info","message":"🚀 邮箱系统后端服务启动成功","port":"3001","service":"email-system-backend","timestamp":"2025-06-12 21:35:53"}
{"level":"info","message":"📧 API文档地址: http://localhost:3001/health","service":"email-system-backend","timestamp":"2025-06-12 21:35:53"}
{"level":"info","message":"🌐 前端地址: http://localhost:5173","service":"email-system-backend","timestamp":"2025-06-12 21:35:53"}
{"environment":"development","level":"info","message":"🚀 邮箱系统后端服务启动成功","port":"3001","service":"email-system-backend","timestamp":"2025-06-12 21:36:08"}
{"level":"info","message":"📧 API文档地址: http://localhost:3001/health","service":"email-system-backend","timestamp":"2025-06-12 21:36:08"}
{"level":"info","message":"🌐 前端地址: http://localhost:5173","service":"email-system-backend","timestamp":"2025-06-12 21:36:08"}
{"environment":"development","level":"info","message":"🚀 邮箱系统后端服务启动成功","port":"3001","service":"email-system-backend","timestamp":"2025-06-12 21:36:41"}
{"level":"info","message":"📧 API文档地址: http://localhost:3001/health","service":"email-system-backend","timestamp":"2025-06-12 21:36:41"}
{"level":"info","message":"🌐 前端地址: http://localhost:5173","service":"email-system-backend","timestamp":"2025-06-12 21:36:41"}
{"environment":"development","level":"info","message":"🚀 邮箱系统后端服务启动成功","port":"3001","service":"email-system-backend","timestamp":"2025-06-12 21:37:13"}
{"level":"info","message":"📧 API文档地址: http://localhost:3001/health","service":"email-system-backend","timestamp":"2025-06-12 21:37:13"}
{"level":"info","message":"🌐 前端地址: http://localhost:5173","service":"email-system-backend","timestamp":"2025-06-12 21:37:13"}
{"environment":"development","level":"info","message":"🚀 邮箱系统后端服务启动成功","port":"3001","service":"email-system-backend","timestamp":"2025-06-12 21:37:32"}
{"level":"info","message":"📧 API文档地址: http://localhost:3001/health","service":"email-system-backend","timestamp":"2025-06-12 21:37:32"}
{"level":"info","message":"🌐 前端地址: http://localhost:5173","service":"email-system-backend","timestamp":"2025-06-12 21:37:32"}
{"environment":"development","level":"info","message":"🚀 邮箱系统后端服务启动成功","port":"3001","service":"email-system-backend","timestamp":"2025-06-12 21:37:52"}
{"level":"info","message":"📧 API文档地址: http://localhost:3001/health","service":"email-system-backend","timestamp":"2025-06-12 21:37:52"}
{"level":"info","message":"🌐 前端地址: http://localhost:5173","service":"email-system-backend","timestamp":"2025-06-12 21:37:52"}
{"environment":"development","level":"info","message":"🚀 邮箱系统后端服务启动成功","port":"3001","service":"email-system-backend","timestamp":"2025-06-12 21:38:10"}
{"level":"info","message":"📧 API文档地址: http://localhost:3001/health","service":"email-system-backend","timestamp":"2025-06-12 21:38:10"}
{"level":"info","message":"🌐 前端地址: http://localhost:5173","service":"email-system-backend","timestamp":"2025-06-12 21:38:10"}
{"environment":"development","level":"info","message":"🚀 邮箱系统后端服务启动成功","port":"3001","service":"email-system-backend","timestamp":"2025-06-12 21:38:28"}
{"level":"info","message":"📧 API文档地址: http://localhost:3001/health","service":"email-system-backend","timestamp":"2025-06-12 21:38:28"}
{"level":"info","message":"🌐 前端地址: http://localhost:5173","service":"email-system-backend","timestamp":"2025-06-12 21:38:28"}
{"environment":"development","level":"info","message":"🚀 邮箱系统后端服务启动成功","port":"3001","service":"email-system-backend","timestamp":"2025-06-12 21:38:44"}
{"level":"info","message":"📧 API文档地址: http://localhost:3001/health","service":"email-system-backend","timestamp":"2025-06-12 21:38:44"}
{"level":"info","message":"🌐 前端地址: http://localhost:5173","service":"email-system-backend","timestamp":"2025-06-12 21:38:44"}
{"environment":"development","level":"info","message":"🚀 邮箱系统后端服务启动成功","port":"3001","service":"email-system-backend","timestamp":"2025-06-12 21:39:00"}
{"level":"info","message":"📧 API文档地址: http://localhost:3001/health","service":"email-system-backend","timestamp":"2025-06-12 21:39:00"}
{"level":"info","message":"🌐 前端地址: http://localhost:5173","service":"email-system-backend","timestamp":"2025-06-12 21:39:00"}
{"environment":"development","level":"info","message":"🚀 邮箱系统后端服务启动成功","port":"3001","service":"email-system-backend","timestamp":"2025-06-12 21:39:23"}
{"level":"info","message":"📧 API文档地址: http://localhost:3001/health","service":"email-system-backend","timestamp":"2025-06-12 21:39:23"}
{"level":"info","message":"🌐 前端地址: http://localhost:5173","service":"email-system-backend","timestamp":"2025-06-12 21:39:23"}
{"environment":"development","level":"info","message":"🚀 邮箱系统后端服务启动成功","port":"3001","service":"email-system-backend","timestamp":"2025-06-12 21:39:36"}
{"level":"info","message":"📧 API文档地址: http://localhost:3001/health","service":"email-system-backend","timestamp":"2025-06-12 21:39:36"}
{"level":"info","message":"🌐 前端地址: http://localhost:5173","service":"email-system-backend","timestamp":"2025-06-12 21:39:36"}
{"environment":"development","level":"info","message":"🚀 邮箱系统后端服务启动成功","port":"3001","service":"email-system-backend","timestamp":"2025-06-12 21:39:50"}
{"level":"info","message":"📧 API文档地址: http://localhost:3001/health","service":"email-system-backend","timestamp":"2025-06-12 21:39:50"}
{"level":"info","message":"🌐 前端地址: http://localhost:5173","service":"email-system-backend","timestamp":"2025-06-12 21:39:50"}
{"environment":"development","level":"info","message":"🚀 邮箱系统后端服务启动成功","port":"3001","service":"email-system-backend","timestamp":"2025-06-12 21:40:03"}
{"level":"info","message":"📧 API文档地址: http://localhost:3001/health","service":"email-system-backend","timestamp":"2025-06-12 21:40:03"}
{"level":"info","message":"🌐 前端地址: http://localhost:5173","service":"email-system-backend","timestamp":"2025-06-12 21:40:03"}
{"environment":"development","level":"info","message":"🚀 邮箱系统后端服务启动成功","port":"3001","service":"email-system-backend","timestamp":"2025-06-12 21:40:14"}
{"level":"info","message":"📧 API文档地址: http://localhost:3001/health","service":"email-system-backend","timestamp":"2025-06-12 21:40:14"}
{"level":"info","message":"🌐 前端地址: http://localhost:5173","service":"email-system-backend","timestamp":"2025-06-12 21:40:14"}
{"environment":"development","level":"info","message":"🚀 邮箱系统后端服务启动成功","port":"3001","service":"email-system-backend","timestamp":"2025-06-12 21:40:26"}
{"level":"info","message":"📧 API文档地址: http://localhost:3001/health","service":"email-system-backend","timestamp":"2025-06-12 21:40:26"}
{"level":"info","message":"🌐 前端地址: http://localhost:5173","service":"email-system-backend","timestamp":"2025-06-12 21:40:26"}
{"environment":"development","level":"info","message":"🚀 邮箱系统后端服务启动成功","port":"3001","service":"email-system-backend","timestamp":"2025-06-12 21:40:40"}
{"level":"info","message":"📧 API文档地址: http://localhost:3001/health","service":"email-system-backend","timestamp":"2025-06-12 21:40:40"}
{"level":"info","message":"🌐 前端地址: http://localhost:5173","service":"email-system-backend","timestamp":"2025-06-12 21:40:40"}
{"environment":"development","level":"info","message":"🚀 邮箱系统后端服务启动成功","port":"3001","service":"email-system-backend","timestamp":"2025-06-12 21:40:52"}
{"level":"info","message":"📧 API文档地址: http://localhost:3001/health","service":"email-system-backend","timestamp":"2025-06-12 21:40:52"}
{"level":"info","message":"🌐 前端地址: http://localhost:5173","service":"email-system-backend","timestamp":"2025-06-12 21:40:52"}
{"environment":"development","level":"info","message":"🚀 邮箱系统后端服务启动成功","port":"3001","service":"email-system-backend","timestamp":"2025-06-12 21:41:08"}
{"level":"info","message":"📧 API文档地址: http://localhost:3001/health","service":"email-system-backend","timestamp":"2025-06-12 21:41:08"}
{"level":"info","message":"🌐 前端地址: http://localhost:5173","service":"email-system-backend","timestamp":"2025-06-12 21:41:08"}
{"environment":"development","level":"info","message":"🚀 邮箱系统后端服务启动成功","port":"3001","service":"email-system-backend","timestamp":"2025-06-12 21:41:21"}
{"level":"info","message":"📧 API文档地址: http://localhost:3001/health","service":"email-system-backend","timestamp":"2025-06-12 21:41:21"}
{"level":"info","message":"🌐 前端地址: http://localhost:5173","service":"email-system-backend","timestamp":"2025-06-12 21:41:21"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 所有字段都是必填的","method":"POST","service":"email-system-backend","stack":"Error: 所有字段都是必填的\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\authController.ts:15:11\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\errorHandler.ts:66:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at requestLogger (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\utils\\logger.ts:84:3)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at urlencodedParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\urlencoded.js:57:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\read.js:132:5\n    at AsyncResource.runInAsyncScope (node:async_hooks:206:9)\n    at invokeCallback (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\raw-body\\index.js:238:16)\n    at done (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at endReadableNT (node:internal/streams/readable:1696:12)\n    at processTicksAndRejections (node:internal/process/task_queues:82:21)","timestamp":"2025-06-12 21:41:38","url":"/api/auth/register"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 该邮箱已被注册","method":"POST","service":"email-system-backend","stack":"Error: 该邮箱已被注册\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\authController.ts:32:11\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 21:41:38","url":"/api/auth/register"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 邮箱格式无效","method":"POST","service":"email-system-backend","stack":"Error: 邮箱格式无效\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\authController.ts:21:11\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\errorHandler.ts:66:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at requestLogger (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\utils\\logger.ts:84:3)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at urlencodedParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\urlencoded.js:57:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\read.js:132:5\n    at AsyncResource.runInAsyncScope (node:async_hooks:206:9)\n    at invokeCallback (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\raw-body\\index.js:238:16)\n    at done (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at endReadableNT (node:internal/streams/readable:1696:12)\n    at processTicksAndRejections (node:internal/process/task_queues:82:21)","timestamp":"2025-06-12 21:41:38","url":"/api/auth/register"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 访问令牌缺失","method":"GET","service":"email-system-backend","stack":"Error: 访问令牌缺失\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:18:13)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at requestLogger (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\utils\\logger.ts:84:3)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at urlencodedParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\urlencoded.js:68:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at jsonParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\json.js:100:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\express-rate-limit\\dist\\index.cjs:822:7\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\express-rate-limit\\dist\\index.cjs:704:5","timestamp":"2025-06-12 21:41:38","url":"/api/folders"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 访问令牌缺失","method":"POST","service":"email-system-backend","stack":"Error: 访问令牌缺失\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:18:13)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at requestLogger (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\utils\\logger.ts:84:3)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at urlencodedParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\urlencoded.js:57:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\read.js:132:5\n    at AsyncResource.runInAsyncScope (node:async_hooks:206:9)\n    at invokeCallback (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\raw-body\\index.js:238:16)\n    at done (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at endReadableNT (node:internal/streams/readable:1696:12)\n    at processTicksAndRejections (node:internal/process/task_queues:82:21)","timestamp":"2025-06-12 21:41:38","url":"/api/emails/search"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 文件夹不存在","method":"GET","service":"email-system-backend","stack":"Error: 文件夹不存在\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\folderController.ts:48:11\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 21:41:38","url":"/api/folders/non-existent-folder"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 请提供至少一个搜索条件","method":"POST","service":"email-system-backend","stack":"Error: 请提供至少一个搜索条件\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\emailController.ts:221:11\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\errorHandler.ts:66:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:41:5)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 21:41:38","url":"/api/emails/search"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 无权访问此文件夹","method":"GET","service":"email-system-backend","stack":"Error: 无权访问此文件夹\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\folderController.ts:53:11\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 21:41:38","url":"/api/folders/inbox-59c1565f-3de1-4b21-9d02-62ed453fd9cb"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 请提供至少一个搜索条件","method":"POST","service":"email-system-backend","stack":"Error: 请提供至少一个搜索条件\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\emailController.ts:221:11\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\errorHandler.ts:66:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:41:5)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 21:41:38","url":"/api/emails/search"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 访问令牌缺失","method":"GET","service":"email-system-backend","stack":"Error: 访问令牌缺失\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:18:13)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at requestLogger (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\utils\\logger.ts:84:3)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at urlencodedParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\urlencoded.js:68:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at jsonParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\json.js:100:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\express-rate-limit\\dist\\index.cjs:822:7\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\express-rate-limit\\dist\\index.cjs:704:5","timestamp":"2025-06-12 21:41:38","url":"/api/emails"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 访问令牌缺失","method":"GET","service":"email-system-backend","stack":"Error: 访问令牌缺失\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:18:13)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at requestLogger (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\utils\\logger.ts:84:3)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at urlencodedParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\urlencoded.js:68:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at jsonParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\json.js:100:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\express-rate-limit\\dist\\index.cjs:822:7\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\express-rate-limit\\dist\\index.cjs:704:5","timestamp":"2025-06-12 21:41:38","url":"/api/folders/inbox"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 邮件不存在","method":"GET","service":"email-system-backend","stack":"Error: 邮件不存在\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\emailController.ts:55:11\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 21:41:38","url":"/api/emails/non-existent-id"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 无权访问此邮件","method":"GET","service":"email-system-backend","stack":"Error: 无权访问此邮件\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\emailController.ts:60:11\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 21:41:38","url":"/api/emails/1161d2fd-3dcd-4dc1-ae80-4249a836c4e3"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 收件人不能为空","method":"POST","service":"email-system-backend","stack":"Error: 收件人不能为空\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\emailController.ts:88:11\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\errorHandler.ts:66:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:41:5)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 21:41:38","url":"/api/emails"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 邮件主题不能为空","method":"POST","service":"email-system-backend","stack":"Error: 邮件主题不能为空\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\emailController.ts:92:11\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\errorHandler.ts:66:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:41:5)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 21:41:38","url":"/api/emails"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 邮件内容不能为空","method":"POST","service":"email-system-backend","stack":"Error: 邮件内容不能为空\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\emailController.ts:96:11\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\errorHandler.ts:66:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:41:5)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 21:41:38","url":"/api/emails"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 邮件不存在","method":"PUT","service":"email-system-backend","stack":"Error: 邮件不存在\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\emailController.ts:150:11\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 21:41:38","url":"/api/emails/non-existent-id"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 邮件不存在","method":"DELETE","service":"email-system-backend","stack":"Error: 邮件不存在\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\emailController.ts:189:11\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 21:41:38","url":"/api/emails/non-existent-id"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 无权删除此邮件","method":"DELETE","service":"email-system-backend","stack":"Error: 无权删除此邮件\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\emailController.ts:194:11\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 21:41:38","url":"/api/emails/cfccf494-6556-4dae-8f95-c2b6e87404be"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 邮箱或密码错误","method":"POST","service":"email-system-backend","stack":"Error: 邮箱或密码错误\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\authController.ts:104:11\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 21:41:38","url":"/api/auth/login"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 邮箱或密码错误","method":"POST","service":"email-system-backend","stack":"Error: 邮箱或密码错误\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\authController.ts:115:11","timestamp":"2025-06-12 21:41:38","url":"/api/auth/login"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 邮箱和密码都是必填的","method":"POST","service":"email-system-backend","stack":"Error: 邮箱和密码都是必填的\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\authController.ts:98:11\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\errorHandler.ts:66:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at requestLogger (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\utils\\logger.ts:84:3)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at urlencodedParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\urlencoded.js:57:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\read.js:132:5\n    at AsyncResource.runInAsyncScope (node:async_hooks:206:9)\n    at invokeCallback (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\raw-body\\index.js:238:16)\n    at done (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at endReadableNT (node:internal/streams/readable:1696:12)\n    at processTicksAndRejections (node:internal/process/task_queues:82:21)","timestamp":"2025-06-12 21:41:38","url":"/api/auth/login"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 访问令牌缺失","method":"GET","service":"email-system-backend","stack":"Error: 访问令牌缺失\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:18:13)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at requestLogger (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\utils\\logger.ts:84:3)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at urlencodedParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\urlencoded.js:68:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at jsonParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\json.js:100:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\express-rate-limit\\dist\\index.cjs:822:7\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\express-rate-limit\\dist\\index.cjs:704:5","timestamp":"2025-06-12 21:41:38","url":"/api/auth/me"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 无效的访问令牌","method":"GET","service":"email-system-backend","stack":"Error: 无效的访问令牌\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:44:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at requestLogger (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\utils\\logger.ts:84:3)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at urlencodedParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\urlencoded.js:68:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at jsonParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\json.js:100:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\express-rate-limit\\dist\\index.cjs:822:7\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\express-rate-limit\\dist\\index.cjs:704:5","timestamp":"2025-06-12 21:41:38","url":"/api/auth/me"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 访问令牌缺失","method":"POST","service":"email-system-backend","stack":"Error: 访问令牌缺失\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:18:13)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at requestLogger (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\utils\\logger.ts:84:3)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at urlencodedParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\urlencoded.js:77:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at jsonParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\json.js:109:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\express-rate-limit\\dist\\index.cjs:822:7\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\express-rate-limit\\dist\\index.cjs:704:5","timestamp":"2025-06-12 21:41:38","url":"/api/auth/logout"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 刷新令牌缺失","method":"POST","service":"email-system-backend","stack":"Error: 刷新令牌缺失\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\authController.ts:188:11\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\errorHandler.ts:66:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at requestLogger (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\utils\\logger.ts:84:3)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at urlencodedParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\urlencoded.js:57:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\read.js:132:5\n    at AsyncResource.runInAsyncScope (node:async_hooks:206:9)\n    at invokeCallback (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\raw-body\\index.js:238:16)\n    at done (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at endReadableNT (node:internal/streams/readable:1696:12)\n    at processTicksAndRejections (node:internal/process/task_queues:82:21)","timestamp":"2025-06-12 21:41:38","url":"/api/auth/refresh"}
{"environment":"development","level":"info","message":"🚀 邮箱系统后端服务启动成功","port":"3001","service":"email-system-backend","timestamp":"2025-06-12 21:42:00"}
{"level":"info","message":"📧 API文档地址: http://localhost:3001/health","service":"email-system-backend","timestamp":"2025-06-12 21:42:00"}
{"level":"info","message":"🌐 前端地址: http://localhost:5173","service":"email-system-backend","timestamp":"2025-06-12 21:42:00"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 访问令牌缺失","method":"GET","service":"email-system-backend","stack":"Error: 访问令牌缺失\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:18:13)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at requestLogger (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\utils\\logger.ts:84:3)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at urlencodedParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\urlencoded.js:68:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at jsonParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\json.js:100:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\express-rate-limit\\dist\\index.cjs:822:7\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\express-rate-limit\\dist\\index.cjs:704:5","timestamp":"2025-06-12 21:42:20","url":"/api/emails"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 所有字段都是必填的","method":"POST","service":"email-system-backend","stack":"Error: 所有字段都是必填的\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\authController.ts:15:11\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\errorHandler.ts:66:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at requestLogger (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\utils\\logger.ts:84:3)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at urlencodedParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\urlencoded.js:57:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\read.js:132:5\n    at AsyncResource.runInAsyncScope (node:async_hooks:206:9)\n    at invokeCallback (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\raw-body\\index.js:238:16)\n    at done (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at endReadableNT (node:internal/streams/readable:1696:12)\n    at processTicksAndRejections (node:internal/process/task_queues:82:21)","timestamp":"2025-06-12 21:42:20","url":"/api/auth/register"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 访问令牌缺失","method":"GET","service":"email-system-backend","stack":"Error: 访问令牌缺失\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:18:13)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at requestLogger (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\utils\\logger.ts:84:3)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at urlencodedParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\urlencoded.js:68:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at jsonParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\json.js:100:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\express-rate-limit\\dist\\index.cjs:822:7\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\express-rate-limit\\dist\\index.cjs:704:5","timestamp":"2025-06-12 21:42:20","url":"/api/folders"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 该邮箱已被注册","method":"POST","service":"email-system-backend","stack":"Error: 该邮箱已被注册\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\authController.ts:32:11\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 21:42:20","url":"/api/auth/register"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 邮箱格式无效","method":"POST","service":"email-system-backend","stack":"Error: 邮箱格式无效\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\authController.ts:21:11\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\errorHandler.ts:66:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at requestLogger (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\utils\\logger.ts:84:3)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at urlencodedParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\urlencoded.js:57:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\read.js:132:5\n    at AsyncResource.runInAsyncScope (node:async_hooks:206:9)\n    at invokeCallback (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\raw-body\\index.js:238:16)\n    at done (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at endReadableNT (node:internal/streams/readable:1696:12)\n    at processTicksAndRejections (node:internal/process/task_queues:82:21)","timestamp":"2025-06-12 21:42:20","url":"/api/auth/register"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 邮件不存在","method":"GET","service":"email-system-backend","stack":"Error: 邮件不存在\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\emailController.ts:55:11\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 21:42:20","url":"/api/emails/non-existent-id"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 无权访问此邮件","method":"GET","service":"email-system-backend","stack":"Error: 无权访问此邮件\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\emailController.ts:60:11\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 21:42:20","url":"/api/emails/26b6e4ee-854f-4ef4-92ae-fc74808b56a3"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 文件夹不存在","method":"GET","service":"email-system-backend","stack":"Error: 文件夹不存在\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\folderController.ts:48:11\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 21:42:20","url":"/api/folders/non-existent-folder"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 无权访问此文件夹","method":"GET","service":"email-system-backend","stack":"Error: 无权访问此文件夹\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\folderController.ts:53:11\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 21:42:20","url":"/api/folders/inbox-0874fb0a-6311-4a98-91f6-769b3000cd9e"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 访问令牌缺失","method":"GET","service":"email-system-backend","stack":"Error: 访问令牌缺失\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:18:13)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at requestLogger (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\utils\\logger.ts:84:3)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at urlencodedParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\urlencoded.js:68:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at jsonParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\json.js:100:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\express-rate-limit\\dist\\index.cjs:822:7\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\express-rate-limit\\dist\\index.cjs:704:5","timestamp":"2025-06-12 21:42:20","url":"/api/folders/inbox"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 收件人不能为空","method":"POST","service":"email-system-backend","stack":"Error: 收件人不能为空\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\emailController.ts:88:11\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\errorHandler.ts:66:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:41:5)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 21:42:20","url":"/api/emails"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 邮件主题不能为空","method":"POST","service":"email-system-backend","stack":"Error: 邮件主题不能为空\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\emailController.ts:92:11\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\errorHandler.ts:66:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:41:5)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 21:42:20","url":"/api/emails"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 邮件内容不能为空","method":"POST","service":"email-system-backend","stack":"Error: 邮件内容不能为空\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\emailController.ts:96:11\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\errorHandler.ts:66:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:41:5)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 21:42:20","url":"/api/emails"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 邮件不存在","method":"PUT","service":"email-system-backend","stack":"Error: 邮件不存在\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\emailController.ts:150:11\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 21:42:20","url":"/api/emails/non-existent-id"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 邮件不存在","method":"DELETE","service":"email-system-backend","stack":"Error: 邮件不存在\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\emailController.ts:189:11\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 21:42:20","url":"/api/emails/non-existent-id"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 无权删除此邮件","method":"DELETE","service":"email-system-backend","stack":"Error: 无权删除此邮件\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\emailController.ts:194:11\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 21:42:20","url":"/api/emails/d3c01aec-1309-40ed-9f40-587e91478623"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 邮箱或密码错误","method":"POST","service":"email-system-backend","stack":"Error: 邮箱或密码错误\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\authController.ts:104:11\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 21:42:20","url":"/api/auth/login"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 访问令牌缺失","method":"POST","service":"email-system-backend","stack":"Error: 访问令牌缺失\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:18:13)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at requestLogger (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\utils\\logger.ts:84:3)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at urlencodedParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\urlencoded.js:57:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\read.js:132:5\n    at AsyncResource.runInAsyncScope (node:async_hooks:206:9)\n    at invokeCallback (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\raw-body\\index.js:238:16)\n    at done (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at endReadableNT (node:internal/streams/readable:1696:12)\n    at processTicksAndRejections (node:internal/process/task_queues:82:21)","timestamp":"2025-06-12 21:42:20","url":"/api/emails/search"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 邮箱或密码错误","method":"POST","service":"email-system-backend","stack":"Error: 邮箱或密码错误\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\authController.ts:115:11","timestamp":"2025-06-12 21:42:20","url":"/api/auth/login"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 邮箱和密码都是必填的","method":"POST","service":"email-system-backend","stack":"Error: 邮箱和密码都是必填的\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\authController.ts:98:11\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\errorHandler.ts:66:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at requestLogger (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\utils\\logger.ts:84:3)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at urlencodedParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\urlencoded.js:57:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\read.js:132:5\n    at AsyncResource.runInAsyncScope (node:async_hooks:206:9)\n    at invokeCallback (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\raw-body\\index.js:238:16)\n    at done (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at endReadableNT (node:internal/streams/readable:1696:12)\n    at processTicksAndRejections (node:internal/process/task_queues:82:21)","timestamp":"2025-06-12 21:42:20","url":"/api/auth/login"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 请提供至少一个搜索条件","method":"POST","service":"email-system-backend","stack":"Error: 请提供至少一个搜索条件\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\emailController.ts:221:11\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\errorHandler.ts:66:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:41:5)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 21:42:20","url":"/api/emails/search"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 请提供至少一个搜索条件","method":"POST","service":"email-system-backend","stack":"Error: 请提供至少一个搜索条件\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\emailController.ts:221:11\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\errorHandler.ts:66:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:41:5)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 21:42:20","url":"/api/emails/search"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 访问令牌缺失","method":"GET","service":"email-system-backend","stack":"Error: 访问令牌缺失\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:18:13)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at requestLogger (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\utils\\logger.ts:84:3)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at urlencodedParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\urlencoded.js:68:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at jsonParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\json.js:100:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\express-rate-limit\\dist\\index.cjs:822:7\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\express-rate-limit\\dist\\index.cjs:704:5","timestamp":"2025-06-12 21:42:20","url":"/api/auth/me"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 无效的访问令牌","method":"GET","service":"email-system-backend","stack":"Error: 无效的访问令牌\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:44:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at requestLogger (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\utils\\logger.ts:84:3)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at urlencodedParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\urlencoded.js:68:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at jsonParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\json.js:100:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\express-rate-limit\\dist\\index.cjs:822:7\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\express-rate-limit\\dist\\index.cjs:704:5","timestamp":"2025-06-12 21:42:20","url":"/api/auth/me"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 访问令牌缺失","method":"POST","service":"email-system-backend","stack":"Error: 访问令牌缺失\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:18:13)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at requestLogger (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\utils\\logger.ts:84:3)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at urlencodedParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\urlencoded.js:77:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at jsonParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\json.js:109:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\express-rate-limit\\dist\\index.cjs:822:7\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\express-rate-limit\\dist\\index.cjs:704:5","timestamp":"2025-06-12 21:42:20","url":"/api/auth/logout"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 刷新令牌缺失","method":"POST","service":"email-system-backend","stack":"Error: 刷新令牌缺失\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\authController.ts:188:11\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\errorHandler.ts:66:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at requestLogger (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\utils\\logger.ts:84:3)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at urlencodedParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\urlencoded.js:57:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\read.js:132:5\n    at AsyncResource.runInAsyncScope (node:async_hooks:206:9)\n    at invokeCallback (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\raw-body\\index.js:238:16)\n    at done (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at endReadableNT (node:internal/streams/readable:1696:12)\n    at processTicksAndRejections (node:internal/process/task_queues:82:21)","timestamp":"2025-06-12 21:42:20","url":"/api/auth/refresh"}
{"environment":"development","level":"info","message":"🚀 邮箱系统后端服务启动成功","port":"3001","service":"email-system-backend","timestamp":"2025-06-12 21:42:57"}
{"level":"info","message":"📧 API文档地址: http://localhost:3001/health","service":"email-system-backend","timestamp":"2025-06-12 21:42:57"}
{"level":"info","message":"🌐 前端地址: http://localhost:5173","service":"email-system-backend","timestamp":"2025-06-12 21:42:57"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 所有字段都是必填的","method":"POST","service":"email-system-backend","stack":"Error: 所有字段都是必填的\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\authController.ts:15:11\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\errorHandler.ts:66:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at requestLogger (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\utils\\logger.ts:84:3)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at urlencodedParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\urlencoded.js:57:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\read.js:132:5\n    at AsyncResource.runInAsyncScope (node:async_hooks:206:9)\n    at invokeCallback (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\raw-body\\index.js:238:16)\n    at done (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at endReadableNT (node:internal/streams/readable:1696:12)\n    at processTicksAndRejections (node:internal/process/task_queues:82:21)","timestamp":"2025-06-12 21:43:13","url":"/api/auth/register"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 访问令牌缺失","method":"GET","service":"email-system-backend","stack":"Error: 访问令牌缺失\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:18:13)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at requestLogger (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\utils\\logger.ts:84:3)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at urlencodedParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\urlencoded.js:68:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at jsonParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\json.js:100:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\express-rate-limit\\dist\\index.cjs:822:7\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\express-rate-limit\\dist\\index.cjs:704:5","timestamp":"2025-06-12 21:43:13","url":"/api/folders"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 该邮箱已被注册","method":"POST","service":"email-system-backend","stack":"Error: 该邮箱已被注册\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\authController.ts:32:11\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 21:43:13","url":"/api/auth/register"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 邮箱格式无效","method":"POST","service":"email-system-backend","stack":"Error: 邮箱格式无效\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\authController.ts:21:11\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\errorHandler.ts:66:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at requestLogger (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\utils\\logger.ts:84:3)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at urlencodedParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\urlencoded.js:57:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\read.js:132:5\n    at AsyncResource.runInAsyncScope (node:async_hooks:206:9)\n    at invokeCallback (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\raw-body\\index.js:238:16)\n    at done (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at endReadableNT (node:internal/streams/readable:1696:12)\n    at processTicksAndRejections (node:internal/process/task_queues:82:21)","timestamp":"2025-06-12 21:43:13","url":"/api/auth/register"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 访问令牌缺失","method":"GET","service":"email-system-backend","stack":"Error: 访问令牌缺失\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:18:13)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at requestLogger (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\utils\\logger.ts:84:3)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at urlencodedParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\urlencoded.js:68:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at jsonParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\json.js:100:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\express-rate-limit\\dist\\index.cjs:822:7\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\express-rate-limit\\dist\\index.cjs:704:5","timestamp":"2025-06-12 21:43:13","url":"/api/emails"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 邮件不存在","method":"GET","service":"email-system-backend","stack":"Error: 邮件不存在\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\emailController.ts:55:11\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 21:43:13","url":"/api/emails/non-existent-id"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 文件夹不存在","method":"GET","service":"email-system-backend","stack":"Error: 文件夹不存在\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\folderController.ts:48:11\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 21:43:13","url":"/api/folders/non-existent-folder"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 无权访问此邮件","method":"GET","service":"email-system-backend","stack":"Error: 无权访问此邮件\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\emailController.ts:60:11\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 21:43:13","url":"/api/emails/99b493d7-c241-44d8-9bdd-cd485e351ca0"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 无权访问此文件夹","method":"GET","service":"email-system-backend","stack":"Error: 无权访问此文件夹\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\folderController.ts:53:11\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 21:43:13","url":"/api/folders/inbox-7eb92a49-bd93-40d2-8237-3dc08dcbf321"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 访问令牌缺失","method":"GET","service":"email-system-backend","stack":"Error: 访问令牌缺失\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:18:13)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at requestLogger (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\utils\\logger.ts:84:3)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at urlencodedParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\urlencoded.js:68:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at jsonParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\json.js:100:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\express-rate-limit\\dist\\index.cjs:822:7\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\express-rate-limit\\dist\\index.cjs:704:5","timestamp":"2025-06-12 21:43:13","url":"/api/folders/inbox"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 收件人不能为空","method":"POST","service":"email-system-backend","stack":"Error: 收件人不能为空\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\emailController.ts:88:11\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\errorHandler.ts:66:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:41:5)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 21:43:13","url":"/api/emails"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 邮件主题不能为空","method":"POST","service":"email-system-backend","stack":"Error: 邮件主题不能为空\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\emailController.ts:92:11\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\errorHandler.ts:66:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:41:5)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 21:43:13","url":"/api/emails"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 邮件内容不能为空","method":"POST","service":"email-system-backend","stack":"Error: 邮件内容不能为空\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\emailController.ts:96:11\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\errorHandler.ts:66:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:41:5)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 21:43:13","url":"/api/emails"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 邮件不存在","method":"PUT","service":"email-system-backend","stack":"Error: 邮件不存在\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\emailController.ts:150:11\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 21:43:13","url":"/api/emails/non-existent-id"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 邮件不存在","method":"DELETE","service":"email-system-backend","stack":"Error: 邮件不存在\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\emailController.ts:189:11\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 21:43:13","url":"/api/emails/non-existent-id"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 无权删除此邮件","method":"DELETE","service":"email-system-backend","stack":"Error: 无权删除此邮件\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\emailController.ts:194:11\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 21:43:13","url":"/api/emails/ff29a614-8de7-4594-af9b-7dff754a54a2"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 邮箱或密码错误","method":"POST","service":"email-system-backend","stack":"Error: 邮箱或密码错误\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\authController.ts:104:11\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 21:43:13","url":"/api/auth/login"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 访问令牌缺失","method":"POST","service":"email-system-backend","stack":"Error: 访问令牌缺失\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:18:13)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at requestLogger (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\utils\\logger.ts:84:3)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at urlencodedParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\urlencoded.js:57:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\read.js:132:5\n    at AsyncResource.runInAsyncScope (node:async_hooks:206:9)\n    at invokeCallback (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\raw-body\\index.js:238:16)\n    at done (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at endReadableNT (node:internal/streams/readable:1696:12)\n    at processTicksAndRejections (node:internal/process/task_queues:82:21)","timestamp":"2025-06-12 21:43:13","url":"/api/emails/search"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 请提供至少一个搜索条件","method":"POST","service":"email-system-backend","stack":"Error: 请提供至少一个搜索条件\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\emailController.ts:221:11\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\errorHandler.ts:66:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:41:5)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 21:43:13","url":"/api/emails/search"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 请提供至少一个搜索条件","method":"POST","service":"email-system-backend","stack":"Error: 请提供至少一个搜索条件\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\emailController.ts:221:11\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\errorHandler.ts:66:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:41:5)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 21:43:13","url":"/api/emails/search"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 邮箱或密码错误","method":"POST","service":"email-system-backend","stack":"Error: 邮箱或密码错误\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\authController.ts:115:11","timestamp":"2025-06-12 21:43:13","url":"/api/auth/login"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 邮箱和密码都是必填的","method":"POST","service":"email-system-backend","stack":"Error: 邮箱和密码都是必填的\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\authController.ts:98:11\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\errorHandler.ts:66:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at requestLogger (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\utils\\logger.ts:84:3)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at urlencodedParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\urlencoded.js:57:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\read.js:132:5\n    at AsyncResource.runInAsyncScope (node:async_hooks:206:9)\n    at invokeCallback (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\raw-body\\index.js:238:16)\n    at done (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at endReadableNT (node:internal/streams/readable:1696:12)\n    at processTicksAndRejections (node:internal/process/task_queues:82:21)","timestamp":"2025-06-12 21:43:13","url":"/api/auth/login"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 访问令牌缺失","method":"GET","service":"email-system-backend","stack":"Error: 访问令牌缺失\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:18:13)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at requestLogger (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\utils\\logger.ts:84:3)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at urlencodedParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\urlencoded.js:68:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at jsonParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\json.js:100:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\express-rate-limit\\dist\\index.cjs:822:7\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\express-rate-limit\\dist\\index.cjs:704:5","timestamp":"2025-06-12 21:43:13","url":"/api/auth/me"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 无效的访问令牌","method":"GET","service":"email-system-backend","stack":"Error: 无效的访问令牌\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:44:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at requestLogger (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\utils\\logger.ts:84:3)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at urlencodedParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\urlencoded.js:68:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at jsonParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\json.js:100:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\express-rate-limit\\dist\\index.cjs:822:7\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\express-rate-limit\\dist\\index.cjs:704:5","timestamp":"2025-06-12 21:43:13","url":"/api/auth/me"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 访问令牌缺失","method":"POST","service":"email-system-backend","stack":"Error: 访问令牌缺失\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:18:13)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at requestLogger (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\utils\\logger.ts:84:3)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at urlencodedParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\urlencoded.js:77:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at jsonParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\json.js:109:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\express-rate-limit\\dist\\index.cjs:822:7\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\express-rate-limit\\dist\\index.cjs:704:5","timestamp":"2025-06-12 21:43:13","url":"/api/auth/logout"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 刷新令牌缺失","method":"POST","service":"email-system-backend","stack":"Error: 刷新令牌缺失\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\authController.ts:188:11\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\errorHandler.ts:66:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at requestLogger (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\utils\\logger.ts:84:3)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at urlencodedParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\urlencoded.js:57:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\read.js:132:5\n    at AsyncResource.runInAsyncScope (node:async_hooks:206:9)\n    at invokeCallback (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\raw-body\\index.js:238:16)\n    at done (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at endReadableNT (node:internal/streams/readable:1696:12)\n    at processTicksAndRejections (node:internal/process/task_queues:82:21)","timestamp":"2025-06-12 21:43:13","url":"/api/auth/refresh"}
