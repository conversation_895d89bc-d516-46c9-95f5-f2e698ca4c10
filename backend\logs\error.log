{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 访问令牌缺失","method":"GET","service":"email-system-backend","stack":"Error: 访问令牌缺失\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:18:13)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at requestLogger (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\utils\\logger.ts:84:3)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at urlencodedParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\urlencoded.js:68:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at jsonParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\json.js:100:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\express-rate-limit\\dist\\index.cjs:822:7\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\express-rate-limit\\dist\\index.cjs:704:5","timestamp":"2025-06-12 21:35:03","url":"/api/folders"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 访问令牌缺失","method":"GET","service":"email-system-backend","stack":"Error: 访问令牌缺失\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:18:13)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at requestLogger (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\utils\\logger.ts:84:3)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at urlencodedParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\urlencoded.js:68:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at jsonParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\json.js:100:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\express-rate-limit\\dist\\index.cjs:822:7\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\express-rate-limit\\dist\\index.cjs:704:5","timestamp":"2025-06-12 21:35:03","url":"/api/emails"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 所有字段都是必填的","method":"POST","service":"email-system-backend","stack":"Error: 所有字段都是必填的\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\authController.ts:15:11\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\errorHandler.ts:66:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at requestLogger (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\utils\\logger.ts:84:3)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at urlencodedParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\urlencoded.js:57:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\read.js:132:5\n    at AsyncResource.runInAsyncScope (node:async_hooks:206:9)\n    at invokeCallback (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\raw-body\\index.js:238:16)\n    at done (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at endReadableNT (node:internal/streams/readable:1696:12)\n    at processTicksAndRejections (node:internal/process/task_queues:82:21)","timestamp":"2025-06-12 21:35:03","url":"/api/auth/register"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 邮件不存在","method":"GET","service":"email-system-backend","stack":"Error: 邮件不存在\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\emailController.ts:55:11\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 21:35:03","url":"/api/emails/non-existent-id"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 无权访问此文件夹","method":"GET","service":"email-system-backend","stack":"Error: 无权访问此文件夹\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\folderController.ts:53:11\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 21:35:03","url":"/api/folders/inbox"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 该邮箱已被注册","method":"POST","service":"email-system-backend","stack":"Error: 该邮箱已被注册\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\authController.ts:32:11\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 21:35:03","url":"/api/auth/register"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 邮箱格式无效","method":"POST","service":"email-system-backend","stack":"Error: 邮箱格式无效\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\authController.ts:21:11\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\errorHandler.ts:66:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at requestLogger (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\utils\\logger.ts:84:3)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at urlencodedParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\urlencoded.js:57:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\read.js:132:5\n    at AsyncResource.runInAsyncScope (node:async_hooks:206:9)\n    at invokeCallback (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\raw-body\\index.js:238:16)\n    at done (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at endReadableNT (node:internal/streams/readable:1696:12)\n    at processTicksAndRejections (node:internal/process/task_queues:82:21)","timestamp":"2025-06-12 21:35:03","url":"/api/auth/register"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 无权访问此文件夹","method":"GET","service":"email-system-backend","stack":"Error: 无权访问此文件夹\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\folderController.ts:53:11\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 21:35:03","url":"/api/folders/inbox"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 无权访问此邮件","method":"GET","service":"email-system-backend","stack":"Error: 无权访问此邮件\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\emailController.ts:60:11\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 21:35:03","url":"/api/emails/e3650618-2cb0-48a8-8d17-371a2a5258d9"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 文件夹不存在","method":"GET","service":"email-system-backend","stack":"Error: 文件夹不存在\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\folderController.ts:48:11\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 21:35:03","url":"/api/folders/non-existent-folder"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 无权访问此文件夹","method":"GET","service":"email-system-backend","stack":"Error: 无权访问此文件夹\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\folderController.ts:53:11\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 21:35:03","url":"/api/folders/inbox"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 访问令牌缺失","method":"GET","service":"email-system-backend","stack":"Error: 访问令牌缺失\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:18:13)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at requestLogger (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\utils\\logger.ts:84:3)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at urlencodedParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\urlencoded.js:68:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at jsonParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\json.js:100:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\express-rate-limit\\dist\\index.cjs:822:7\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\express-rate-limit\\dist\\index.cjs:704:5","timestamp":"2025-06-12 21:35:03","url":"/api/folders/inbox"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 访问令牌缺失","method":"POST","service":"email-system-backend","stack":"Error: 访问令牌缺失\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:18:13)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at requestLogger (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\utils\\logger.ts:84:3)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at urlencodedParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\urlencoded.js:57:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\read.js:132:5\n    at AsyncResource.runInAsyncScope (node:async_hooks:206:9)\n    at invokeCallback (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\raw-body\\index.js:238:16)\n    at done (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at endReadableNT (node:internal/streams/readable:1696:12)\n    at processTicksAndRejections (node:internal/process/task_queues:82:21)","timestamp":"2025-06-12 21:35:03","url":"/api/emails/search"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 无权访问此文件夹","method":"GET","service":"email-system-backend","stack":"Error: 无权访问此文件夹\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\folderController.ts:53:11\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 21:35:03","url":"/api/folders/inbox"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 无权访问此文件夹","method":"GET","service":"email-system-backend","stack":"Error: 无权访问此文件夹\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\folderController.ts:53:11\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 21:35:03","url":"/api/folders/inbox"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 请提供至少一个搜索条件","method":"POST","service":"email-system-backend","stack":"Error: 请提供至少一个搜索条件\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\emailController.ts:221:11\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\errorHandler.ts:66:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:41:5)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 21:35:03","url":"/api/emails/search"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 收件人不能为空","method":"POST","service":"email-system-backend","stack":"Error: 收件人不能为空\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\emailController.ts:88:11\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\errorHandler.ts:66:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:41:5)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 21:35:03","url":"/api/emails"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 无权访问此文件夹","method":"GET","service":"email-system-backend","stack":"Error: 无权访问此文件夹\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\folderController.ts:53:11\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 21:35:03","url":"/api/folders/trash"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 请提供至少一个搜索条件","method":"POST","service":"email-system-backend","stack":"Error: 请提供至少一个搜索条件\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\emailController.ts:221:11\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\errorHandler.ts:66:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:41:5)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 21:35:03","url":"/api/emails/search"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 邮件主题不能为空","method":"POST","service":"email-system-backend","stack":"Error: 邮件主题不能为空\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\emailController.ts:92:11\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\errorHandler.ts:66:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:41:5)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 21:35:03","url":"/api/emails"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 邮件内容不能为空","method":"POST","service":"email-system-backend","stack":"Error: 邮件内容不能为空\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\emailController.ts:96:11\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\errorHandler.ts:66:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:41:5)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 21:35:03","url":"/api/emails"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 无权访问此文件夹","method":"GET","service":"email-system-backend","stack":"Error: 无权访问此文件夹\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\folderController.ts:53:11\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 21:35:03","url":"/api/folders/inbox"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 无权访问此文件夹","method":"GET","service":"email-system-backend","stack":"Error: 无权访问此文件夹\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\folderController.ts:53:11\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 21:35:03","url":"/api/folders/trash"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 邮件不存在","method":"PUT","service":"email-system-backend","stack":"Error: 邮件不存在\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\emailController.ts:150:11\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 21:35:03","url":"/api/emails/non-existent-id"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 邮件不存在","method":"DELETE","service":"email-system-backend","stack":"Error: 邮件不存在\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\emailController.ts:189:11\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 21:35:03","url":"/api/emails/non-existent-id"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 无权删除此邮件","method":"DELETE","service":"email-system-backend","stack":"Error: 无权删除此邮件\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\emailController.ts:194:11\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 21:35:03","url":"/api/emails/a86cdc2d-9222-4cb4-9592-1d55ffa0989f"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 邮箱或密码错误","method":"POST","service":"email-system-backend","stack":"Error: 邮箱或密码错误\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\authController.ts:104:11\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 21:35:03","url":"/api/auth/login"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 邮箱或密码错误","method":"POST","service":"email-system-backend","stack":"Error: 邮箱或密码错误\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\authController.ts:115:11","timestamp":"2025-06-12 21:35:03","url":"/api/auth/login"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 邮箱和密码都是必填的","method":"POST","service":"email-system-backend","stack":"Error: 邮箱和密码都是必填的\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\authController.ts:98:11\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\errorHandler.ts:66:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at requestLogger (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\utils\\logger.ts:84:3)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at urlencodedParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\urlencoded.js:57:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\read.js:132:5\n    at AsyncResource.runInAsyncScope (node:async_hooks:206:9)\n    at invokeCallback (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\raw-body\\index.js:238:16)\n    at done (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at endReadableNT (node:internal/streams/readable:1696:12)\n    at processTicksAndRejections (node:internal/process/task_queues:82:21)","timestamp":"2025-06-12 21:35:03","url":"/api/auth/login"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 访问令牌缺失","method":"GET","service":"email-system-backend","stack":"Error: 访问令牌缺失\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:18:13)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at requestLogger (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\utils\\logger.ts:84:3)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at urlencodedParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\urlencoded.js:68:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at jsonParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\json.js:100:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\express-rate-limit\\dist\\index.cjs:822:7\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\express-rate-limit\\dist\\index.cjs:704:5","timestamp":"2025-06-12 21:35:03","url":"/api/auth/me"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 无效的访问令牌","method":"GET","service":"email-system-backend","stack":"Error: 无效的访问令牌\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:44:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at requestLogger (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\utils\\logger.ts:84:3)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at urlencodedParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\urlencoded.js:68:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at jsonParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\json.js:100:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\express-rate-limit\\dist\\index.cjs:822:7\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\express-rate-limit\\dist\\index.cjs:704:5","timestamp":"2025-06-12 21:35:03","url":"/api/auth/me"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 访问令牌缺失","method":"POST","service":"email-system-backend","stack":"Error: 访问令牌缺失\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:18:13)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at requestLogger (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\utils\\logger.ts:84:3)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at urlencodedParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\urlencoded.js:77:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at jsonParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\json.js:109:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\express-rate-limit\\dist\\index.cjs:822:7\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\express-rate-limit\\dist\\index.cjs:704:5","timestamp":"2025-06-12 21:35:03","url":"/api/auth/logout"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 刷新令牌缺失","method":"POST","service":"email-system-backend","stack":"Error: 刷新令牌缺失\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\authController.ts:188:11\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\errorHandler.ts:66:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at requestLogger (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\utils\\logger.ts:84:3)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at urlencodedParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\urlencoded.js:57:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\read.js:132:5\n    at AsyncResource.runInAsyncScope (node:async_hooks:206:9)\n    at invokeCallback (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\raw-body\\index.js:238:16)\n    at done (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at endReadableNT (node:internal/streams/readable:1696:12)\n    at processTicksAndRejections (node:internal/process/task_queues:82:21)","timestamp":"2025-06-12 21:35:03","url":"/api/auth/refresh"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 所有字段都是必填的","method":"POST","service":"email-system-backend","stack":"Error: 所有字段都是必填的\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\authController.ts:15:11\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\errorHandler.ts:66:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at requestLogger (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\utils\\logger.ts:84:3)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at urlencodedParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\urlencoded.js:57:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\read.js:132:5\n    at AsyncResource.runInAsyncScope (node:async_hooks:206:9)\n    at invokeCallback (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\raw-body\\index.js:238:16)\n    at done (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at endReadableNT (node:internal/streams/readable:1696:12)\n    at processTicksAndRejections (node:internal/process/task_queues:82:21)","timestamp":"2025-06-12 21:41:38","url":"/api/auth/register"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 该邮箱已被注册","method":"POST","service":"email-system-backend","stack":"Error: 该邮箱已被注册\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\authController.ts:32:11\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 21:41:38","url":"/api/auth/register"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 邮箱格式无效","method":"POST","service":"email-system-backend","stack":"Error: 邮箱格式无效\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\authController.ts:21:11\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\errorHandler.ts:66:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at requestLogger (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\utils\\logger.ts:84:3)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at urlencodedParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\urlencoded.js:57:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\read.js:132:5\n    at AsyncResource.runInAsyncScope (node:async_hooks:206:9)\n    at invokeCallback (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\raw-body\\index.js:238:16)\n    at done (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at endReadableNT (node:internal/streams/readable:1696:12)\n    at processTicksAndRejections (node:internal/process/task_queues:82:21)","timestamp":"2025-06-12 21:41:38","url":"/api/auth/register"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 访问令牌缺失","method":"GET","service":"email-system-backend","stack":"Error: 访问令牌缺失\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:18:13)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at requestLogger (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\utils\\logger.ts:84:3)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at urlencodedParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\urlencoded.js:68:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at jsonParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\json.js:100:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\express-rate-limit\\dist\\index.cjs:822:7\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\express-rate-limit\\dist\\index.cjs:704:5","timestamp":"2025-06-12 21:41:38","url":"/api/folders"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 访问令牌缺失","method":"POST","service":"email-system-backend","stack":"Error: 访问令牌缺失\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:18:13)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at requestLogger (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\utils\\logger.ts:84:3)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at urlencodedParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\urlencoded.js:57:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\read.js:132:5\n    at AsyncResource.runInAsyncScope (node:async_hooks:206:9)\n    at invokeCallback (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\raw-body\\index.js:238:16)\n    at done (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at endReadableNT (node:internal/streams/readable:1696:12)\n    at processTicksAndRejections (node:internal/process/task_queues:82:21)","timestamp":"2025-06-12 21:41:38","url":"/api/emails/search"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 文件夹不存在","method":"GET","service":"email-system-backend","stack":"Error: 文件夹不存在\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\folderController.ts:48:11\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 21:41:38","url":"/api/folders/non-existent-folder"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 请提供至少一个搜索条件","method":"POST","service":"email-system-backend","stack":"Error: 请提供至少一个搜索条件\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\emailController.ts:221:11\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\errorHandler.ts:66:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:41:5)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 21:41:38","url":"/api/emails/search"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 无权访问此文件夹","method":"GET","service":"email-system-backend","stack":"Error: 无权访问此文件夹\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\folderController.ts:53:11\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 21:41:38","url":"/api/folders/inbox-59c1565f-3de1-4b21-9d02-62ed453fd9cb"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 请提供至少一个搜索条件","method":"POST","service":"email-system-backend","stack":"Error: 请提供至少一个搜索条件\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\emailController.ts:221:11\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\errorHandler.ts:66:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:41:5)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 21:41:38","url":"/api/emails/search"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 访问令牌缺失","method":"GET","service":"email-system-backend","stack":"Error: 访问令牌缺失\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:18:13)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at requestLogger (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\utils\\logger.ts:84:3)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at urlencodedParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\urlencoded.js:68:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at jsonParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\json.js:100:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\express-rate-limit\\dist\\index.cjs:822:7\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\express-rate-limit\\dist\\index.cjs:704:5","timestamp":"2025-06-12 21:41:38","url":"/api/emails"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 访问令牌缺失","method":"GET","service":"email-system-backend","stack":"Error: 访问令牌缺失\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:18:13)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at requestLogger (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\utils\\logger.ts:84:3)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at urlencodedParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\urlencoded.js:68:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at jsonParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\json.js:100:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\express-rate-limit\\dist\\index.cjs:822:7\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\express-rate-limit\\dist\\index.cjs:704:5","timestamp":"2025-06-12 21:41:38","url":"/api/folders/inbox"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 邮件不存在","method":"GET","service":"email-system-backend","stack":"Error: 邮件不存在\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\emailController.ts:55:11\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 21:41:38","url":"/api/emails/non-existent-id"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 无权访问此邮件","method":"GET","service":"email-system-backend","stack":"Error: 无权访问此邮件\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\emailController.ts:60:11\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 21:41:38","url":"/api/emails/1161d2fd-3dcd-4dc1-ae80-4249a836c4e3"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 收件人不能为空","method":"POST","service":"email-system-backend","stack":"Error: 收件人不能为空\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\emailController.ts:88:11\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\errorHandler.ts:66:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:41:5)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 21:41:38","url":"/api/emails"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 邮件主题不能为空","method":"POST","service":"email-system-backend","stack":"Error: 邮件主题不能为空\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\emailController.ts:92:11\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\errorHandler.ts:66:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:41:5)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 21:41:38","url":"/api/emails"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 邮件内容不能为空","method":"POST","service":"email-system-backend","stack":"Error: 邮件内容不能为空\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\emailController.ts:96:11\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\errorHandler.ts:66:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:41:5)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 21:41:38","url":"/api/emails"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 邮件不存在","method":"PUT","service":"email-system-backend","stack":"Error: 邮件不存在\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\emailController.ts:150:11\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 21:41:38","url":"/api/emails/non-existent-id"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 邮件不存在","method":"DELETE","service":"email-system-backend","stack":"Error: 邮件不存在\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\emailController.ts:189:11\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 21:41:38","url":"/api/emails/non-existent-id"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 无权删除此邮件","method":"DELETE","service":"email-system-backend","stack":"Error: 无权删除此邮件\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\emailController.ts:194:11\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 21:41:38","url":"/api/emails/cfccf494-6556-4dae-8f95-c2b6e87404be"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 邮箱或密码错误","method":"POST","service":"email-system-backend","stack":"Error: 邮箱或密码错误\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\authController.ts:104:11\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 21:41:38","url":"/api/auth/login"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 邮箱或密码错误","method":"POST","service":"email-system-backend","stack":"Error: 邮箱或密码错误\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\authController.ts:115:11","timestamp":"2025-06-12 21:41:38","url":"/api/auth/login"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 邮箱和密码都是必填的","method":"POST","service":"email-system-backend","stack":"Error: 邮箱和密码都是必填的\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\authController.ts:98:11\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\errorHandler.ts:66:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at requestLogger (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\utils\\logger.ts:84:3)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at urlencodedParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\urlencoded.js:57:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\read.js:132:5\n    at AsyncResource.runInAsyncScope (node:async_hooks:206:9)\n    at invokeCallback (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\raw-body\\index.js:238:16)\n    at done (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at endReadableNT (node:internal/streams/readable:1696:12)\n    at processTicksAndRejections (node:internal/process/task_queues:82:21)","timestamp":"2025-06-12 21:41:38","url":"/api/auth/login"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 访问令牌缺失","method":"GET","service":"email-system-backend","stack":"Error: 访问令牌缺失\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:18:13)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at requestLogger (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\utils\\logger.ts:84:3)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at urlencodedParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\urlencoded.js:68:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at jsonParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\json.js:100:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\express-rate-limit\\dist\\index.cjs:822:7\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\express-rate-limit\\dist\\index.cjs:704:5","timestamp":"2025-06-12 21:41:38","url":"/api/auth/me"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 无效的访问令牌","method":"GET","service":"email-system-backend","stack":"Error: 无效的访问令牌\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:44:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at requestLogger (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\utils\\logger.ts:84:3)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at urlencodedParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\urlencoded.js:68:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at jsonParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\json.js:100:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\express-rate-limit\\dist\\index.cjs:822:7\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\express-rate-limit\\dist\\index.cjs:704:5","timestamp":"2025-06-12 21:41:38","url":"/api/auth/me"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 访问令牌缺失","method":"POST","service":"email-system-backend","stack":"Error: 访问令牌缺失\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:18:13)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at requestLogger (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\utils\\logger.ts:84:3)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at urlencodedParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\urlencoded.js:77:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at jsonParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\json.js:109:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\express-rate-limit\\dist\\index.cjs:822:7\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\express-rate-limit\\dist\\index.cjs:704:5","timestamp":"2025-06-12 21:41:38","url":"/api/auth/logout"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 刷新令牌缺失","method":"POST","service":"email-system-backend","stack":"Error: 刷新令牌缺失\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\authController.ts:188:11\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\errorHandler.ts:66:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at requestLogger (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\utils\\logger.ts:84:3)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at urlencodedParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\urlencoded.js:57:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\read.js:132:5\n    at AsyncResource.runInAsyncScope (node:async_hooks:206:9)\n    at invokeCallback (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\raw-body\\index.js:238:16)\n    at done (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at endReadableNT (node:internal/streams/readable:1696:12)\n    at processTicksAndRejections (node:internal/process/task_queues:82:21)","timestamp":"2025-06-12 21:41:38","url":"/api/auth/refresh"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 访问令牌缺失","method":"GET","service":"email-system-backend","stack":"Error: 访问令牌缺失\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:18:13)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at requestLogger (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\utils\\logger.ts:84:3)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at urlencodedParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\urlencoded.js:68:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at jsonParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\json.js:100:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\express-rate-limit\\dist\\index.cjs:822:7\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\express-rate-limit\\dist\\index.cjs:704:5","timestamp":"2025-06-12 21:42:20","url":"/api/emails"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 所有字段都是必填的","method":"POST","service":"email-system-backend","stack":"Error: 所有字段都是必填的\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\authController.ts:15:11\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\errorHandler.ts:66:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at requestLogger (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\utils\\logger.ts:84:3)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at urlencodedParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\urlencoded.js:57:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\read.js:132:5\n    at AsyncResource.runInAsyncScope (node:async_hooks:206:9)\n    at invokeCallback (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\raw-body\\index.js:238:16)\n    at done (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at endReadableNT (node:internal/streams/readable:1696:12)\n    at processTicksAndRejections (node:internal/process/task_queues:82:21)","timestamp":"2025-06-12 21:42:20","url":"/api/auth/register"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 访问令牌缺失","method":"GET","service":"email-system-backend","stack":"Error: 访问令牌缺失\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:18:13)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at requestLogger (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\utils\\logger.ts:84:3)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at urlencodedParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\urlencoded.js:68:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at jsonParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\json.js:100:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\express-rate-limit\\dist\\index.cjs:822:7\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\express-rate-limit\\dist\\index.cjs:704:5","timestamp":"2025-06-12 21:42:20","url":"/api/folders"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 该邮箱已被注册","method":"POST","service":"email-system-backend","stack":"Error: 该邮箱已被注册\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\authController.ts:32:11\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 21:42:20","url":"/api/auth/register"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 邮箱格式无效","method":"POST","service":"email-system-backend","stack":"Error: 邮箱格式无效\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\authController.ts:21:11\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\errorHandler.ts:66:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at requestLogger (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\utils\\logger.ts:84:3)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at urlencodedParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\urlencoded.js:57:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\read.js:132:5\n    at AsyncResource.runInAsyncScope (node:async_hooks:206:9)\n    at invokeCallback (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\raw-body\\index.js:238:16)\n    at done (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at endReadableNT (node:internal/streams/readable:1696:12)\n    at processTicksAndRejections (node:internal/process/task_queues:82:21)","timestamp":"2025-06-12 21:42:20","url":"/api/auth/register"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 邮件不存在","method":"GET","service":"email-system-backend","stack":"Error: 邮件不存在\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\emailController.ts:55:11\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 21:42:20","url":"/api/emails/non-existent-id"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 无权访问此邮件","method":"GET","service":"email-system-backend","stack":"Error: 无权访问此邮件\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\emailController.ts:60:11\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 21:42:20","url":"/api/emails/26b6e4ee-854f-4ef4-92ae-fc74808b56a3"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 文件夹不存在","method":"GET","service":"email-system-backend","stack":"Error: 文件夹不存在\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\folderController.ts:48:11\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 21:42:20","url":"/api/folders/non-existent-folder"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 无权访问此文件夹","method":"GET","service":"email-system-backend","stack":"Error: 无权访问此文件夹\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\folderController.ts:53:11\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 21:42:20","url":"/api/folders/inbox-0874fb0a-6311-4a98-91f6-769b3000cd9e"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 访问令牌缺失","method":"GET","service":"email-system-backend","stack":"Error: 访问令牌缺失\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:18:13)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at requestLogger (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\utils\\logger.ts:84:3)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at urlencodedParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\urlencoded.js:68:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at jsonParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\json.js:100:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\express-rate-limit\\dist\\index.cjs:822:7\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\express-rate-limit\\dist\\index.cjs:704:5","timestamp":"2025-06-12 21:42:20","url":"/api/folders/inbox"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 收件人不能为空","method":"POST","service":"email-system-backend","stack":"Error: 收件人不能为空\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\emailController.ts:88:11\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\errorHandler.ts:66:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:41:5)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 21:42:20","url":"/api/emails"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 邮件主题不能为空","method":"POST","service":"email-system-backend","stack":"Error: 邮件主题不能为空\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\emailController.ts:92:11\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\errorHandler.ts:66:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:41:5)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 21:42:20","url":"/api/emails"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 邮件内容不能为空","method":"POST","service":"email-system-backend","stack":"Error: 邮件内容不能为空\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\emailController.ts:96:11\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\errorHandler.ts:66:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:41:5)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 21:42:20","url":"/api/emails"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 邮件不存在","method":"PUT","service":"email-system-backend","stack":"Error: 邮件不存在\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\emailController.ts:150:11\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 21:42:20","url":"/api/emails/non-existent-id"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 邮件不存在","method":"DELETE","service":"email-system-backend","stack":"Error: 邮件不存在\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\emailController.ts:189:11\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 21:42:20","url":"/api/emails/non-existent-id"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 无权删除此邮件","method":"DELETE","service":"email-system-backend","stack":"Error: 无权删除此邮件\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\emailController.ts:194:11\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 21:42:20","url":"/api/emails/d3c01aec-1309-40ed-9f40-587e91478623"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 邮箱或密码错误","method":"POST","service":"email-system-backend","stack":"Error: 邮箱或密码错误\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\authController.ts:104:11\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 21:42:20","url":"/api/auth/login"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 访问令牌缺失","method":"POST","service":"email-system-backend","stack":"Error: 访问令牌缺失\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:18:13)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at requestLogger (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\utils\\logger.ts:84:3)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at urlencodedParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\urlencoded.js:57:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\read.js:132:5\n    at AsyncResource.runInAsyncScope (node:async_hooks:206:9)\n    at invokeCallback (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\raw-body\\index.js:238:16)\n    at done (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at endReadableNT (node:internal/streams/readable:1696:12)\n    at processTicksAndRejections (node:internal/process/task_queues:82:21)","timestamp":"2025-06-12 21:42:20","url":"/api/emails/search"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 邮箱或密码错误","method":"POST","service":"email-system-backend","stack":"Error: 邮箱或密码错误\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\authController.ts:115:11","timestamp":"2025-06-12 21:42:20","url":"/api/auth/login"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 邮箱和密码都是必填的","method":"POST","service":"email-system-backend","stack":"Error: 邮箱和密码都是必填的\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\authController.ts:98:11\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\errorHandler.ts:66:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at requestLogger (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\utils\\logger.ts:84:3)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at urlencodedParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\urlencoded.js:57:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\read.js:132:5\n    at AsyncResource.runInAsyncScope (node:async_hooks:206:9)\n    at invokeCallback (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\raw-body\\index.js:238:16)\n    at done (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at endReadableNT (node:internal/streams/readable:1696:12)\n    at processTicksAndRejections (node:internal/process/task_queues:82:21)","timestamp":"2025-06-12 21:42:20","url":"/api/auth/login"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 请提供至少一个搜索条件","method":"POST","service":"email-system-backend","stack":"Error: 请提供至少一个搜索条件\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\emailController.ts:221:11\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\errorHandler.ts:66:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:41:5)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 21:42:20","url":"/api/emails/search"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 请提供至少一个搜索条件","method":"POST","service":"email-system-backend","stack":"Error: 请提供至少一个搜索条件\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\emailController.ts:221:11\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\errorHandler.ts:66:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:41:5)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 21:42:20","url":"/api/emails/search"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 访问令牌缺失","method":"GET","service":"email-system-backend","stack":"Error: 访问令牌缺失\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:18:13)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at requestLogger (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\utils\\logger.ts:84:3)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at urlencodedParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\urlencoded.js:68:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at jsonParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\json.js:100:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\express-rate-limit\\dist\\index.cjs:822:7\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\express-rate-limit\\dist\\index.cjs:704:5","timestamp":"2025-06-12 21:42:20","url":"/api/auth/me"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 无效的访问令牌","method":"GET","service":"email-system-backend","stack":"Error: 无效的访问令牌\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:44:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at requestLogger (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\utils\\logger.ts:84:3)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at urlencodedParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\urlencoded.js:68:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at jsonParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\json.js:100:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\express-rate-limit\\dist\\index.cjs:822:7\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\express-rate-limit\\dist\\index.cjs:704:5","timestamp":"2025-06-12 21:42:20","url":"/api/auth/me"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 访问令牌缺失","method":"POST","service":"email-system-backend","stack":"Error: 访问令牌缺失\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:18:13)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at requestLogger (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\utils\\logger.ts:84:3)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at urlencodedParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\urlencoded.js:77:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at jsonParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\json.js:109:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\express-rate-limit\\dist\\index.cjs:822:7\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\express-rate-limit\\dist\\index.cjs:704:5","timestamp":"2025-06-12 21:42:20","url":"/api/auth/logout"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 刷新令牌缺失","method":"POST","service":"email-system-backend","stack":"Error: 刷新令牌缺失\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\authController.ts:188:11\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\errorHandler.ts:66:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at requestLogger (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\utils\\logger.ts:84:3)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at urlencodedParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\urlencoded.js:57:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\read.js:132:5\n    at AsyncResource.runInAsyncScope (node:async_hooks:206:9)\n    at invokeCallback (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\raw-body\\index.js:238:16)\n    at done (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at endReadableNT (node:internal/streams/readable:1696:12)\n    at processTicksAndRejections (node:internal/process/task_queues:82:21)","timestamp":"2025-06-12 21:42:20","url":"/api/auth/refresh"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 所有字段都是必填的","method":"POST","service":"email-system-backend","stack":"Error: 所有字段都是必填的\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\authController.ts:15:11\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\errorHandler.ts:66:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at requestLogger (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\utils\\logger.ts:84:3)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at urlencodedParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\urlencoded.js:57:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\read.js:132:5\n    at AsyncResource.runInAsyncScope (node:async_hooks:206:9)\n    at invokeCallback (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\raw-body\\index.js:238:16)\n    at done (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at endReadableNT (node:internal/streams/readable:1696:12)\n    at processTicksAndRejections (node:internal/process/task_queues:82:21)","timestamp":"2025-06-12 21:43:13","url":"/api/auth/register"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 访问令牌缺失","method":"GET","service":"email-system-backend","stack":"Error: 访问令牌缺失\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:18:13)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at requestLogger (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\utils\\logger.ts:84:3)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at urlencodedParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\urlencoded.js:68:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at jsonParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\json.js:100:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\express-rate-limit\\dist\\index.cjs:822:7\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\express-rate-limit\\dist\\index.cjs:704:5","timestamp":"2025-06-12 21:43:13","url":"/api/folders"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 该邮箱已被注册","method":"POST","service":"email-system-backend","stack":"Error: 该邮箱已被注册\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\authController.ts:32:11\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 21:43:13","url":"/api/auth/register"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 邮箱格式无效","method":"POST","service":"email-system-backend","stack":"Error: 邮箱格式无效\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\authController.ts:21:11\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\errorHandler.ts:66:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at requestLogger (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\utils\\logger.ts:84:3)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at urlencodedParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\urlencoded.js:57:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\read.js:132:5\n    at AsyncResource.runInAsyncScope (node:async_hooks:206:9)\n    at invokeCallback (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\raw-body\\index.js:238:16)\n    at done (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at endReadableNT (node:internal/streams/readable:1696:12)\n    at processTicksAndRejections (node:internal/process/task_queues:82:21)","timestamp":"2025-06-12 21:43:13","url":"/api/auth/register"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 访问令牌缺失","method":"GET","service":"email-system-backend","stack":"Error: 访问令牌缺失\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:18:13)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at requestLogger (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\utils\\logger.ts:84:3)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at urlencodedParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\urlencoded.js:68:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at jsonParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\json.js:100:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\express-rate-limit\\dist\\index.cjs:822:7\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\express-rate-limit\\dist\\index.cjs:704:5","timestamp":"2025-06-12 21:43:13","url":"/api/emails"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 邮件不存在","method":"GET","service":"email-system-backend","stack":"Error: 邮件不存在\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\emailController.ts:55:11\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 21:43:13","url":"/api/emails/non-existent-id"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 文件夹不存在","method":"GET","service":"email-system-backend","stack":"Error: 文件夹不存在\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\folderController.ts:48:11\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 21:43:13","url":"/api/folders/non-existent-folder"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 无权访问此邮件","method":"GET","service":"email-system-backend","stack":"Error: 无权访问此邮件\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\emailController.ts:60:11\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 21:43:13","url":"/api/emails/99b493d7-c241-44d8-9bdd-cd485e351ca0"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 无权访问此文件夹","method":"GET","service":"email-system-backend","stack":"Error: 无权访问此文件夹\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\folderController.ts:53:11\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 21:43:13","url":"/api/folders/inbox-7eb92a49-bd93-40d2-8237-3dc08dcbf321"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 访问令牌缺失","method":"GET","service":"email-system-backend","stack":"Error: 访问令牌缺失\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:18:13)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at requestLogger (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\utils\\logger.ts:84:3)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at urlencodedParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\urlencoded.js:68:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at jsonParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\json.js:100:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\express-rate-limit\\dist\\index.cjs:822:7\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\express-rate-limit\\dist\\index.cjs:704:5","timestamp":"2025-06-12 21:43:13","url":"/api/folders/inbox"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 收件人不能为空","method":"POST","service":"email-system-backend","stack":"Error: 收件人不能为空\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\emailController.ts:88:11\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\errorHandler.ts:66:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:41:5)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 21:43:13","url":"/api/emails"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 邮件主题不能为空","method":"POST","service":"email-system-backend","stack":"Error: 邮件主题不能为空\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\emailController.ts:92:11\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\errorHandler.ts:66:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:41:5)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 21:43:13","url":"/api/emails"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 邮件内容不能为空","method":"POST","service":"email-system-backend","stack":"Error: 邮件内容不能为空\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\emailController.ts:96:11\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\errorHandler.ts:66:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:41:5)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 21:43:13","url":"/api/emails"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 邮件不存在","method":"PUT","service":"email-system-backend","stack":"Error: 邮件不存在\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\emailController.ts:150:11\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 21:43:13","url":"/api/emails/non-existent-id"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 邮件不存在","method":"DELETE","service":"email-system-backend","stack":"Error: 邮件不存在\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\emailController.ts:189:11\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 21:43:13","url":"/api/emails/non-existent-id"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 无权删除此邮件","method":"DELETE","service":"email-system-backend","stack":"Error: 无权删除此邮件\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\emailController.ts:194:11\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 21:43:13","url":"/api/emails/ff29a614-8de7-4594-af9b-7dff754a54a2"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 邮箱或密码错误","method":"POST","service":"email-system-backend","stack":"Error: 邮箱或密码错误\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\authController.ts:104:11\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 21:43:13","url":"/api/auth/login"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 访问令牌缺失","method":"POST","service":"email-system-backend","stack":"Error: 访问令牌缺失\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:18:13)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at requestLogger (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\utils\\logger.ts:84:3)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at urlencodedParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\urlencoded.js:57:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\read.js:132:5\n    at AsyncResource.runInAsyncScope (node:async_hooks:206:9)\n    at invokeCallback (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\raw-body\\index.js:238:16)\n    at done (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at endReadableNT (node:internal/streams/readable:1696:12)\n    at processTicksAndRejections (node:internal/process/task_queues:82:21)","timestamp":"2025-06-12 21:43:13","url":"/api/emails/search"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 请提供至少一个搜索条件","method":"POST","service":"email-system-backend","stack":"Error: 请提供至少一个搜索条件\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\emailController.ts:221:11\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\errorHandler.ts:66:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:41:5)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 21:43:13","url":"/api/emails/search"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 请提供至少一个搜索条件","method":"POST","service":"email-system-backend","stack":"Error: 请提供至少一个搜索条件\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\emailController.ts:221:11\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\errorHandler.ts:66:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:41:5)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 21:43:13","url":"/api/emails/search"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 邮箱或密码错误","method":"POST","service":"email-system-backend","stack":"Error: 邮箱或密码错误\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\authController.ts:115:11","timestamp":"2025-06-12 21:43:13","url":"/api/auth/login"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 邮箱和密码都是必填的","method":"POST","service":"email-system-backend","stack":"Error: 邮箱和密码都是必填的\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\authController.ts:98:11\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\errorHandler.ts:66:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at requestLogger (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\utils\\logger.ts:84:3)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at urlencodedParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\urlencoded.js:57:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\read.js:132:5\n    at AsyncResource.runInAsyncScope (node:async_hooks:206:9)\n    at invokeCallback (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\raw-body\\index.js:238:16)\n    at done (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at endReadableNT (node:internal/streams/readable:1696:12)\n    at processTicksAndRejections (node:internal/process/task_queues:82:21)","timestamp":"2025-06-12 21:43:13","url":"/api/auth/login"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 访问令牌缺失","method":"GET","service":"email-system-backend","stack":"Error: 访问令牌缺失\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:18:13)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at requestLogger (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\utils\\logger.ts:84:3)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at urlencodedParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\urlencoded.js:68:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at jsonParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\json.js:100:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\express-rate-limit\\dist\\index.cjs:822:7\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\express-rate-limit\\dist\\index.cjs:704:5","timestamp":"2025-06-12 21:43:13","url":"/api/auth/me"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 无效的访问令牌","method":"GET","service":"email-system-backend","stack":"Error: 无效的访问令牌\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:44:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at requestLogger (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\utils\\logger.ts:84:3)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at urlencodedParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\urlencoded.js:68:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at jsonParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\json.js:100:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\express-rate-limit\\dist\\index.cjs:822:7\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\express-rate-limit\\dist\\index.cjs:704:5","timestamp":"2025-06-12 21:43:13","url":"/api/auth/me"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 访问令牌缺失","method":"POST","service":"email-system-backend","stack":"Error: 访问令牌缺失\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:18:13)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at requestLogger (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\utils\\logger.ts:84:3)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at urlencodedParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\urlencoded.js:77:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at jsonParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\json.js:109:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\express-rate-limit\\dist\\index.cjs:822:7\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\express-rate-limit\\dist\\index.cjs:704:5","timestamp":"2025-06-12 21:43:13","url":"/api/auth/logout"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 刷新令牌缺失","method":"POST","service":"email-system-backend","stack":"Error: 刷新令牌缺失\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\authController.ts:188:11\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\errorHandler.ts:66:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at requestLogger (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\utils\\logger.ts:84:3)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at urlencodedParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\urlencoded.js:57:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\read.js:132:5\n    at AsyncResource.runInAsyncScope (node:async_hooks:206:9)\n    at invokeCallback (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\raw-body\\index.js:238:16)\n    at done (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at endReadableNT (node:internal/streams/readable:1696:12)\n    at processTicksAndRejections (node:internal/process/task_queues:82:21)","timestamp":"2025-06-12 21:43:13","url":"/api/auth/refresh"}
{"code":"EPIPE","errno":-4047,"level":"error","message":"未捕获的异常: write EPIPE","service":"email-system-backend","stack":"Error: write EPIPE\n    at afterWriteDispatched (node:internal/stream_base_commons:160:15)\n    at writeGeneric (node:internal/stream_base_commons:151:3)\n    at WriteStream.Socket._writeGeneric (node:net:952:11)\n    at WriteStream.Socket._write (node:net:964:8)\n    at writeOrBuffer (node:internal/streams/writable:564:12)\n    at _write (node:internal/streams/writable:493:10)\n    at WriteStream.Writable.write (node:internal/streams/writable:502:10)\n    at Console.log (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\winston\\lib\\winston\\transports\\console.js:87:23)\n    at Console._write (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\winston-transport\\modern.js:103:17)\n    at doWrite (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\readable-stream\\lib\\_stream_writable.js:390:139)","syscall":"write","timestamp":"2025-06-12 21:48:29"}
{"ip":"::1","level":"error","message":"Error occurred: 用户不存在","method":"GET","service":"email-system-backend","stack":"Error: 用户不存在\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:32:13)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 21:49:05","url":"/api/folders","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error occurred: 用户不存在","method":"GET","service":"email-system-backend","stack":"Error: 用户不存在\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:32:13)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 21:49:05","url":"/api/folders","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error occurred: 用户不存在","method":"GET","service":"email-system-backend","stack":"Error: 用户不存在\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:32:13)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 21:49:05","url":"/api/folders","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error occurred: 用户不存在","method":"GET","service":"email-system-backend","stack":"Error: 用户不存在\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:32:13)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 21:49:05","url":"/api/folders","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error occurred: 无效的访问令牌","method":"GET","service":"email-system-backend","stack":"Error: 无效的访问令牌\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:44:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)","timestamp":"2025-06-12 21:49:05","url":"/api/folders","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error occurred: 无效的访问令牌","method":"GET","service":"email-system-backend","stack":"Error: 无效的访问令牌\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:44:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)","timestamp":"2025-06-12 21:49:05","url":"/api/folders","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error occurred: 无效的访问令牌","method":"GET","service":"email-system-backend","stack":"Error: 无效的访问令牌\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:44:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)","timestamp":"2025-06-12 21:49:05","url":"/api/folders","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error occurred: 无效的访问令牌","method":"GET","service":"email-system-backend","stack":"Error: 无效的访问令牌\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:44:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)","timestamp":"2025-06-12 21:49:05","url":"/api/folders","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error occurred: 无效的访问令牌","method":"GET","service":"email-system-backend","stack":"Error: 无效的访问令牌\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:44:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)","timestamp":"2025-06-12 21:49:05","url":"/api/folders","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error occurred: 无效的访问令牌","method":"GET","service":"email-system-backend","stack":"Error: 无效的访问令牌\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:44:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)","timestamp":"2025-06-12 21:49:05","url":"/api/folders","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error occurred: 无效的访问令牌","method":"GET","service":"email-system-backend","stack":"Error: 无效的访问令牌\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:44:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)","timestamp":"2025-06-12 21:49:05","url":"/api/folders","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error occurred: 无效的访问令牌","method":"GET","service":"email-system-backend","stack":"Error: 无效的访问令牌\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:44:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)","timestamp":"2025-06-12 21:49:05","url":"/api/folders","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error occurred: 无效的访问令牌","method":"GET","service":"email-system-backend","stack":"Error: 无效的访问令牌\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:44:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)","timestamp":"2025-06-12 21:49:05","url":"/api/folders","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error occurred: 无效的访问令牌","method":"GET","service":"email-system-backend","stack":"Error: 无效的访问令牌\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:44:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)","timestamp":"2025-06-12 21:49:05","url":"/api/folders","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error occurred: 无效的访问令牌","method":"GET","service":"email-system-backend","stack":"Error: 无效的访问令牌\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:44:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)","timestamp":"2025-06-12 21:49:05","url":"/api/folders","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error occurred: 无效的访问令牌","method":"GET","service":"email-system-backend","stack":"Error: 无效的访问令牌\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:44:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)","timestamp":"2025-06-12 21:49:05","url":"/api/folders","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error occurred: 无效的访问令牌","method":"GET","service":"email-system-backend","stack":"Error: 无效的访问令牌\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:44:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)","timestamp":"2025-06-12 21:49:05","url":"/api/folders","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error occurred: 无效的访问令牌","method":"GET","service":"email-system-backend","stack":"Error: 无效的访问令牌\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:44:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)","timestamp":"2025-06-12 21:49:05","url":"/api/folders","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error occurred: 无效的访问令牌","method":"GET","service":"email-system-backend","stack":"Error: 无效的访问令牌\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:44:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)","timestamp":"2025-06-12 21:49:05","url":"/api/folders","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error occurred: 无效的访问令牌","method":"GET","service":"email-system-backend","stack":"Error: 无效的访问令牌\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:44:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)","timestamp":"2025-06-12 21:49:05","url":"/api/folders","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error occurred: 无效的访问令牌","method":"GET","service":"email-system-backend","stack":"Error: 无效的访问令牌\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:44:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)","timestamp":"2025-06-12 21:49:05","url":"/api/folders","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error occurred: 无效的访问令牌","method":"GET","service":"email-system-backend","stack":"Error: 无效的访问令牌\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:44:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)","timestamp":"2025-06-12 21:49:05","url":"/api/folders","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error occurred: 无效的访问令牌","method":"GET","service":"email-system-backend","stack":"Error: 无效的访问令牌\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:44:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)","timestamp":"2025-06-12 21:49:05","url":"/api/folders","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error occurred: 无效的访问令牌","method":"GET","service":"email-system-backend","stack":"Error: 无效的访问令牌\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:44:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)","timestamp":"2025-06-12 21:49:05","url":"/api/folders","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error occurred: 无效的访问令牌","method":"GET","service":"email-system-backend","stack":"Error: 无效的访问令牌\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:44:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)","timestamp":"2025-06-12 21:49:05","url":"/api/folders","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error occurred: 无效的访问令牌","method":"GET","service":"email-system-backend","stack":"Error: 无效的访问令牌\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:44:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)","timestamp":"2025-06-12 21:49:05","url":"/api/folders","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error occurred: 无效的访问令牌","method":"GET","service":"email-system-backend","stack":"Error: 无效的访问令牌\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:44:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)","timestamp":"2025-06-12 21:49:05","url":"/api/folders","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error occurred: 无效的访问令牌","method":"GET","service":"email-system-backend","stack":"Error: 无效的访问令牌\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:44:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)","timestamp":"2025-06-12 21:49:05","url":"/api/folders","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error occurred: 无效的访问令牌","method":"GET","service":"email-system-backend","stack":"Error: 无效的访问令牌\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:44:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)","timestamp":"2025-06-12 21:49:05","url":"/api/folders","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error occurred: 无效的访问令牌","method":"GET","service":"email-system-backend","stack":"Error: 无效的访问令牌\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:44:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)","timestamp":"2025-06-12 21:49:05","url":"/api/folders","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error occurred: 无效的访问令牌","method":"GET","service":"email-system-backend","stack":"Error: 无效的访问令牌\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:44:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)","timestamp":"2025-06-12 21:49:05","url":"/api/folders","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error occurred: 无效的访问令牌","method":"GET","service":"email-system-backend","stack":"Error: 无效的访问令牌\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:44:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)","timestamp":"2025-06-12 21:49:05","url":"/api/folders","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error occurred: 无效的访问令牌","method":"GET","service":"email-system-backend","stack":"Error: 无效的访问令牌\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:44:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)","timestamp":"2025-06-12 21:49:05","url":"/api/folders","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error occurred: 无效的访问令牌","method":"GET","service":"email-system-backend","stack":"Error: 无效的访问令牌\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:44:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)","timestamp":"2025-06-12 21:49:05","url":"/api/folders","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error occurred: 无效的访问令牌","method":"GET","service":"email-system-backend","stack":"Error: 无效的访问令牌\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:44:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)","timestamp":"2025-06-12 21:49:05","url":"/api/folders","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error occurred: 无效的访问令牌","method":"GET","service":"email-system-backend","stack":"Error: 无效的访问令牌\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:44:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)","timestamp":"2025-06-12 21:49:05","url":"/api/folders","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error occurred: 无效的访问令牌","method":"GET","service":"email-system-backend","stack":"Error: 无效的访问令牌\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:44:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)","timestamp":"2025-06-12 21:49:05","url":"/api/folders","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error occurred: 无效的访问令牌","method":"GET","service":"email-system-backend","stack":"Error: 无效的访问令牌\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:44:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)","timestamp":"2025-06-12 21:49:05","url":"/api/folders","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error occurred: 无效的访问令牌","method":"GET","service":"email-system-backend","stack":"Error: 无效的访问令牌\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:44:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)","timestamp":"2025-06-12 21:49:05","url":"/api/folders","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error occurred: 无效的访问令牌","method":"GET","service":"email-system-backend","stack":"Error: 无效的访问令牌\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:44:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)","timestamp":"2025-06-12 21:49:05","url":"/api/folders","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error occurred: 无效的访问令牌","method":"GET","service":"email-system-backend","stack":"Error: 无效的访问令牌\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:44:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)","timestamp":"2025-06-12 21:49:05","url":"/api/folders","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error occurred: 无效的访问令牌","method":"GET","service":"email-system-backend","stack":"Error: 无效的访问令牌\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:44:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)","timestamp":"2025-06-12 21:49:05","url":"/api/folders","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error occurred: 无效的访问令牌","method":"GET","service":"email-system-backend","stack":"Error: 无效的访问令牌\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:44:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)","timestamp":"2025-06-12 21:49:05","url":"/api/folders","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error occurred: 无效的访问令牌","method":"GET","service":"email-system-backend","stack":"Error: 无效的访问令牌\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:44:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)","timestamp":"2025-06-12 21:49:05","url":"/api/folders","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error occurred: 无效的访问令牌","method":"GET","service":"email-system-backend","stack":"Error: 无效的访问令牌\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:44:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)","timestamp":"2025-06-12 21:49:05","url":"/api/folders","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error occurred: 无效的访问令牌","method":"GET","service":"email-system-backend","stack":"Error: 无效的访问令牌\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:44:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)","timestamp":"2025-06-12 21:49:05","url":"/api/folders","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error occurred: 无效的访问令牌","method":"GET","service":"email-system-backend","stack":"Error: 无效的访问令牌\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:44:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)","timestamp":"2025-06-12 21:49:05","url":"/api/folders","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error occurred: 无效的访问令牌","method":"GET","service":"email-system-backend","stack":"Error: 无效的访问令牌\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:44:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)","timestamp":"2025-06-12 21:49:05","url":"/api/folders","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error occurred: 无效的访问令牌","method":"GET","service":"email-system-backend","stack":"Error: 无效的访问令牌\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:44:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)","timestamp":"2025-06-12 21:49:05","url":"/api/folders","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error occurred: 无效的访问令牌","method":"GET","service":"email-system-backend","stack":"Error: 无效的访问令牌\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:44:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)","timestamp":"2025-06-12 21:49:05","url":"/api/folders","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error occurred: 无效的访问令牌","method":"GET","service":"email-system-backend","stack":"Error: 无效的访问令牌\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:44:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)","timestamp":"2025-06-12 21:49:05","url":"/api/folders","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error occurred: 邮箱或密码错误","method":"POST","service":"email-system-backend","stack":"Error: 邮箱或密码错误\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\authController.ts:104:11\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 21:49:23","url":"/api/auth/login","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 访问令牌缺失","method":"GET","params":{},"query":{},"requestId":"cnkaho8l8hhyo3ubqd55d","service":"email-system-backend","stack":"Error: 访问令牌缺失\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:18:13)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at requestLogger (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\utils\\logger.ts:84:3)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at urlencodedParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\urlencoded.js:68:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at jsonParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\json.js:100:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\express-rate-limit\\dist\\index.cjs:822:7\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\express-rate-limit\\dist\\index.cjs:704:5","timestamp":"2025-06-12 21:57:08","url":"/api/folders"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 访问令牌缺失","method":"GET","params":{},"query":{},"requestId":"nde8r7alvqfly9c7tmje","service":"email-system-backend","stack":"Error: 访问令牌缺失\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:18:13)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at requestLogger (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\utils\\logger.ts:84:3)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at urlencodedParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\urlencoded.js:68:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at jsonParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\json.js:100:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\express-rate-limit\\dist\\index.cjs:822:7\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\express-rate-limit\\dist\\index.cjs:704:5","timestamp":"2025-06-12 21:57:08","url":"/api/emails"}
{"body":{"email":"<EMAIL>"},"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 所有字段都是必填的","method":"POST","params":{},"query":{},"requestId":"yryn8dckg2f6ranoe6aa6c","service":"email-system-backend","stack":"Error: 所有字段都是必填的\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\authController.ts:15:11\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\errorHandler.ts:108:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at requestLogger (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\utils\\logger.ts:84:3)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at urlencodedParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\urlencoded.js:57:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\read.js:132:5\n    at AsyncResource.runInAsyncScope (node:async_hooks:206:9)\n    at invokeCallback (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\raw-body\\index.js:238:16)\n    at done (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at endReadableNT (node:internal/streams/readable:1696:12)\n    at processTicksAndRejections (node:internal/process/task_queues:82:21)","timestamp":"2025-06-12 21:57:08","url":"/api/auth/register"}
{"body":{"displayName":"新用户","email":"<EMAIL>","password":"password123","username":"newuser"},"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 该邮箱已被注册","method":"POST","params":{},"query":{},"requestId":"g1benoxbzio5id446udha","service":"email-system-backend","stack":"Error: 该邮箱已被注册\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\authController.ts:32:11\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 21:57:08","url":"/api/auth/register"}
{"body":{"displayName":"新用户","email":"invalid-email","password":"password123","username":"newuser"},"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 邮箱格式无效","method":"POST","params":{},"query":{},"requestId":"ykt4t2lfz2vv8spgg4rt","service":"email-system-backend","stack":"Error: 邮箱格式无效\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\authController.ts:21:11\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\errorHandler.ts:108:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at requestLogger (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\utils\\logger.ts:84:3)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at urlencodedParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\urlencoded.js:57:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\read.js:132:5\n    at AsyncResource.runInAsyncScope (node:async_hooks:206:9)\n    at invokeCallback (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\raw-body\\index.js:238:16)\n    at done (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at endReadableNT (node:internal/streams/readable:1696:12)\n    at processTicksAndRejections (node:internal/process/task_queues:82:21)","timestamp":"2025-06-12 21:57:08","url":"/api/auth/register"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 邮件不存在","method":"GET","params":{},"query":{},"requestId":"6h1c9a9peqsl1qa9o9djp9","service":"email-system-backend","stack":"Error: 邮件不存在\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\emailController.ts:55:11\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 21:57:08","url":"/api/emails/non-existent-id"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 无权访问此邮件","method":"GET","params":{},"query":{},"requestId":"jak3k92c3eao9cxhrlbui","service":"email-system-backend","stack":"Error: 无权访问此邮件\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\emailController.ts:60:11\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 21:57:08","url":"/api/emails/7d6ccdd7-a320-4be8-bda3-30576a60a341"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 文件夹不存在","method":"GET","params":{},"query":{},"requestId":"hy8yutp1vdvf1qcq6tffnb","service":"email-system-backend","stack":"Error: 文件夹不存在\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\folderController.ts:48:11\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 21:57:08","url":"/api/folders/non-existent-folder"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 无权访问此文件夹","method":"GET","params":{},"query":{},"requestId":"pqb3mld9s2r2zsuj1a5b1v","service":"email-system-backend","stack":"Error: 无权访问此文件夹\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\folderController.ts:53:11\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 21:57:08","url":"/api/folders/inbox-6617340a-851e-4503-9eb2-06118c4f66d2"}
{"body":{"query":"JavaScript"},"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 访问令牌缺失","method":"POST","params":{},"query":{},"requestId":"o11cx4t0mydx5zkduuh4h","service":"email-system-backend","stack":"Error: 访问令牌缺失\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:18:13)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at requestLogger (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\utils\\logger.ts:84:3)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at urlencodedParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\urlencoded.js:57:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\read.js:132:5\n    at AsyncResource.runInAsyncScope (node:async_hooks:206:9)\n    at invokeCallback (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\raw-body\\index.js:238:16)\n    at done (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at endReadableNT (node:internal/streams/readable:1696:12)\n    at processTicksAndRejections (node:internal/process/task_queues:82:21)","timestamp":"2025-06-12 21:57:08","url":"/api/emails/search"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 访问令牌缺失","method":"GET","params":{},"query":{},"requestId":"joa5jpl611i3n7hw9w7cqf","service":"email-system-backend","stack":"Error: 访问令牌缺失\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:18:13)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at requestLogger (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\utils\\logger.ts:84:3)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at urlencodedParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\urlencoded.js:68:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at jsonParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\json.js:100:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\express-rate-limit\\dist\\index.cjs:822:7\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\express-rate-limit\\dist\\index.cjs:704:5","timestamp":"2025-06-12 21:57:08","url":"/api/folders/inbox"}
{"body":{"query":""},"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 请提供至少一个搜索条件","method":"POST","params":{},"query":{},"requestId":"ad6k9ww9fe4522hnn4miz","service":"email-system-backend","stack":"Error: 请提供至少一个搜索条件\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\emailController.ts:221:11\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\errorHandler.ts:108:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:41:5)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 21:57:08","url":"/api/emails/search"}
{"body":{},"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 请提供至少一个搜索条件","method":"POST","params":{},"query":{},"requestId":"16ammrw7oykkrmt1g5tsh","service":"email-system-backend","stack":"Error: 请提供至少一个搜索条件\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\emailController.ts:221:11\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\errorHandler.ts:108:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:41:5)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 21:57:08","url":"/api/emails/search"}
{"body":{"content":"这是邮件内容","subject":"测试邮件主题"},"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 收件人不能为空","method":"POST","params":{},"query":{},"requestId":"h4yiwcrtuyjhw02hlrwfu6","service":"email-system-backend","stack":"Error: 收件人不能为空\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\emailController.ts:88:11\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\errorHandler.ts:108:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:41:5)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 21:57:08","url":"/api/emails"}
{"body":{"content":"这是邮件内容","to":["<EMAIL>"]},"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 邮件主题不能为空","method":"POST","params":{},"query":{},"requestId":"3z2mfftk8m4x97jpegj6tp","service":"email-system-backend","stack":"Error: 邮件主题不能为空\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\emailController.ts:92:11\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\errorHandler.ts:108:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:41:5)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 21:57:08","url":"/api/emails"}
{"body":{"subject":"测试邮件主题","to":["<EMAIL>"]},"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 邮件内容不能为空","method":"POST","params":{},"query":{},"requestId":"m5poixne5wp5qsq5h6kkyg","service":"email-system-backend","stack":"Error: 邮件内容不能为空\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\emailController.ts:96:11\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\errorHandler.ts:108:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:41:5)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 21:57:08","url":"/api/emails"}
{"body":{"isRead":true},"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 邮件不存在","method":"PUT","params":{},"query":{},"requestId":"8gks471m8cieslumhyylgf","service":"email-system-backend","stack":"Error: 邮件不存在\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\emailController.ts:150:11\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 21:57:08","url":"/api/emails/non-existent-id"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 邮件不存在","method":"DELETE","params":{},"query":{},"requestId":"5zbxgej0g5hbhsdnrm61dg","service":"email-system-backend","stack":"Error: 邮件不存在\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\emailController.ts:189:11\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 21:57:08","url":"/api/emails/non-existent-id"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 无权删除此邮件","method":"DELETE","params":{},"query":{},"requestId":"s8tgupnthdtuhdgmysqqf","service":"email-system-backend","stack":"Error: 无权删除此邮件\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\emailController.ts:194:11\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 21:57:08","url":"/api/emails/91c9973f-5ece-4837-acf7-a5496e0e0c27"}
{"body":{"email":"<EMAIL>","password":"123456"},"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 邮箱或密码错误","method":"POST","params":{},"query":{},"requestId":"vs020fr0l7tc35screo4ff","service":"email-system-backend","stack":"Error: 邮箱或密码错误\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\authController.ts:104:11\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 21:57:08","url":"/api/auth/login"}
{"body":{"email":"<EMAIL>","password":"wrongpassword"},"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 邮箱或密码错误","method":"POST","params":{},"query":{},"requestId":"nwtzizkptqyybl4p9mtod","service":"email-system-backend","stack":"Error: 邮箱或密码错误\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\authController.ts:115:11","timestamp":"2025-06-12 21:57:08","url":"/api/auth/login"}
{"body":{},"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 邮箱和密码都是必填的","method":"POST","params":{},"query":{},"requestId":"4fqvkdx68xxiw81kt2fgyk","service":"email-system-backend","stack":"Error: 邮箱和密码都是必填的\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\authController.ts:98:11\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\errorHandler.ts:108:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at requestLogger (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\utils\\logger.ts:84:3)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at urlencodedParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\urlencoded.js:57:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\read.js:132:5\n    at AsyncResource.runInAsyncScope (node:async_hooks:206:9)\n    at invokeCallback (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\raw-body\\index.js:238:16)\n    at done (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at endReadableNT (node:internal/streams/readable:1696:12)\n    at processTicksAndRejections (node:internal/process/task_queues:82:21)","timestamp":"2025-06-12 21:57:08","url":"/api/auth/login"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 访问令牌缺失","method":"GET","params":{},"query":{},"requestId":"an57tggn6gujesuii3thxg","service":"email-system-backend","stack":"Error: 访问令牌缺失\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:18:13)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at requestLogger (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\utils\\logger.ts:84:3)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at urlencodedParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\urlencoded.js:68:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at jsonParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\json.js:100:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\express-rate-limit\\dist\\index.cjs:822:7\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\express-rate-limit\\dist\\index.cjs:704:5","timestamp":"2025-06-12 21:57:08","url":"/api/auth/me"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 无效的访问令牌","method":"GET","params":{},"query":{},"requestId":"iyf7mk0xlkgrvvfdcqrmfb","service":"email-system-backend","stack":"Error: 无效的访问令牌\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:44:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at requestLogger (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\utils\\logger.ts:84:3)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at urlencodedParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\urlencoded.js:68:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at jsonParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\json.js:100:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\express-rate-limit\\dist\\index.cjs:822:7\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\express-rate-limit\\dist\\index.cjs:704:5","timestamp":"2025-06-12 21:57:08","url":"/api/auth/me"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 访问令牌缺失","method":"POST","params":{},"query":{},"requestId":"i40dt9are4d8puj99gj5th","service":"email-system-backend","stack":"Error: 访问令牌缺失\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:18:13)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at requestLogger (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\utils\\logger.ts:84:3)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at urlencodedParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\urlencoded.js:77:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at jsonParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\json.js:109:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\express-rate-limit\\dist\\index.cjs:822:7\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\express-rate-limit\\dist\\index.cjs:704:5","timestamp":"2025-06-12 21:57:08","url":"/api/auth/logout"}
{"body":{},"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 刷新令牌缺失","method":"POST","params":{},"query":{},"requestId":"441fx3j5q7frox2p6hr8","service":"email-system-backend","stack":"Error: 刷新令牌缺失\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\authController.ts:188:11\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\errorHandler.ts:108:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at requestLogger (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\utils\\logger.ts:84:3)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at urlencodedParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\urlencoded.js:57:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\read.js:132:5\n    at AsyncResource.runInAsyncScope (node:async_hooks:206:9)\n    at invokeCallback (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\raw-body\\index.js:238:16)\n    at done (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at endReadableNT (node:internal/streams/readable:1696:12)\n    at processTicksAndRejections (node:internal/process/task_queues:82:21)","timestamp":"2025-06-12 21:57:08","url":"/api/auth/refresh"}
{"body":{"email":"<EMAIL>"},"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 所有字段都是必填的","method":"POST","params":{},"query":{},"requestId":"97bwjhtbtir6bcw0cdh2uc","service":"email-system-backend","stack":"Error: 所有字段都是必填的\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\authController.ts:15:11\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\errorHandler.ts:108:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at requestLogger (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\utils\\logger.ts:84:3)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at urlencodedParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\urlencoded.js:57:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\read.js:132:5\n    at AsyncResource.runInAsyncScope (node:async_hooks:206:9)\n    at invokeCallback (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\raw-body\\index.js:238:16)\n    at done (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at endReadableNT (node:internal/streams/readable:1696:12)\n    at processTicksAndRejections (node:internal/process/task_queues:82:21)","timestamp":"2025-06-12 22:08:32","url":"/api/auth/register"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 访问令牌缺失","method":"GET","params":{},"query":{},"requestId":"jdacejg7kshqca9ecs82h","service":"email-system-backend","stack":"Error: 访问令牌缺失\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:18:13)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at requestLogger (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\utils\\logger.ts:84:3)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at urlencodedParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\urlencoded.js:68:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at jsonParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\json.js:100:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\express-rate-limit\\dist\\index.cjs:822:7\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\express-rate-limit\\dist\\index.cjs:704:5","timestamp":"2025-06-12 22:08:32","url":"/api/folders"}
{"body":{"displayName":"新用户","email":"<EMAIL>","password":"password123","username":"newuser"},"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 该邮箱已被注册","method":"POST","params":{},"query":{},"requestId":"co3vfkdvc9s789qx5g04ii","service":"email-system-backend","stack":"Error: 该邮箱已被注册\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\authController.ts:32:11\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 22:08:32","url":"/api/auth/register"}
{"body":{"displayName":"新用户","email":"invalid-email","password":"password123","username":"newuser"},"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 邮箱格式无效","method":"POST","params":{},"query":{},"requestId":"egdiuysa0ps5gycbp12in","service":"email-system-backend","stack":"Error: 邮箱格式无效\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\authController.ts:21:11\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\errorHandler.ts:108:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at requestLogger (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\utils\\logger.ts:84:3)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at urlencodedParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\urlencoded.js:57:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\read.js:132:5\n    at AsyncResource.runInAsyncScope (node:async_hooks:206:9)\n    at invokeCallback (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\raw-body\\index.js:238:16)\n    at done (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at endReadableNT (node:internal/streams/readable:1696:12)\n    at processTicksAndRejections (node:internal/process/task_queues:82:21)","timestamp":"2025-06-12 22:08:32","url":"/api/auth/register"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 访问令牌缺失","method":"GET","params":{},"query":{},"requestId":"hzgu7cl1lkzn51numi8mp","service":"email-system-backend","stack":"Error: 访问令牌缺失\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:18:13)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at requestLogger (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\utils\\logger.ts:84:3)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at urlencodedParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\urlencoded.js:68:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at jsonParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\json.js:100:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\express-rate-limit\\dist\\index.cjs:822:7\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\express-rate-limit\\dist\\index.cjs:704:5","timestamp":"2025-06-12 22:08:32","url":"/api/emails"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 文件夹不存在","method":"GET","params":{},"query":{},"requestId":"vobvfg8lofmthb4ptdbt","service":"email-system-backend","stack":"Error: 文件夹不存在\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\folderController.ts:48:11\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 22:08:32","url":"/api/folders/non-existent-folder"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 邮件不存在","method":"GET","params":{},"query":{},"requestId":"jaedk4kcqcn30g6tz3tc5m","service":"email-system-backend","stack":"Error: 邮件不存在\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\emailController.ts:55:11\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 22:08:32","url":"/api/emails/non-existent-id"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 无权访问此文件夹","method":"GET","params":{},"query":{},"requestId":"bvfts53sl5hls1sp8pwuz","service":"email-system-backend","stack":"Error: 无权访问此文件夹\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\folderController.ts:53:11\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 22:08:32","url":"/api/folders/inbox-bea7e709-acff-4b10-8be8-868a66381873"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 无权访问此邮件","method":"GET","params":{},"query":{},"requestId":"12szrpj7dcbtl3htk89njf","service":"email-system-backend","stack":"Error: 无权访问此邮件\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\emailController.ts:60:11\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 22:08:32","url":"/api/emails/03bdcdaa-da8a-4c51-9a73-18bbc8c557cb"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 访问令牌缺失","method":"GET","params":{},"query":{},"requestId":"ll3xl9ngsetuss8e1p9ni","service":"email-system-backend","stack":"Error: 访问令牌缺失\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:18:13)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at requestLogger (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\utils\\logger.ts:84:3)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at urlencodedParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\urlencoded.js:68:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at jsonParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\json.js:100:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\express-rate-limit\\dist\\index.cjs:822:7\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\express-rate-limit\\dist\\index.cjs:704:5","timestamp":"2025-06-12 22:08:32","url":"/api/folders/inbox"}
{"body":{"query":"JavaScript"},"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 访问令牌缺失","method":"POST","params":{},"query":{},"requestId":"733j5e2w1ywtjrsvbubkrc","service":"email-system-backend","stack":"Error: 访问令牌缺失\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:18:13)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at requestLogger (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\utils\\logger.ts:84:3)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at urlencodedParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\urlencoded.js:57:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\read.js:132:5\n    at AsyncResource.runInAsyncScope (node:async_hooks:206:9)\n    at invokeCallback (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\raw-body\\index.js:238:16)\n    at done (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at endReadableNT (node:internal/streams/readable:1696:12)\n    at processTicksAndRejections (node:internal/process/task_queues:82:21)","timestamp":"2025-06-12 22:08:33","url":"/api/emails/search"}
{"body":{"query":""},"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 请提供至少一个搜索条件","method":"POST","params":{},"query":{},"requestId":"ydmci8ax4vlk8x0k3dig","service":"email-system-backend","stack":"Error: 请提供至少一个搜索条件\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\emailController.ts:221:11\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\errorHandler.ts:108:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:41:5)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 22:08:33","url":"/api/emails/search"}
{"body":{"content":"这是邮件内容","subject":"测试邮件主题"},"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 收件人不能为空","method":"POST","params":{},"query":{},"requestId":"9i8ms1oigm9ltluyavxyu","service":"email-system-backend","stack":"Error: 收件人不能为空\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\emailController.ts:88:11\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\errorHandler.ts:108:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:41:5)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 22:08:33","url":"/api/emails"}
{"body":{},"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 请提供至少一个搜索条件","method":"POST","params":{},"query":{},"requestId":"lpyuqcp8j6f2fsy30qf70a","service":"email-system-backend","stack":"Error: 请提供至少一个搜索条件\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\emailController.ts:221:11\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\errorHandler.ts:108:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:41:5)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 22:08:33","url":"/api/emails/search"}
{"body":{"content":"这是邮件内容","to":["<EMAIL>"]},"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 邮件主题不能为空","method":"POST","params":{},"query":{},"requestId":"5fg7ex0mmqg1a4bkw7eo8t","service":"email-system-backend","stack":"Error: 邮件主题不能为空\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\emailController.ts:92:11\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\errorHandler.ts:108:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:41:5)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 22:08:33","url":"/api/emails"}
{"body":{"subject":"测试邮件主题","to":["<EMAIL>"]},"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 邮件内容不能为空","method":"POST","params":{},"query":{},"requestId":"skc8xktzln4rugrqmlmwf","service":"email-system-backend","stack":"Error: 邮件内容不能为空\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\emailController.ts:96:11\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\errorHandler.ts:108:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:41:5)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 22:08:33","url":"/api/emails"}
{"body":{"isRead":true},"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 邮件不存在","method":"PUT","params":{},"query":{},"requestId":"xt0cyis8ac9djxod94z5m","service":"email-system-backend","stack":"Error: 邮件不存在\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\emailController.ts:150:11\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 22:08:33","url":"/api/emails/non-existent-id"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 邮件不存在","method":"DELETE","params":{},"query":{},"requestId":"t04rwtwdvolx9brf2mb2xo","service":"email-system-backend","stack":"Error: 邮件不存在\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\emailController.ts:189:11\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 22:08:33","url":"/api/emails/non-existent-id"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 无权删除此邮件","method":"DELETE","params":{},"query":{},"requestId":"4a3ppd02nfr3wylffrc0rb","service":"email-system-backend","stack":"Error: 无权删除此邮件\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\emailController.ts:194:11\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 22:08:33","url":"/api/emails/49fa1cc2-1ca0-4a51-8f56-ec08d41d45d4"}
{"body":{"email":"<EMAIL>","password":"123456"},"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 邮箱或密码错误","method":"POST","params":{},"query":{},"requestId":"g2qex9ezh7wbux9164bhc5","service":"email-system-backend","stack":"Error: 邮箱或密码错误\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\authController.ts:104:11\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 22:08:33","url":"/api/auth/login"}
{"body":{"email":"<EMAIL>","password":"wrongpassword"},"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 邮箱或密码错误","method":"POST","params":{},"query":{},"requestId":"19low89x8iwhfj9oxtvpmqw","service":"email-system-backend","stack":"Error: 邮箱或密码错误\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\authController.ts:115:11","timestamp":"2025-06-12 22:08:33","url":"/api/auth/login"}
{"body":{},"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 邮箱和密码都是必填的","method":"POST","params":{},"query":{},"requestId":"gifhb4x9hn61yjwchkzu22","service":"email-system-backend","stack":"Error: 邮箱和密码都是必填的\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\authController.ts:98:11\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\errorHandler.ts:108:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at requestLogger (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\utils\\logger.ts:84:3)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at urlencodedParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\urlencoded.js:57:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\read.js:132:5\n    at AsyncResource.runInAsyncScope (node:async_hooks:206:9)\n    at invokeCallback (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\raw-body\\index.js:238:16)\n    at done (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at endReadableNT (node:internal/streams/readable:1696:12)\n    at processTicksAndRejections (node:internal/process/task_queues:82:21)","timestamp":"2025-06-12 22:08:33","url":"/api/auth/login"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 访问令牌缺失","method":"GET","params":{},"query":{},"requestId":"4xxeycrqc5pzweb3inw40f","service":"email-system-backend","stack":"Error: 访问令牌缺失\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:18:13)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at requestLogger (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\utils\\logger.ts:84:3)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at urlencodedParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\urlencoded.js:68:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at jsonParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\json.js:100:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\express-rate-limit\\dist\\index.cjs:822:7\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\express-rate-limit\\dist\\index.cjs:704:5","timestamp":"2025-06-12 22:08:33","url":"/api/auth/me"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 无效的访问令牌","method":"GET","params":{},"query":{},"requestId":"z7343eevgcck7m033fanu","service":"email-system-backend","stack":"Error: 无效的访问令牌\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:44:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at requestLogger (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\utils\\logger.ts:84:3)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at urlencodedParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\urlencoded.js:68:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at jsonParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\json.js:100:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\express-rate-limit\\dist\\index.cjs:822:7\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\express-rate-limit\\dist\\index.cjs:704:5","timestamp":"2025-06-12 22:08:33","url":"/api/auth/me"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 访问令牌缺失","method":"POST","params":{},"query":{},"requestId":"lgy9fih6x9dcv8yf81bn","service":"email-system-backend","stack":"Error: 访问令牌缺失\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\auth.ts:18:13)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at requestLogger (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\utils\\logger.ts:84:3)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at urlencodedParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\urlencoded.js:77:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at jsonParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\json.js:109:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\express-rate-limit\\dist\\index.cjs:822:7\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\express-rate-limit\\dist\\index.cjs:704:5","timestamp":"2025-06-12 22:08:33","url":"/api/auth/logout"}
{"body":{},"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: 刷新令牌缺失","method":"POST","params":{},"query":{},"requestId":"zzbx2f1r4vdve14hxdr0c","service":"email-system-backend","stack":"Error: 刷新令牌缺失\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\controllers\\authController.ts:188:11\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\middleware\\errorHandler.ts:108:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at requestLogger (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\src\\utils\\logger.ts:84:3)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at urlencodedParser (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\types\\urlencoded.js:57:7)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\router\\index.js:291:5)\n    at C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\body-parser\\lib\\read.js:132:5\n    at AsyncResource.runInAsyncScope (node:async_hooks:206:9)\n    at invokeCallback (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\raw-body\\index.js:238:16)\n    at done (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\backend\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at endReadableNT (node:internal/streams/readable:1696:12)\n    at processTicksAndRejections (node:internal/process/task_queues:82:21)","timestamp":"2025-06-12 22:08:33","url":"/api/auth/refresh"}
