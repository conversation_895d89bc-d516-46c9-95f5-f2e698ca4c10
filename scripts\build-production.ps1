# 生产环境构建脚本 (PowerShell)
param(
    [string]$Service = "all",
    [switch]$Push = $false,
    [string]$Registry = "ghcr.io",
    [string]$Tag = "latest"
)

# 颜色定义
$Red = "Red"
$Green = "Green"
$Yellow = "Yellow"
$Blue = "Blue"

function Write-Log {
    param([string]$Message, [string]$Color = "White")
    Write-Host "[$(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')] $Message" -ForegroundColor $Color
}

function Write-Info { param([string]$Message) Write-Log "INFO: $Message" $Blue }
function Write-Success { param([string]$Message) Write-Log "SUCCESS: $Message" $Green }
function Write-Warning { param([string]$Message) Write-Log "WARNING: $Message" $Yellow }
function Write-Error { param([string]$Message) Write-Log "ERROR: $Message" $Red }

# 检查Docker是否安装
function Test-Docker {
    Write-Info "检查Docker环境..."
    
    if (-not (Get-Command docker -ErrorAction SilentlyContinue)) {
        Write-Error "Docker未安装或不在PATH中"
        exit 1
    }
    
    try {
        docker version | Out-Null
        Write-Success "Docker环境检查通过"
    }
    catch {
        Write-Error "Docker服务未运行"
        exit 1
    }
}

# 构建前端
function Build-Frontend {
    Write-Info "构建前端应用..."
    
    Push-Location frontend
    
    try {
        # 安装依赖
        Write-Info "安装前端依赖..."
        npm ci
        
        # 运行测试
        Write-Info "运行前端测试..."
        npm run test:run
        
        # 构建应用
        Write-Info "构建前端应用..."
        npm run build
        
        # 构建Docker镜像
        Write-Info "构建前端Docker镜像..."
        $imageName = "$Registry/email-system-frontend:$Tag"
        docker build -t $imageName .
        
        if ($Push) {
            Write-Info "推送前端镜像到注册表..."
            docker push $imageName
        }
        
        Write-Success "前端构建完成"
    }
    catch {
        Write-Error "前端构建失败: $_"
        exit 1
    }
    finally {
        Pop-Location
    }
}

# 构建后端
function Build-Backend {
    Write-Info "构建后端应用..."
    
    Push-Location backend
    
    try {
        # 安装依赖
        Write-Info "安装后端依赖..."
        npm ci
        
        # 运行测试
        Write-Info "运行后端测试..."
        npm test
        
        # 构建应用
        Write-Info "构建后端应用..."
        npm run build
        
        # 构建Docker镜像
        Write-Info "构建后端Docker镜像..."
        $imageName = "$Registry/email-system-backend:$Tag"
        docker build -t $imageName .
        
        if ($Push) {
            Write-Info "推送后端镜像到注册表..."
            docker push $imageName
        }
        
        Write-Success "后端构建完成"
    }
    catch {
        Write-Error "后端构建失败: $_"
        exit 1
    }
    finally {
        Pop-Location
    }
}

# 主函数
function Main {
    Write-Info "开始生产环境构建..."
    Write-Info "服务: $Service"
    Write-Info "标签: $Tag"
    Write-Info "推送: $Push"
    
    Test-Docker
    
    switch ($Service.ToLower()) {
        "frontend" {
            Build-Frontend
        }
        "backend" {
            Build-Backend
        }
        "all" {
            Build-Frontend
            Build-Backend
        }
        default {
            Write-Error "未知服务: $Service"
            Write-Info "可用服务: frontend, backend, all"
            exit 1
        }
    }
    
    Write-Success "构建完成!"
}

# 执行主函数
Main
