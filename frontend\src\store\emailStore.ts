import { create } from 'zustand';
import type { Em<PERSON>, Folder, ComposeEmailRequest, SearchParams } from '../types';
import { apiService } from '../services/api';

interface EmailState {
  emails: Email[];
  currentEmail: Email | null;
  folders: Folder[];
  currentFolder: Folder | null;
  selectedEmails: string[];
  isLoading: boolean;
  error: string | null;
  hasMore: boolean;
  page: number;
  searchParams: SearchParams | null;
  
  // Actions
  fetchEmails: (folderId?: string, reset?: boolean) => Promise<void>;
  fetchEmail: (id: string) => Promise<void>;
  sendEmail: (emailData: ComposeEmailRequest) => Promise<void>;
  markAsRead: (id: string) => Promise<void>;
  markAsUnread: (id: string) => Promise<void>;
  starEmail: (id: string) => Promise<void>;
  unstarEmail: (id: string) => Promise<void>;
  deleteEmail: (id: string) => Promise<void>;
  searchEmails: (params: SearchParams) => Promise<void>;
  fetchFolders: () => Promise<void>;
  setCurrentFolder: (folder: Folder | null) => void;
  setCurrentEmail: (email: Email | null) => void;
  selectEmail: (id: string) => void;
  unselectEmail: (id: string) => void;
  selectAllEmails: () => void;
  clearSelection: () => void;
  clearError: () => void;
  loadMore: () => Promise<void>;
}

export const useEmailStore = create<EmailState>((set, get) => ({
  emails: [],
  currentEmail: null,
  folders: [],
  currentFolder: null,
  selectedEmails: [],
  isLoading: false,
  error: null,
  hasMore: true,
  page: 1,
  searchParams: null,

  fetchEmails: async (folderId?: string, reset = false) => {
    try {
      const state = get();
      const currentPage = reset ? 1 : state.page;

      set({ isLoading: true, error: null });

      // 使用真实API
      const response = await apiService.getEmails({
        folderId,
        page: currentPage,
        limit: 20,
        search: state.searchParams || undefined,
      });

      const { data, hasNext } = response.data.data;

      set({
        emails: reset ? data : [...state.emails, ...data],
        hasMore: hasNext,
        page: currentPage,
        isLoading: false,
      });
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || '获取邮件失败';
      set({
        error: errorMessage,
        isLoading: false,
      });
    }
  },

  fetchEmail: async (id: string) => {
    try {
      set({ isLoading: true, error: null });
      
      const response = await apiService.getEmail(id);
      const email = response.data.data;
      
      set({
        currentEmail: email,
        isLoading: false,
      });
      
      // 如果邮件未读，标记为已读
      if (!email.isRead) {
        await get().markAsRead(id);
      }
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || '获取邮件详情失败';
      set({
        error: errorMessage,
        isLoading: false,
      });
    }
  },

  sendEmail: async (emailData: ComposeEmailRequest) => {
    try {
      set({ isLoading: true, error: null });
      
      await apiService.sendEmail(emailData);
      
      set({ isLoading: false });
      
      // 刷新邮件列表
      await get().fetchEmails(get().currentFolder?.id, true);
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || '发送邮件失败';
      set({
        error: errorMessage,
        isLoading: false,
      });
      throw error;
    }
  },

  markAsRead: async (id: string) => {
    try {
      await apiService.markAsRead(id);
      
      const state = get();
      set({
        emails: state.emails.map(email =>
          email.id === id ? { ...email, isRead: true } : email
        ),
        currentEmail: state.currentEmail?.id === id 
          ? { ...state.currentEmail, isRead: true }
          : state.currentEmail,
      });
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || '标记已读失败';
      set({ error: errorMessage });
    }
  },

  markAsUnread: async (id: string) => {
    try {
      await apiService.markAsUnread(id);
      
      const state = get();
      set({
        emails: state.emails.map(email =>
          email.id === id ? { ...email, isRead: false } : email
        ),
        currentEmail: state.currentEmail?.id === id 
          ? { ...state.currentEmail, isRead: false }
          : state.currentEmail,
      });
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || '标记未读失败';
      set({ error: errorMessage });
    }
  },

  starEmail: async (id: string) => {
    try {
      await apiService.starEmail(id);
      
      const state = get();
      set({
        emails: state.emails.map(email =>
          email.id === id ? { ...email, isStarred: true } : email
        ),
        currentEmail: state.currentEmail?.id === id 
          ? { ...state.currentEmail, isStarred: true }
          : state.currentEmail,
      });
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || '标星失败';
      set({ error: errorMessage });
    }
  },

  unstarEmail: async (id: string) => {
    try {
      await apiService.unstarEmail(id);
      
      const state = get();
      set({
        emails: state.emails.map(email =>
          email.id === id ? { ...email, isStarred: false } : email
        ),
        currentEmail: state.currentEmail?.id === id 
          ? { ...state.currentEmail, isStarred: false }
          : state.currentEmail,
      });
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || '取消标星失败';
      set({ error: errorMessage });
    }
  },

  deleteEmail: async (id: string) => {
    try {
      await apiService.deleteEmail(id);
      
      const state = get();
      set({
        emails: state.emails.filter(email => email.id !== id),
        selectedEmails: state.selectedEmails.filter(emailId => emailId !== id),
        currentEmail: state.currentEmail?.id === id ? null : state.currentEmail,
      });
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || '删除邮件失败';
      set({ error: errorMessage });
    }
  },

  searchEmails: async (params: SearchParams) => {
    try {
      set({ isLoading: true, error: null, searchParams: params });
      
      const response = await apiService.searchEmails(params);
      const { data } = response.data.data;
      
      set({
        emails: data,
        hasMore: false, // 搜索结果不支持分页
        page: 1,
        isLoading: false,
      });
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || '搜索邮件失败';
      set({
        error: errorMessage,
        isLoading: false,
      });
    }
  },

  fetchFolders: async () => {
    try {
      // 使用真实API
      const response = await apiService.getFolders();
      const folders = response.data.data;

      set({ folders });
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || '获取文件夹失败';
      set({ error: errorMessage });
    }
  },

  setCurrentFolder: (folder: Folder | null) => {
    set({ 
      currentFolder: folder,
      searchParams: null, // 切换文件夹时清除搜索参数
    });
  },

  setCurrentEmail: (email: Email | null) => {
    set({ currentEmail: email });
  },

  selectEmail: (id: string) => {
    const state = get();
    if (!state.selectedEmails.includes(id)) {
      set({ selectedEmails: [...state.selectedEmails, id] });
    }
  },

  unselectEmail: (id: string) => {
    const state = get();
    set({ selectedEmails: state.selectedEmails.filter(emailId => emailId !== id) });
  },

  selectAllEmails: () => {
    const state = get();
    set({ selectedEmails: state.emails.map(email => email.id) });
  },

  clearSelection: () => {
    set({ selectedEmails: [] });
  },

  clearError: () => {
    set({ error: null });
  },

  loadMore: async () => {
    const state = get();
    if (state.hasMore && !state.isLoading) {
      set({ page: state.page + 1 });
      await get().fetchEmails(state.currentFolder?.id, false);
    }
  },
}));
