import React from 'react';
import { <PERSON><PERSON>, But<PERSON>, <PERSON>, Typography } from 'antd';
import { 
  ExclamationCircleOutlined, 
  ReloadOutlined, 
  QuestionCircleOutlined,
  BugOutlined 
} from '@ant-design/icons';

const { Text, Link } = Typography;

// 错误类型枚举
export enum ErrorType {
  VALIDATION = 'VALIDATION',
  AUTHENTICATION = 'AUTHENTICATION',
  AUTHORIZATION = 'AUTHORIZATION',
  NOT_FOUND = 'NOT_FOUND',
  CONFLICT = 'CONFLICT',
  RATE_LIMIT = 'RATE_LIMIT',
  SERVER_ERROR = 'SERVER_ERROR',
  DATABASE_ERROR = 'DATABASE_ERROR',
  NETWORK_ERROR = 'NETWORK_ERROR',
}

// 错误代码枚举
export enum ErrorCode {
  INVALID_INPUT = 'INVALID_INPUT',
  MISSING_REQUIRED_FIELD = 'MISSING_REQUIRED_FIELD',
  INVALID_EMAIL_FORMAT = 'INVALID_EMAIL_FORMAT',
  INVALID_PASSWORD_FORMAT = 'INVALID_PASSWORD_FORMAT',
  INVALID_CREDENTIALS = 'INVALID_CREDENTIALS',
  TOKEN_EXPIRED = 'TOKEN_EXPIRED',
  TOKEN_INVALID = 'TOKEN_INVALID',
  TOKEN_MISSING = 'TOKEN_MISSING',
  INSUFFICIENT_PERMISSIONS = 'INSUFFICIENT_PERMISSIONS',
  ACCOUNT_DISABLED = 'ACCOUNT_DISABLED',
  RESOURCE_NOT_FOUND = 'RESOURCE_NOT_FOUND',
  RESOURCE_ALREADY_EXISTS = 'RESOURCE_ALREADY_EXISTS',
  INTERNAL_SERVER_ERROR = 'INTERNAL_SERVER_ERROR',
  DATABASE_CONNECTION_ERROR = 'DATABASE_CONNECTION_ERROR',
  EXTERNAL_SERVICE_ERROR = 'EXTERNAL_SERVICE_ERROR',
}

interface ErrorMessageProps {
  error?: string;
  errorType?: ErrorType;
  errorCode?: ErrorCode;
  details?: any;
  onRetry?: () => void;
  onDismiss?: () => void;
  showRetry?: boolean;
  showDismiss?: boolean;
  className?: string;
}

// 错误消息映射
const ERROR_MESSAGES: Record<ErrorCode, { title: string; description: string; suggestion: string }> = {
  [ErrorCode.INVALID_INPUT]: {
    title: '输入信息有误',
    description: '请检查您输入的信息是否正确',
    suggestion: '请修正输入内容后重试'
  },
  [ErrorCode.MISSING_REQUIRED_FIELD]: {
    title: '必填信息缺失',
    description: '请填写所有必需的信息',
    suggestion: '请完善表单内容'
  },
  [ErrorCode.INVALID_EMAIL_FORMAT]: {
    title: '邮箱格式错误',
    description: '请输入正确的邮箱地址格式',
    suggestion: '例如：<EMAIL>'
  },
  [ErrorCode.INVALID_PASSWORD_FORMAT]: {
    title: '密码格式错误',
    description: '密码不符合安全要求',
    suggestion: '密码应包含字母、数字，长度至少8位'
  },
  [ErrorCode.INVALID_CREDENTIALS]: {
    title: '登录信息错误',
    description: '用户名或密码不正确',
    suggestion: '请检查您的登录信息或重置密码'
  },
  [ErrorCode.TOKEN_EXPIRED]: {
    title: '登录已过期',
    description: '您的登录状态已过期，请重新登录',
    suggestion: '点击重新登录按钮'
  },
  [ErrorCode.TOKEN_INVALID]: {
    title: '登录状态异常',
    description: '登录验证失败，请重新登录',
    suggestion: '请重新登录您的账户'
  },
  [ErrorCode.TOKEN_MISSING]: {
    title: '未登录',
    description: '请先登录您的账户',
    suggestion: '点击登录按钮进行身份验证'
  },
  [ErrorCode.INSUFFICIENT_PERMISSIONS]: {
    title: '权限不足',
    description: '您没有执行此操作的权限',
    suggestion: '请联系管理员获取相应权限'
  },
  [ErrorCode.ACCOUNT_DISABLED]: {
    title: '账户已被禁用',
    description: '您的账户暂时无法使用',
    suggestion: '请联系客服了解详情'
  },
  [ErrorCode.RESOURCE_NOT_FOUND]: {
    title: '内容不存在',
    description: '您要查找的内容不存在或已被删除',
    suggestion: '请检查链接是否正确或返回上一页'
  },
  [ErrorCode.RESOURCE_ALREADY_EXISTS]: {
    title: '内容已存在',
    description: '该内容已经存在，无法重复创建',
    suggestion: '请使用不同的名称或标识'
  },
  [ErrorCode.INTERNAL_SERVER_ERROR]: {
    title: '服务器错误',
    description: '服务器遇到了意外错误',
    suggestion: '请稍后重试，如问题持续请联系技术支持'
  },
  [ErrorCode.DATABASE_CONNECTION_ERROR]: {
    title: '数据库连接错误',
    description: '无法连接到数据库服务',
    suggestion: '请稍后重试或联系技术支持'
  },
  [ErrorCode.EXTERNAL_SERVICE_ERROR]: {
    title: '外部服务错误',
    description: '依赖的外部服务暂时不可用',
    suggestion: '请稍后重试'
  },
};

const ErrorMessage: React.FC<ErrorMessageProps> = ({
  error,
  errorType,
  errorCode,
  details,
  onRetry,
  onDismiss,
  showRetry = true,
  showDismiss = true,
  className,
}) => {
  // 获取用户友好的错误信息
  const getErrorInfo = () => {
    if (errorCode && ERROR_MESSAGES[errorCode]) {
      return ERROR_MESSAGES[errorCode];
    }

    // 根据错误类型提供默认信息
    switch (errorType) {
      case ErrorType.VALIDATION:
        return {
          title: '输入验证失败',
          description: error || '请检查您的输入信息',
          suggestion: '请修正后重试'
        };
      case ErrorType.AUTHENTICATION:
        return {
          title: '身份验证失败',
          description: error || '登录信息验证失败',
          suggestion: '请重新登录'
        };
      case ErrorType.AUTHORIZATION:
        return {
          title: '权限不足',
          description: error || '您没有执行此操作的权限',
          suggestion: '请联系管理员'
        };
      case ErrorType.NOT_FOUND:
        return {
          title: '内容不存在',
          description: error || '请求的内容不存在',
          suggestion: '请检查链接或返回上一页'
        };
      case ErrorType.NETWORK_ERROR:
        return {
          title: '网络连接错误',
          description: error || '网络连接失败',
          suggestion: '请检查网络连接后重试'
        };
      default:
        return {
          title: '操作失败',
          description: error || '发生了未知错误',
          suggestion: '请稍后重试'
        };
    }
  };

  const errorInfo = getErrorInfo();
  
  // 根据错误类型确定Alert类型
  const getAlertType = (): 'error' | 'warning' | 'info' => {
    switch (errorType) {
      case ErrorType.VALIDATION:
      case ErrorType.NOT_FOUND:
        return 'warning';
      case ErrorType.AUTHENTICATION:
      case ErrorType.AUTHORIZATION:
        return 'info';
      default:
        return 'error';
    }
  };

  // 根据错误类型确定图标
  const getIcon = () => {
    switch (errorType) {
      case ErrorType.VALIDATION:
        return <ExclamationCircleOutlined />;
      case ErrorType.AUTHENTICATION:
      case ErrorType.AUTHORIZATION:
        return <QuestionCircleOutlined />;
      default:
        return <BugOutlined />;
    }
  };

  return (
    <Alert
      className={className}
      type={getAlertType()}
      icon={getIcon()}
      message={errorInfo.title}
      description={
        <Space direction="vertical" size="small" style={{ width: '100%' }}>
          <Text>{errorInfo.description}</Text>
          <Text type="secondary" style={{ fontSize: '12px' }}>
            💡 {errorInfo.suggestion}
          </Text>
          {details && process.env.NODE_ENV === 'development' && (
            <details style={{ marginTop: '8px' }}>
              <summary style={{ cursor: 'pointer', fontSize: '12px' }}>
                技术详情
              </summary>
              <pre style={{ 
                fontSize: '11px', 
                marginTop: '4px', 
                padding: '8px',
                backgroundColor: '#f5f5f5',
                borderRadius: '4px',
                overflow: 'auto',
                maxHeight: '100px'
              }}>
                {JSON.stringify(details, null, 2)}
              </pre>
            </details>
          )}
        </Space>
      }
      action={
        <Space>
          {showRetry && onRetry && (
            <Button size="small" icon={<ReloadOutlined />} onClick={onRetry}>
              重试
            </Button>
          )}
          {showDismiss && onDismiss && (
            <Button size="small" type="text" onClick={onDismiss}>
              关闭
            </Button>
          )}
        </Space>
      }
      closable={showDismiss}
      onClose={onDismiss}
    />
  );
};

export default ErrorMessage;
