/**
 * 基础功能验证测试
 * 验证核心组件和功能是否正常工作
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { BrowserRouter } from 'react-router-dom';
import React from 'react';

// 组件导入
import { useUIStore } from '../../store/uiStore';
import ErrorBoundary from '../../components/ErrorBoundary/ErrorBoundary';
import ErrorMessage from '../../components/ErrorBoundary/ErrorMessage';

// Mock API服务
vi.mock('../../services/api', () => ({
  apiService: {
    login: vi.fn(),
    getCurrentUser: vi.fn(),
    getEmails: vi.fn(),
    getFolders: vi.fn(),
    sendEmail: vi.fn(),
    updateEmail: vi.fn(),
    deleteEmail: vi.fn(),
  },
}));

// Mock Ant Design message
vi.mock('antd', async () => {
  const actual = await vi.importActual('antd');
  return {
    ...actual,
    message: {
      success: vi.fn(),
      error: vi.fn(),
      warning: vi.fn(),
      info: vi.fn(),
    },
  };
});

// 测试工具函数
const createTestQueryClient = () => {
  return new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
        gcTime: 0,
      },
      mutations: {
        retry: false,
      },
    },
  });
};

const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const queryClient = createTestQueryClient();
  
  return (
    <QueryClientProvider client={queryClient}>
      <BrowserRouter>
        <ErrorBoundary>
          {children}
        </ErrorBoundary>
      </BrowserRouter>
    </QueryClientProvider>
  );
};

describe('基础功能验证', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    // 重置Zustand store
    useUIStore.getState().clearSelection();
    useUIStore.getState().setCurrentFolder(null);
    useUIStore.getState().setCurrentEmail(null);
    useUIStore.getState().closeComposer();
  });

  describe('UI状态管理', () => {
    it('应该能够正确管理邮件选择状态', () => {
      const TestComponent = () => {
        const { 
          selectedEmails, 
          selectEmail, 
          unselectEmail, 
          clearSelection,
          toggleEmailSelection 
        } = useUIStore();

        return (
          <div>
            <div data-testid="selected-count">{selectedEmails.length}</div>
            <div data-testid="selected-emails">{selectedEmails.join(',')}</div>
            <button onClick={() => selectEmail('email-1')}>选择邮件1</button>
            <button onClick={() => selectEmail('email-2')}>选择邮件2</button>
            <button onClick={() => unselectEmail('email-1')}>取消选择邮件1</button>
            <button onClick={() => clearSelection()}>清除所有选择</button>
            <button onClick={() => toggleEmailSelection('email-3')}>切换邮件3</button>
          </div>
        );
      };

      render(<TestComponent />, { wrapper: TestWrapper });

      // 初始状态
      expect(screen.getByTestId('selected-count')).toHaveTextContent('0');

      // 选择邮件
      fireEvent.click(screen.getByText('选择邮件1'));
      fireEvent.click(screen.getByText('选择邮件2'));
      
      expect(screen.getByTestId('selected-count')).toHaveTextContent('2');
      expect(screen.getByTestId('selected-emails')).toHaveTextContent('email-1,email-2');

      // 取消选择
      fireEvent.click(screen.getByText('取消选择邮件1'));
      expect(screen.getByTestId('selected-count')).toHaveTextContent('1');
      expect(screen.getByTestId('selected-emails')).toHaveTextContent('email-2');

      // 切换选择
      fireEvent.click(screen.getByText('切换邮件3'));
      expect(screen.getByTestId('selected-count')).toHaveTextContent('2');
      expect(screen.getByTestId('selected-emails')).toHaveTextContent('email-2,email-3');

      // 清除所有选择
      fireEvent.click(screen.getByText('清除所有选择'));
      expect(screen.getByTestId('selected-count')).toHaveTextContent('0');
    });

    it('应该能够正确管理编写邮件状态', () => {
      const TestComponent = () => {
        const { 
          isComposerOpen, 
          composerMode, 
          openComposer, 
          closeComposer 
        } = useUIStore();

        return (
          <div>
            <div data-testid="composer-open">{isComposerOpen.toString()}</div>
            <div data-testid="composer-mode">{composerMode || 'null'}</div>
            <button onClick={() => openComposer('new')}>新建邮件</button>
            <button onClick={() => openComposer('reply')}>回复邮件</button>
            <button onClick={() => closeComposer()}>关闭编写器</button>
          </div>
        );
      };

      render(<TestComponent />, { wrapper: TestWrapper });

      // 初始状态
      expect(screen.getByTestId('composer-open')).toHaveTextContent('false');
      expect(screen.getByTestId('composer-mode')).toHaveTextContent('null');

      // 打开新建邮件
      fireEvent.click(screen.getByText('新建邮件'));
      expect(screen.getByTestId('composer-open')).toHaveTextContent('true');
      expect(screen.getByTestId('composer-mode')).toHaveTextContent('new');

      // 切换到回复模式
      fireEvent.click(screen.getByText('回复邮件'));
      expect(screen.getByTestId('composer-mode')).toHaveTextContent('reply');

      // 关闭编写器
      fireEvent.click(screen.getByText('关闭编写器'));
      expect(screen.getByTestId('composer-open')).toHaveTextContent('false');
      expect(screen.getByTestId('composer-mode')).toHaveTextContent('null');
    });

    it('应该能够正确管理搜索状态', () => {
      const TestComponent = () => {
        const { 
          isSearchMode, 
          searchParams, 
          setSearchParams, 
          exitSearchMode 
        } = useUIStore();

        return (
          <div>
            <div data-testid="search-mode">{isSearchMode.toString()}</div>
            <div data-testid="search-query">{searchParams?.query || 'null'}</div>
            <button onClick={() => setSearchParams({ query: '测试' })}>设置搜索</button>
            <button onClick={() => exitSearchMode()}>退出搜索</button>
          </div>
        );
      };

      render(<TestComponent />, { wrapper: TestWrapper });

      // 初始状态
      expect(screen.getByTestId('search-mode')).toHaveTextContent('false');
      expect(screen.getByTestId('search-query')).toHaveTextContent('null');

      // 设置搜索
      fireEvent.click(screen.getByText('设置搜索'));
      expect(screen.getByTestId('search-mode')).toHaveTextContent('true');
      expect(screen.getByTestId('search-query')).toHaveTextContent('测试');

      // 退出搜索
      fireEvent.click(screen.getByText('退出搜索'));
      expect(screen.getByTestId('search-mode')).toHaveTextContent('false');
      expect(screen.getByTestId('search-query')).toHaveTextContent('null');
    });
  });

  describe('错误处理组件', () => {
    it('应该能够显示错误边界', () => {
      const ThrowError = ({ shouldThrow }: { shouldThrow: boolean }) => {
        if (shouldThrow) {
          throw new Error('测试错误');
        }
        return <div>正常内容</div>;
      };

      const TestComponent = () => {
        const [shouldThrow, setShouldThrow] = React.useState(false);
        
        return (
          <div>
            <button onClick={() => setShouldThrow(true)}>触发错误</button>
            <ErrorBoundary>
              <ThrowError shouldThrow={shouldThrow} />
            </ErrorBoundary>
          </div>
        );
      };

      render(<TestComponent />, { wrapper: TestWrapper });

      // 初始状态
      expect(screen.getByText('正常内容')).toBeInTheDocument();

      // 触发错误
      fireEvent.click(screen.getByText('触发错误'));
      
      // 应该显示错误页面
      expect(screen.getByText('页面出现了错误')).toBeInTheDocument();
      expect(screen.getByText('重试')).toBeInTheDocument();
    });

    it('应该能够显示错误消息组件', () => {
      const TestComponent = () => {
        const [showError, setShowError] = React.useState(false);
        
        return (
          <div>
            <button onClick={() => setShowError(true)}>显示错误</button>
            {showError && (
              <ErrorMessage
                error="这是一个测试错误消息"
                onDismiss={() => setShowError(false)}
              />
            )}
          </div>
        );
      };

      render(<TestComponent />, { wrapper: TestWrapper });

      // 显示错误
      fireEvent.click(screen.getByText('显示错误'));
      
      // 应该显示错误消息
      expect(screen.getByText('这是一个测试错误消息')).toBeInTheDocument();
      expect(screen.getByText('关闭')).toBeInTheDocument();

      // 关闭错误消息
      fireEvent.click(screen.getByText('关闭'));
      expect(screen.queryByText('这是一个测试错误消息')).not.toBeInTheDocument();
    });
  });

  describe('状态持久化', () => {
    it('应该能够持久化UI设置', () => {
      // Mock localStorage
      const localStorageMock = {
        getItem: vi.fn(),
        setItem: vi.fn(),
        removeItem: vi.fn(),
        clear: vi.fn(),
      };
      
      Object.defineProperty(window, 'localStorage', {
        value: localStorageMock,
        writable: true,
      });

      const TestComponent = () => {
        const { viewMode, setViewMode, sortBy, setSortBy } = useUIStore();

        return (
          <div>
            <div data-testid="view-mode">{viewMode}</div>
            <div data-testid="sort-by">{sortBy}</div>
            <button onClick={() => setViewMode('grid')}>网格视图</button>
            <button onClick={() => setSortBy('sender')}>按发件人排序</button>
          </div>
        );
      };

      render(<TestComponent />, { wrapper: TestWrapper });

      // 更改设置
      fireEvent.click(screen.getByText('网格视图'));
      fireEvent.click(screen.getByText('按发件人排序'));

      expect(screen.getByTestId('view-mode')).toHaveTextContent('grid');
      expect(screen.getByTestId('sort-by')).toHaveTextContent('sender');

      // 验证localStorage被调用（由于Zustand的persist中间件）
      // 注意：实际的localStorage调用可能是异步的
    });
  });

  describe('组件渲染', () => {
    it('应该能够正确渲染基础组件', () => {
      const TestComponent = () => (
        <div>
          <h1>邮件系统</h1>
          <p>这是一个测试组件</p>
        </div>
      );

      render(<TestComponent />, { wrapper: TestWrapper });

      expect(screen.getByText('邮件系统')).toBeInTheDocument();
      expect(screen.getByText('这是一个测试组件')).toBeInTheDocument();
    });

    it('应该能够处理用户交互', async () => {
      const user = userEvent.setup();
      
      const TestComponent = () => {
        const [count, setCount] = React.useState(0);
        
        return (
          <div>
            <div data-testid="count">{count}</div>
            <button onClick={() => setCount(count + 1)}>增加</button>
          </div>
        );
      };

      render(<TestComponent />, { wrapper: TestWrapper });

      expect(screen.getByTestId('count')).toHaveTextContent('0');

      await user.click(screen.getByText('增加'));
      expect(screen.getByTestId('count')).toHaveTextContent('1');

      await user.click(screen.getByText('增加'));
      expect(screen.getByTestId('count')).toHaveTextContent('2');
    });
  });
});
