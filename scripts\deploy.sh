#!/bin/bash

# 邮件系统部署脚本
# 使用方法: ./deploy.sh [environment] [service]
# 环境: development, staging, production
# 服务: all, frontend, backend, database

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查依赖
check_dependencies() {
    log_info "检查依赖..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose 未安装"
        exit 1
    fi
    
    log_success "依赖检查完成"
}

# 构建镜像
build_images() {
    local service=$1
    log_info "构建 $service 镜像..."
    
    case $service in
        "frontend")
            docker build -t email-frontend:latest ./frontend
            ;;
        "backend")
            docker build -t email-backend:latest ./backend
            ;;
        "all")
            docker build -t email-frontend:latest ./frontend
            docker build -t email-backend:latest ./backend
            ;;
        *)
            log_error "未知服务: $service"
            exit 1
            ;;
    esac
    
    log_success "$service 镜像构建完成"
}

# 部署到开发环境
deploy_development() {
    local service=$1
    log_info "部署到开发环境..."
    
    # 启动开发环境
    if [ "$service" = "all" ]; then
        docker-compose -f docker-compose.yml -f docker-compose.dev.yml up -d
    else
        docker-compose -f docker-compose.yml -f docker-compose.dev.yml up -d $service
    fi
    
    log_success "开发环境部署完成"
}

# 部署到测试环境
deploy_staging() {
    local service=$1
    log_info "部署到测试环境..."
    
    # 构建镜像
    build_images $service
    
    # 启动测试环境
    if [ "$service" = "all" ]; then
        docker-compose -f docker-compose.yml -f docker-compose.staging.yml up -d
    else
        docker-compose -f docker-compose.yml -f docker-compose.staging.yml up -d $service
    fi
    
    # 运行健康检查
    health_check
    
    log_success "测试环境部署完成"
}

# 部署到生产环境
deploy_production() {
    local service=$1
    log_info "部署到生产环境..."
    
    # 确认部署
    read -p "确认部署到生产环境? (y/N): " confirm
    if [[ $confirm != [yY] ]]; then
        log_warning "部署已取消"
        exit 0
    fi
    
    # 备份数据库
    backup_database
    
    # 构建镜像
    build_images $service
    
    # 启动生产环境
    if [ "$service" = "all" ]; then
        docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d
    else
        docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d $service
    fi
    
    # 运行健康检查
    health_check
    
    # 运行烟雾测试
    smoke_test
    
    log_success "生产环境部署完成"
}

# 数据库备份
backup_database() {
    log_info "备份数据库..."
    
    local backup_file="backup_$(date +%Y%m%d_%H%M%S).sql"
    
    docker-compose exec postgres pg_dump -U emailuser emaildb > "./backups/$backup_file"
    
    log_success "数据库备份完成: $backup_file"
}

# 健康检查
health_check() {
    log_info "运行健康检查..."
    
    local max_attempts=30
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if curl -f http://localhost/health &> /dev/null; then
            log_success "健康检查通过"
            return 0
        fi
        
        log_info "等待服务启动... ($attempt/$max_attempts)"
        sleep 10
        ((attempt++))
    done
    
    log_error "健康检查失败"
    return 1
}

# 烟雾测试
smoke_test() {
    log_info "运行烟雾测试..."
    
    # 测试前端
    if curl -f http://localhost/ &> /dev/null; then
        log_success "前端服务正常"
    else
        log_error "前端服务异常"
        return 1
    fi
    
    # 测试后端API
    if curl -f http://localhost/api/health &> /dev/null; then
        log_success "后端API正常"
    else
        log_error "后端API异常"
        return 1
    fi
    
    log_success "烟雾测试通过"
}

# 回滚
rollback() {
    log_warning "开始回滚..."
    
    # 停止当前服务
    docker-compose down
    
    # 恢复数据库备份
    local latest_backup=$(ls -t ./backups/*.sql | head -n1)
    if [ -n "$latest_backup" ]; then
        log_info "恢复数据库备份: $latest_backup"
        docker-compose exec postgres psql -U emailuser -d emaildb < "$latest_backup"
    fi
    
    # 启动上一个版本
    docker-compose up -d
    
    log_success "回滚完成"
}

# 清理
cleanup() {
    log_info "清理资源..."
    
    # 清理未使用的镜像
    docker image prune -f
    
    # 清理未使用的容器
    docker container prune -f
    
    # 清理未使用的网络
    docker network prune -f
    
    log_success "清理完成"
}

# 显示帮助
show_help() {
    echo "邮件系统部署脚本"
    echo ""
    echo "使用方法:"
    echo "  $0 [environment] [service]"
    echo ""
    echo "环境:"
    echo "  development  - 开发环境"
    echo "  staging      - 测试环境"
    echo "  production   - 生产环境"
    echo ""
    echo "服务:"
    echo "  all          - 所有服务"
    echo "  frontend     - 前端服务"
    echo "  backend      - 后端服务"
    echo "  database     - 数据库服务"
    echo ""
    echo "其他命令:"
    echo "  rollback     - 回滚到上一个版本"
    echo "  cleanup      - 清理资源"
    echo "  health       - 健康检查"
    echo ""
    echo "示例:"
    echo "  $0 development all"
    echo "  $0 production backend"
    echo "  $0 rollback"
}

# 主函数
main() {
    local environment=$1
    local service=${2:-all}
    
    # 创建必要的目录
    mkdir -p ./backups
    mkdir -p ./logs
    
    case $environment in
        "development")
            check_dependencies
            deploy_development $service
            ;;
        "staging")
            check_dependencies
            deploy_staging $service
            ;;
        "production")
            check_dependencies
            deploy_production $service
            ;;
        "rollback")
            rollback
            ;;
        "cleanup")
            cleanup
            ;;
        "health")
            health_check
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        *)
            log_error "未知环境: $environment"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
