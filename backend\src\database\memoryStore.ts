import { User, Email, Folder, Contact, MemoryStore } from '../types';
import { v4 as uuidv4 } from 'uuid';
import bcrypt from 'bcryptjs';

// 内存数据存储
class MemoryDatabase {
  private store: MemoryStore = {
    users: new Map(),
    emails: new Map(),
    folders: new Map(),
    contacts: new Map(),
  };

  constructor() {
    this.initializeData();
  }

  // 初始化示例数据
  private async initializeData() {
    // 创建示例用户
    const hashedPassword = await bcrypt.hash('123456', 12);
    const testUser: User = {
      id: 'user-1',
      email: '<EMAIL>',
      username: 'testuser',
      displayName: '测试用户',
      passwordHash: hashedPassword,
      createdAt: new Date(),
      updatedAt: new Date(),
      isActive: true,
    };
    this.store.users.set(testUser.id, testUser);

    // 创建默认文件夹
    const folders: Folder[] = [
      {
        id: 'inbox',
        userId: 'user-1',
        name: '收件箱',
        type: 'inbox',
        emailCount: 3,
        unreadCount: 2,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        id: 'sent',
        userId: 'user-1',
        name: '已发送',
        type: 'sent',
        emailCount: 1,
        unreadCount: 0,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        id: 'draft',
        userId: 'user-1',
        name: '草稿箱',
        type: 'draft',
        emailCount: 1,
        unreadCount: 0,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        id: 'trash',
        userId: 'user-1',
        name: '垃圾箱',
        type: 'trash',
        emailCount: 0,
        unreadCount: 0,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    ];

    folders.forEach(folder => {
      this.store.folders.set(folder.id, folder);
    });

    // 创建示例邮件
    const emails: Email[] = [
      {
        id: 'email-1',
        messageId: 'msg-1',
        userId: 'user-1',
        folderId: 'inbox',
        subject: '欢迎使用邮箱系统',
        senderEmail: '<EMAIL>',
        senderName: '系统管理员',
        recipients: ['<EMAIL>'],
        ccRecipients: [],
        bccRecipients: [],
        contentText: '欢迎使用我们的邮箱系统！这是一个功能完整的邮件管理平台。',
        contentHtml: '<p>欢迎使用我们的邮箱系统！这是一个功能完整的邮件管理平台。</p>',
        attachments: [],
        isRead: false,
        isStarred: true,
        isDeleted: false,
        receivedAt: new Date(Date.now() - 1000 * 60 * 30), // 30分钟前
        createdAt: new Date(Date.now() - 1000 * 60 * 30),
        updatedAt: new Date(Date.now() - 1000 * 60 * 30),
      },
      {
        id: 'email-2',
        messageId: 'msg-2',
        userId: 'user-1',
        folderId: 'inbox',
        subject: '项目进度更新',
        senderEmail: '<EMAIL>',
        senderName: '项目经理',
        recipients: ['<EMAIL>'],
        ccRecipients: ['<EMAIL>'],
        bccRecipients: [],
        contentText: '本周项目进度顺利，已完成主要功能开发。',
        contentHtml: '<p>本周项目进度顺利，已完成主要功能开发。</p><ul><li>前端界面完成</li><li>后端API开发</li><li>数据库设计</li></ul>',
        attachments: [
          {
            id: 'att-1',
            filename: '项目报告.pdf',
            contentType: 'application/pdf',
            size: 1024000,
            url: '/attachments/report.pdf',
          },
        ],
        isRead: true,
        isStarred: false,
        isDeleted: false,
        receivedAt: new Date(Date.now() - 1000 * 60 * 60 * 2), // 2小时前
        createdAt: new Date(Date.now() - 1000 * 60 * 60 * 2),
        updatedAt: new Date(Date.now() - 1000 * 60 * 60 * 2),
      },
      {
        id: 'email-3',
        messageId: 'msg-3',
        userId: 'user-1',
        folderId: 'inbox',
        subject: '会议邀请：周例会',
        senderEmail: '<EMAIL>',
        senderName: '会议助手',
        recipients: ['<EMAIL>'],
        ccRecipients: [],
        bccRecipients: [],
        contentText: '邀请您参加本周的例会，时间：周五下午2点。',
        contentHtml: '<p>邀请您参加本周的例会</p><p><strong>时间：</strong>周五下午2点</p><p><strong>地点：</strong>会议室A</p>',
        attachments: [],
        isRead: false,
        isStarred: false,
        isDeleted: false,
        receivedAt: new Date(Date.now() - 1000 * 60 * 60 * 6), // 6小时前
        createdAt: new Date(Date.now() - 1000 * 60 * 60 * 6),
        updatedAt: new Date(Date.now() - 1000 * 60 * 60 * 6),
      },
      {
        id: 'email-4',
        messageId: 'msg-4',
        userId: 'user-1',
        folderId: 'sent',
        subject: '回复：项目需求确认',
        senderEmail: '<EMAIL>',
        senderName: '测试用户',
        recipients: ['<EMAIL>'],
        ccRecipients: [],
        bccRecipients: [],
        contentText: '感谢您的需求确认，我们将按照讨论的方案进行开发。',
        contentHtml: '<p>感谢您的需求确认，我们将按照讨论的方案进行开发。</p>',
        attachments: [],
        isRead: true,
        isStarred: false,
        isDeleted: false,
        sentAt: new Date(Date.now() - 1000 * 60 * 60 * 24), // 1天前
        createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24),
        updatedAt: new Date(Date.now() - 1000 * 60 * 60 * 24),
      },
      {
        id: 'email-5',
        messageId: 'msg-5',
        userId: 'user-1',
        folderId: 'draft',
        subject: '待发送：月度总结报告',
        senderEmail: '<EMAIL>',
        senderName: '测试用户',
        recipients: ['<EMAIL>'],
        ccRecipients: [],
        bccRecipients: [],
        contentText: '本月工作总结...',
        contentHtml: '<p>本月工作总结...</p>',
        attachments: [],
        isRead: true,
        isStarred: false,
        isDeleted: false,
        createdAt: new Date(Date.now() - 1000 * 60 * 60 * 12),
        updatedAt: new Date(Date.now() - 1000 * 60 * 60 * 12),
      },
    ];

    emails.forEach(email => {
      this.store.emails.set(email.id, email);
    });

    // 创建示例联系人
    const contacts: Contact[] = [
      {
        id: 'contact-1',
        userId: 'user-1',
        name: '张三',
        email: '<EMAIL>',
        phone: '13800138000',
        company: 'ABC公司',
        notes: '项目合作伙伴',
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        id: 'contact-2',
        userId: 'user-1',
        name: '李四',
        email: '<EMAIL>',
        phone: '13900139000',
        company: 'XYZ公司',
        notes: '技术顾问',
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    ];

    contacts.forEach(contact => {
      this.store.contacts.set(contact.id, contact);
    });
  }

  // 获取存储实例
  getStore(): MemoryStore {
    return this.store;
  }

  // 用户相关方法
  async createUser(userData: Omit<User, 'id' | 'createdAt' | 'updatedAt'>): Promise<User> {
    const user: User = {
      ...userData,
      id: uuidv4(),
      createdAt: new Date(),
      updatedAt: new Date(),
    };
    this.store.users.set(user.id, user);
    return user;
  }

  async getUserById(id: string): Promise<User | undefined> {
    return this.store.users.get(id);
  }

  async getUserByEmail(email: string): Promise<User | undefined> {
    for (const user of this.store.users.values()) {
      if (user.email === email) {
        return user;
      }
    }
    return undefined;
  }

  async getUserByUsername(username: string): Promise<User | undefined> {
    for (const user of this.store.users.values()) {
      if (user.username === username) {
        return user;
      }
    }
    return undefined;
  }

  // 邮件相关方法
  async createEmail(emailData: Omit<Email, 'id' | 'createdAt' | 'updatedAt'>): Promise<Email> {
    const email: Email = {
      ...emailData,
      id: uuidv4(),
      createdAt: new Date(),
      updatedAt: new Date(),
    };
    this.store.emails.set(email.id, email);
    return email;
  }

  async getEmailById(id: string): Promise<Email | undefined> {
    return this.store.emails.get(id);
  }

  async getEmailsByUserId(userId: string, folderId?: string): Promise<Email[]> {
    const emails = Array.from(this.store.emails.values())
      .filter(email => email.userId === userId && !email.isDeleted);
    
    if (folderId) {
      return emails.filter(email => email.folderId === folderId);
    }
    
    return emails;
  }

  async updateEmail(id: string, updates: Partial<Email>): Promise<Email | undefined> {
    const email = this.store.emails.get(id);
    if (!email) return undefined;

    const updatedEmail = {
      ...email,
      ...updates,
      updatedAt: new Date(),
    };
    
    this.store.emails.set(id, updatedEmail);
    return updatedEmail;
  }

  // 文件夹相关方法
  async getFoldersByUserId(userId: string): Promise<Folder[]> {
    return Array.from(this.store.folders.values())
      .filter(folder => folder.userId === userId);
  }

  async getFolderById(id: string): Promise<Folder | undefined> {
    return this.store.folders.get(id);
  }

  // 联系人相关方法
  async getContactsByUserId(userId: string): Promise<Contact[]> {
    return Array.from(this.store.contacts.values())
      .filter(contact => contact.userId === userId);
  }
}

// 导出单例实例
export const memoryDB = new MemoryDatabase();
