# Dovecot 主配置文件
# 邮件系统 - 接收邮件服务器配置

# ================================
# 基本设置
# ================================
# 协议支持
protocols = imap pop3 lmtp sieve

# 监听地址
listen = *, ::

# 基础目录
base_dir = /var/run/dovecot/

# 实例名称
instance_name = dovecot

# ================================
# 登录设置
# ================================
# 禁用明文认证 (除非使用 TLS)
disable_plaintext_auth = yes

# 认证机制
auth_mechanisms = plain login

# 认证详细日志
auth_verbose = yes
auth_verbose_passwords = no
auth_debug = no
auth_debug_passwords = no

# ================================
# 邮箱设置
# ================================
# 邮箱格式 (Maildir)
mail_location = maildir:/var/mail/vhosts/%d/%n

# 邮箱用户和组
mail_uid = vmail
mail_gid = vmail

# 首次登录时自动创建邮箱
mail_auto_create = yes

# 邮箱权限
mail_access_groups = vmail

# ================================
# SSL/TLS 设置
# ================================
# SSL 支持
ssl = required

# SSL 证书
ssl_cert = </etc/ssl/certs/dovecot.pem
ssl_key = </etc/ssl/private/dovecot.pem

# SSL 协议
ssl_min_protocol = TLSv1.2
ssl_cipher_list = ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-AES256-GCM-SHA384:DHE-RSA-AES128-GCM-SHA256:DHE-DSS-AES128-GCM-SHA256:kEDH+AESGCM:ECDHE-RSA-AES128-SHA256:ECDHE-ECDSA-AES128-SHA256:ECDHE-RSA-AES128-SHA:ECDHE-ECDSA-AES128-SHA:ECDHE-RSA-AES256-SHA384:ECDHE-ECDSA-AES256-SHA384:ECDHE-RSA-AES256-SHA:ECDHE-ECDSA-AES256-SHA:DHE-RSA-AES128-SHA256:DHE-RSA-AES128-SHA:DHE-DSS-AES128-SHA256:DHE-RSA-AES256-SHA256:DHE-DSS-AES256-SHA:DHE-RSA-AES256-SHA:!aNULL:!eNULL:!EXPORT:!DES:!RC4:!3DES:!MD5:!PSK

# SSL 选项
ssl_prefer_server_ciphers = yes
ssl_dh = </etc/dovecot/dh.pem

# ================================
# 日志设置
# ================================
# 日志文件
log_path = /var/log/dovecot/dovecot.log
info_log_path = /var/log/dovecot/dovecot-info.log
debug_log_path = /var/log/dovecot/dovecot-debug.log

# 日志时间戳
log_timestamp = "%Y-%m-%d %H:%M:%S "

# ================================
# 服务设置
# ================================
# IMAP 设置
service imap-login {
  inet_listener imap {
    port = 143
  }
  inet_listener imaps {
    port = 993
    ssl = yes
  }
  
  # 进程设置
  process_min_avail = 0
  process_limit = 100
  
  # 连接设置
  client_limit = 1
  service_count = 1
  
  # 用户设置
  user = dovecot
  group = 
  
  # 监听设置
  chroot = login
  
  # 空闲超时
  idle_kill = 0
}

# POP3 设置
service pop3-login {
  inet_listener pop3 {
    port = 110
  }
  inet_listener pop3s {
    port = 995
    ssl = yes
  }
  
  # 进程设置
  process_min_avail = 0
  process_limit = 100
  
  # 连接设置
  client_limit = 1
  service_count = 1
  
  # 用户设置
  user = dovecot
  group = 
  
  # 监听设置
  chroot = login
}

# LMTP 设置 (本地邮件传输)
service lmtp {
  unix_listener /var/spool/postfix/private/dovecot-lmtp {
    mode = 0600
    user = postfix
    group = postfix
  }
  
  # 进程设置
  process_min_avail = 0
  process_limit = 100
  
  # 连接设置
  client_limit = 1
  service_count = 0
  
  # 用户设置
  user = vmail
  group = vmail
}

# 认证服务
service auth {
  # Postfix SMTP 认证
  unix_listener /var/spool/postfix/private/auth {
    mode = 0666
    user = postfix
    group = postfix
  }
  
  # 认证工作进程
  unix_listener auth-userdb {
    mode = 0600
    user = vmail
    group = vmail
  }
  
  # 认证主进程
  unix_listener auth-master {
    mode = 0600
    user = vmail
    group = vmail
  }
  
  # 进程设置
  user = dovecot
  group = 
}

# 字典服务 (配额等)
service dict {
  unix_listener dict {
    mode = 0600
    user = vmail
    group = vmail
  }
}

# ================================
# 协议设置
# ================================
# IMAP 协议设置
protocol imap {
  # 邮箱插件
  mail_plugins = $mail_plugins imap_quota
  
  # IMAP 特定设置
  imap_max_line_length = 64k
  imap_idle_notify_interval = 2 mins
  imap_client_workarounds = delay-newmail tb-extra-mailbox-sep
}

# POP3 协议设置
protocol pop3 {
  # 邮箱插件
  mail_plugins = $mail_plugins
  
  # POP3 特定设置
  pop3_uidl_format = %08Xu%08Xv
  pop3_client_workarounds = outlook-no-nuls oe-ns-eoh
}

# LMTP 协议设置
protocol lmtp {
  # 邮箱插件
  mail_plugins = $mail_plugins sieve
  
  # 投递设置
  lmtp_save_to_detail_mailbox = yes
}

# ================================
# 插件设置
# ================================
# 全局邮箱插件
mail_plugins = notify replication quota

# 配额插件设置
plugin {
  quota = maildir:User quota
  quota_rule = *:storage=1G
  quota_rule2 = Trash:storage=+100M
  quota_warning = storage=95%% quota-warning 95 %u
  quota_warning2 = storage=80%% quota-warning 80 %u
}

# Sieve 脚本设置
plugin {
  sieve = file:~/sieve;active=~/.dovecot.sieve
  sieve_global_extensions = +vnd.dovecot.pipe +vnd.dovecot.environment
}

# ================================
# 包含其他配置文件
# ================================
!include auth-sql.conf.ext
!include conf.d/*.conf
