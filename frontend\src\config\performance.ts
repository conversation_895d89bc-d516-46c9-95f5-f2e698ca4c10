/**
 * 性能优化配置
 * 集中管理所有性能相关的配置参数
 */

// React Query 性能配置
export const QUERY_CONFIG = {
  // 缓存时间配置 (毫秒)
  CACHE_TIME: {
    REALTIME: 0,           // 实时数据，不缓存
    SHORT: 1 * 60 * 1000,  // 1分钟 - 频繁变化的数据
    MEDIUM: 5 * 60 * 1000, // 5分钟 - 一般数据
    LONG: 30 * 60 * 1000,  // 30分钟 - 相对稳定的数据
    STATIC: 60 * 60 * 1000, // 1小时 - 静态数据
  },
  
  // 数据新鲜度配置 (毫秒)
  STALE_TIME: {
    REALTIME: 0,
    SHORT: 30 * 1000,      // 30秒
    MEDIUM: 2 * 60 * 1000, // 2分钟
    LONG: 10 * 60 * 1000,  // 10分钟
    STATIC: 30 * 60 * 1000, // 30分钟
  },
  
  // 重试配置
  RETRY: {
    DEFAULT: 3,
    CRITICAL: 5,
    NONE: 0,
  },
  
  // 重试延迟 (毫秒)
  RETRY_DELAY: {
    MIN: 1000,
    MAX: 30000,
    MULTIPLIER: 2,
  },
} as const;

// 分页配置
export const PAGINATION_CONFIG = {
  DEFAULT_PAGE_SIZE: 20,
  MAX_PAGE_SIZE: 100,
  INFINITE_SCROLL_THRESHOLD: 0.8, // 滚动到80%时加载更多
  PREFETCH_PAGES: 1, // 预取下一页
} as const;

// 虚拟滚动配置
export const VIRTUAL_SCROLL_CONFIG = {
  ITEM_HEIGHT: 80,        // 邮件项高度
  OVERSCAN: 5,           // 额外渲染的项目数
  BUFFER_SIZE: 10,       // 缓冲区大小
  THRESHOLD: 100,        // 启用虚拟滚动的最小项目数
} as const;

// 防抖和节流配置 (毫秒)
export const DEBOUNCE_CONFIG = {
  SEARCH: 300,           // 搜索输入防抖
  RESIZE: 100,           // 窗口大小调整防抖
  SCROLL: 16,            // 滚动节流 (60fps)
  AUTO_SAVE: 1000,       // 自动保存防抖
  API_CALL: 500,         // API调用防抖
} as const;

// 预取配置
export const PREFETCH_CONFIG = {
  EMAIL_HOVER_DELAY: 200,    // 邮件悬停预取延迟
  FOLDER_SWITCH_DELAY: 100,  // 文件夹切换预取延迟
  SEARCH_RESULT_DELAY: 300,  // 搜索结果预取延迟
  MAX_PREFETCH_SIZE: 5,      // 最大预取数量
} as const;

// 图片和附件优化配置
export const MEDIA_CONFIG = {
  IMAGE_LAZY_LOAD_THRESHOLD: 100, // 图片懒加载阈值 (px)
  MAX_IMAGE_SIZE: 2 * 1024 * 1024, // 最大图片大小 (2MB)
  THUMBNAIL_SIZE: 150,             // 缩略图大小
  ATTACHMENT_PREVIEW_SIZE: 1024 * 1024, // 附件预览大小限制 (1MB)
} as const;

// 内存管理配置
export const MEMORY_CONFIG = {
  MAX_CACHE_SIZE: 50 * 1024 * 1024,  // 最大缓存大小 (50MB)
  CLEANUP_INTERVAL: 5 * 60 * 1000,   // 清理间隔 (5分钟)
  MAX_INACTIVE_TIME: 10 * 60 * 1000, // 最大非活跃时间 (10分钟)
  GC_THRESHOLD: 0.8,                 // 垃圾回收阈值 (80%)
} as const;

// 网络优化配置
export const NETWORK_CONFIG = {
  REQUEST_TIMEOUT: 30000,        // 请求超时 (30秒)
  CONCURRENT_REQUESTS: 6,        // 最大并发请求数
  RETRY_AFTER_DELAY: 1000,       // 重试延迟
  BATCH_SIZE: 10,                // 批量操作大小
  COMPRESSION_THRESHOLD: 1024,   // 压缩阈值 (1KB)
} as const;

// Bundle 分割配置
export const BUNDLE_CONFIG = {
  CHUNK_SIZE_WARNING: 500 * 1024,   // Chunk大小警告阈值 (500KB)
  MAX_CHUNK_SIZE: 1024 * 1024,      // 最大Chunk大小 (1MB)
  MIN_CHUNK_SIZE: 20 * 1024,        // 最小Chunk大小 (20KB)
  VENDOR_CHUNK_SIZE: 2 * 1024 * 1024, // Vendor chunk大小 (2MB)
} as const;

// 性能监控配置
export const MONITORING_CONFIG = {
  PERFORMANCE_BUDGET: {
    FCP: 1500,    // First Contentful Paint (ms)
    LCP: 2500,    // Largest Contentful Paint (ms)
    FID: 100,     // First Input Delay (ms)
    CLS: 0.1,     // Cumulative Layout Shift
    TTI: 3500,    // Time to Interactive (ms)
  },
  
  MEMORY_THRESHOLDS: {
    WARNING: 100 * 1024 * 1024,  // 100MB
    CRITICAL: 200 * 1024 * 1024, // 200MB
  },
  
  RENDER_TIME_THRESHOLD: 16, // 渲染时间阈值 (60fps)
  
  SAMPLING_RATE: 0.1, // 性能数据采样率 (10%)
} as const;

// 开发环境性能配置
export const DEV_CONFIG = {
  ENABLE_PROFILER: process.env.NODE_ENV === 'development',
  ENABLE_WHY_DID_YOU_RENDER: false,
  ENABLE_REACT_DEVTOOLS: process.env.NODE_ENV === 'development',
  LOG_PERFORMANCE: process.env.NODE_ENV === 'development',
} as const;

// 导出所有配置
export const PERFORMANCE_CONFIG = {
  QUERY: QUERY_CONFIG,
  PAGINATION: PAGINATION_CONFIG,
  VIRTUAL_SCROLL: VIRTUAL_SCROLL_CONFIG,
  DEBOUNCE: DEBOUNCE_CONFIG,
  PREFETCH: PREFETCH_CONFIG,
  MEDIA: MEDIA_CONFIG,
  MEMORY: MEMORY_CONFIG,
  NETWORK: NETWORK_CONFIG,
  BUNDLE: BUNDLE_CONFIG,
  MONITORING: MONITORING_CONFIG,
  DEV: DEV_CONFIG,
} as const;

// 性能优化工具函数
export const getOptimalPageSize = (viewportHeight: number, itemHeight: number): number => {
  const visibleItems = Math.ceil(viewportHeight / itemHeight);
  return Math.min(visibleItems * 2, PAGINATION_CONFIG.MAX_PAGE_SIZE);
};

export const shouldUseVirtualScroll = (itemCount: number): boolean => {
  return itemCount >= VIRTUAL_SCROLL_CONFIG.THRESHOLD;
};

export const getRetryDelay = (attemptIndex: number): number => {
  return Math.min(
    QUERY_CONFIG.RETRY_DELAY.MIN * Math.pow(QUERY_CONFIG.RETRY_DELAY.MULTIPLIER, attemptIndex),
    QUERY_CONFIG.RETRY_DELAY.MAX
  );
};

export const isLowEndDevice = (): boolean => {
  // 检测设备性能
  if (typeof navigator !== 'undefined') {
    // @ts-ignore
    const connection = navigator.connection || navigator.mozConnection || navigator.webkitConnection;
    if (connection) {
      return connection.effectiveType === 'slow-2g' || connection.effectiveType === '2g';
    }
    
    // 检测内存
    // @ts-ignore
    if (navigator.deviceMemory) {
      // @ts-ignore
      return navigator.deviceMemory < 4; // 小于4GB内存
    }
  }
  
  return false;
};

export const getPerformanceConfig = () => {
  const isLowEnd = isLowEndDevice();
  
  if (isLowEnd) {
    return {
      ...PERFORMANCE_CONFIG,
      PAGINATION: {
        ...PAGINATION_CONFIG,
        DEFAULT_PAGE_SIZE: 10, // 低端设备减少页面大小
      },
      VIRTUAL_SCROLL: {
        ...VIRTUAL_SCROLL_CONFIG,
        THRESHOLD: 50, // 更早启用虚拟滚动
        OVERSCAN: 3,   // 减少额外渲染
      },
      PREFETCH: {
        ...PREFETCH_CONFIG,
        EMAIL_HOVER_DELAY: 500, // 增加预取延迟
        MAX_PREFETCH_SIZE: 2,   // 减少预取数量
      },
    };
  }
  
  return PERFORMANCE_CONFIG;
};
