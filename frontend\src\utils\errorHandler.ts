import { message, notification } from 'antd';

// 错误类型
export enum ErrorType {
  VALIDATION = 'VALIDATION',
  AUTHENTICATION = 'AUTHENTICATION',
  AUTHORIZATION = 'AUTHORIZATION',
  NOT_FOUND = 'NOT_FOUND',
  CONFLICT = 'CONFLICT',
  RATE_LIMIT = 'RATE_LIMIT',
  SERVER_ERROR = 'SERVER_ERROR',
  DATABASE_ERROR = 'DATABASE_ERROR',
  NETWORK_ERROR = 'NETWORK_ERROR',
}

// 错误代码
export enum ErrorCode {
  INVALID_INPUT = 'INVALID_INPUT',
  MISSING_REQUIRED_FIELD = 'MISSING_REQUIRED_FIELD',
  INVALID_EMAIL_FORMAT = 'INVALID_EMAIL_FORMAT',
  INVALID_PASSWORD_FORMAT = 'INVALID_PASSWORD_FORMAT',
  INVALID_CREDENTIALS = 'INVALID_CREDENTIALS',
  TOKEN_EXPIRED = 'TOKEN_EXPIRED',
  TOKEN_INVALID = 'TOKEN_INVALID',
  TOKEN_MISSING = 'TOKEN_MISSING',
  INSUFFICIENT_PERMISSIONS = 'INSUFFICIENT_PERMISSIONS',
  ACCOUNT_DISABLED = 'ACCOUNT_DISABLED',
  RESOURCE_NOT_FOUND = 'RESOURCE_NOT_FOUND',
  RESOURCE_ALREADY_EXISTS = 'RESOURCE_ALREADY_EXISTS',
  INTERNAL_SERVER_ERROR = 'INTERNAL_SERVER_ERROR',
  DATABASE_CONNECTION_ERROR = 'DATABASE_CONNECTION_ERROR',
  EXTERNAL_SERVICE_ERROR = 'EXTERNAL_SERVICE_ERROR',
}

interface ErrorInfo {
  message: string;
  errorType?: ErrorType;
  errorCode?: ErrorCode;
  details?: any;
  timestamp?: string;
  requestId?: string;
}

// 解析错误信息
export const parseError = (error: any): ErrorInfo => {
  // 如果是API响应错误
  if (error?.response?.data) {
    const responseData = error.response.data;
    return {
      message: responseData.error || responseData.message || '请求失败',
      errorType: responseData.errorType,
      errorCode: responseData.errorCode,
      details: responseData.details,
      timestamp: responseData.timestamp,
      requestId: responseData.requestId,
    };
  }

  // 如果是网络错误
  if (error?.code === 'ERR_NETWORK' || error?.message?.includes('Network Error')) {
    return {
      message: '网络连接失败，请检查您的网络连接',
      errorType: ErrorType.NETWORK_ERROR,
      timestamp: new Date().toISOString(),
    };
  }

  // 如果是超时错误
  if (error?.code === 'ECONNABORTED' || error?.message?.includes('timeout')) {
    return {
      message: '请求超时，请稍后重试',
      errorType: ErrorType.NETWORK_ERROR,
      timestamp: new Date().toISOString(),
    };
  }

  // 默认错误处理
  return {
    message: error?.message || error?.toString() || '发生了未知错误',
    timestamp: new Date().toISOString(),
  };
};

// 获取用户友好的错误消息
export const getFriendlyErrorMessage = (error: any): string => {
  const errorInfo = parseError(error);
  
  // 根据错误类型和代码返回友好的消息
  if (errorInfo.errorCode) {
    switch (errorInfo.errorCode) {
      case ErrorCode.INVALID_CREDENTIALS:
        return '用户名或密码错误，请重新输入';
      case ErrorCode.TOKEN_EXPIRED:
        return '登录已过期，请重新登录';
      case ErrorCode.INSUFFICIENT_PERMISSIONS:
        return '您没有执行此操作的权限';
      case ErrorCode.RESOURCE_NOT_FOUND:
        return '请求的内容不存在';
      case ErrorCode.RESOURCE_ALREADY_EXISTS:
        return '该内容已存在';
      case ErrorCode.INVALID_EMAIL_FORMAT:
        return '请输入正确的邮箱格式';
      case ErrorCode.MISSING_REQUIRED_FIELD:
        return '请填写所有必需的信息';
      default:
        break;
    }
  }

  switch (errorInfo.errorType) {
    case ErrorType.VALIDATION:
      return '输入信息有误，请检查后重试';
    case ErrorType.AUTHENTICATION:
      return '身份验证失败，请重新登录';
    case ErrorType.AUTHORIZATION:
      return '权限不足，无法执行此操作';
    case ErrorType.NOT_FOUND:
      return '请求的内容不存在';
    case ErrorType.NETWORK_ERROR:
      return '网络连接失败，请检查网络后重试';
    case ErrorType.SERVER_ERROR:
      return '服务器暂时不可用，请稍后重试';
    default:
      return errorInfo.message;
  }
};

// 显示错误消息
export const showErrorMessage = (error: any, customMessage?: string) => {
  const errorMessage = customMessage || getFriendlyErrorMessage(error);
  message.error(errorMessage);
};

// 显示错误通知
export const showErrorNotification = (error: any, customMessage?: string) => {
  const errorInfo = parseError(error);
  const errorMessage = customMessage || getFriendlyErrorMessage(error);
  
  notification.error({
    message: getErrorTitle(errorInfo.errorType, errorInfo.errorCode),
    description: errorMessage,
    duration: 4.5,
    placement: 'topRight',
  });
};

// 获取错误标题
const getErrorTitle = (errorType?: ErrorType, errorCode?: ErrorCode): string => {
  if (errorCode) {
    switch (errorCode) {
      case ErrorCode.INVALID_CREDENTIALS:
        return '登录失败';
      case ErrorCode.TOKEN_EXPIRED:
        return '登录已过期';
      case ErrorCode.INSUFFICIENT_PERMISSIONS:
        return '权限不足';
      case ErrorCode.RESOURCE_NOT_FOUND:
        return '内容不存在';
      default:
        break;
    }
  }

  switch (errorType) {
    case ErrorType.VALIDATION:
      return '输入验证失败';
    case ErrorType.AUTHENTICATION:
      return '身份验证失败';
    case ErrorType.AUTHORIZATION:
      return '权限验证失败';
    case ErrorType.NOT_FOUND:
      return '内容不存在';
    case ErrorType.NETWORK_ERROR:
      return '网络连接错误';
    case ErrorType.SERVER_ERROR:
      return '服务器错误';
    default:
      return '操作失败';
  }
};

// 处理异步操作的错误
export const handleAsyncError = async <T>(
  asyncFn: () => Promise<T>,
  errorMessage?: string,
  showNotification = true
): Promise<T | null> => {
  try {
    return await asyncFn();
  } catch (error) {
    console.error('Async operation failed:', error);
    
    if (showNotification) {
      showErrorMessage(error, errorMessage);
    }
    
    return null;
  }
};

// 静默处理错误（只记录，不显示给用户）
export const handleSilentError = (error: any, context?: string) => {
  const errorInfo = parseError(error);
  console.error(`Silent error ${context ? `in ${context}` : ''}:`, errorInfo);
  
  // 在开发环境中可以发送到错误监控服务
  if (process.env.NODE_ENV === 'development') {
    console.warn('Error details:', error);
  }
};

// 检查是否为网络错误
export const isNetworkError = (error: any): boolean => {
  return !error.response || 
         error.code === 'ERR_NETWORK' || 
         error.message?.includes('Network Error') ||
         error.code === 'ECONNABORTED';
};

// 检查是否为认证错误
export const isAuthError = (error: any): boolean => {
  return error?.response?.status === 401 || 
         error?.response?.data?.errorType === ErrorType.AUTHENTICATION;
};

// 检查是否为权限错误
export const isPermissionError = (error: any): boolean => {
  return error?.response?.status === 403 || 
         error?.response?.data?.errorType === ErrorType.AUTHORIZATION;
};
