import React, { useEffect } from "react";
import { List, Checkbox, Avatar, Typography, Button, Empty, Spin } from "antd";
import {
  StarOutlined,
  StarFilled,
  PaperClipOutlined,
  DeleteOutlined,
  MailOutlined,
} from "@ant-design/icons";
import {
  useInfiniteEmails,
  useUpdateEmail,
  useDeleteEmail,
} from "../../hooks/useEmails";
import { useUIStore } from "../../store/uiStore";
import { useNavigate, useParams } from "react-router-dom";
import { showErrorMessage } from "../../utils/errorHandler";
import type { Email } from "../../types";
import { formatDistanceToNow } from "date-fns";
import { zhCN } from "date-fns/locale";

const { Text, Title } = Typography;

const EmailList: React.FC = () => {
  const navigate = useNavigate();
  const { folderId } = useParams();

  // 使用新的状态管理
  const {
    currentFolder,
    selectedEmails,
    selectEmail,
    unselectEmail,
    clearSelection,
    setCurrentEmail,
  } = useUIStore();

  // 使用无限滚动查询
  const {
    data,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    isLoading,
    error,
  } = useInfiniteEmails({
    folderId: folderId || currentFolder?.id,
  });

  // 邮件操作mutations
  const updateEmailMutation = useUpdateEmail();
  const deleteEmailMutation = useDeleteEmail();

  // 扁平化邮件数据
  const emails = data?.pages.flatMap((page) => page.data) || [];

  // 错误处理
  useEffect(() => {
    if (error) {
      showErrorMessage(error, "获取邮件列表失败");
    }
  }, [error]);

  const handleEmailClick = (email: Email) => {
    setCurrentEmail(email);
    navigate(`/email/${email.id}`);

    // 如果邮件未读，标记为已读
    if (!email.isRead) {
      updateEmailMutation.mutate({
        id: email.id,
        updates: { isRead: true },
      });
    }
  };

  const handleSelectEmail = (emailId: string, checked: boolean) => {
    if (checked) {
      selectEmail(emailId);
    } else {
      unselectEmail(emailId);
    }
  };

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      emails.forEach((email) => selectEmail(email.id));
    } else {
      clearSelection();
    }
  };

  const handleStarToggle = (email: Email, e: React.MouseEvent) => {
    e.stopPropagation();
    updateEmailMutation.mutate({
      id: email.id,
      updates: { isStarred: !email.isStarred },
    });
  };

  const handleMarkAsRead = (emailId: string, e: React.MouseEvent) => {
    e.stopPropagation();
    updateEmailMutation.mutate({
      id: emailId,
      updates: { isRead: true },
    });
  };

  const handleDelete = (emailId: string, e: React.MouseEvent) => {
    e.stopPropagation();
    deleteEmailMutation.mutate(emailId);
  };

  const formatDate = (dateString: string) => {
    return formatDistanceToNow(new Date(dateString), {
      addSuffix: true,
      locale: zhCN,
    });
  };

  const getEmailPreview = (email: Email) => {
    // 从HTML内容中提取纯文本预览
    const div = document.createElement("div");
    div.innerHTML = email.contentHtml || email.contentText;
    const text = div.textContent || div.innerText || "";
    return text.substring(0, 100) + (text.length > 100 ? "..." : "");
  };

  const renderEmailItem = (email: Email) => (
    <List.Item
      key={email.id}
      className={`cursor-pointer hover:bg-gray-50 transition-colors ${
        !email.isRead ? "bg-blue-50 border-l-4 border-l-blue-500" : ""
      } ${selectedEmails.includes(email.id) ? "bg-blue-100" : ""}`}
      onClick={() => handleEmailClick(email)}
      actions={[
        <Button
          type="text"
          icon={
            email.isStarred ? (
              <StarFilled className="text-yellow-500" />
            ) : (
              <StarOutlined />
            )
          }
          onClick={(e) => handleStarToggle(email, e)}
          size="small"
        />,
        !email.isRead && (
          <Button
            type="text"
            icon={<MailOutlined />}
            onClick={(e) => handleMarkAsRead(email.id, e)}
            size="small"
            title="标记为已读"
          />
        ),
        <Button
          type="text"
          icon={<DeleteOutlined />}
          onClick={(e) => handleDelete(email.id, e)}
          size="small"
          danger
          title="删除"
        />,
      ].filter(Boolean)}
    >
      <div className="flex items-start space-x-3 w-full">
        <Checkbox
          checked={selectedEmails.includes(email.id)}
          onChange={(e) => handleSelectEmail(email.id, e.target.checked)}
          onClick={(e) => e.stopPropagation()}
        />

        <Avatar size="small" className="bg-primary-500 flex-shrink-0">
          {email.senderName?.charAt(0) ||
            email.senderEmail.charAt(0).toUpperCase()}
        </Avatar>

        <div className="flex-1 min-w-0">
          <div className="flex items-center justify-between mb-1">
            <div className="flex items-center space-x-2">
              <Text
                className={`text-sm ${
                  !email.isRead ? "font-semibold" : "font-normal"
                }`}
                ellipsis
              >
                {email.senderName || email.senderEmail}
              </Text>
              {email.attachments.length > 0 && (
                <PaperClipOutlined className="text-gray-400 text-xs" />
              )}
            </div>
            <Text className="text-xs text-gray-500 flex-shrink-0">
              {formatDate(email.receivedAt || email.createdAt)}
            </Text>
          </div>

          <div className="mb-1">
            <Text
              className={`text-sm ${
                !email.isRead ? "font-medium" : "font-normal"
              }`}
              ellipsis
            >
              {email.subject || "(无主题)"}
            </Text>
          </div>

          <Text className="text-xs text-gray-600" ellipsis>
            {getEmailPreview(email)}
          </Text>
        </div>
      </div>
    </List.Item>
  );

  if (isLoading && emails.length === 0) {
    return (
      <div className="flex justify-center items-center h-64">
        <Spin size="large" />
      </div>
    );
  }

  if (emails.length === 0 && !isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} description="暂无邮件" />
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col">
      {/* 工具栏 */}
      <div className="bg-white border-b border-gray-200 p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Checkbox
              indeterminate={
                selectedEmails.length > 0 &&
                selectedEmails.length < emails.length
              }
              checked={
                selectedEmails.length === emails.length && emails.length > 0
              }
              onChange={(e) => handleSelectAll(e.target.checked)}
            />
            <Title level={5} className="mb-0">
              {currentFolder?.name} ({emails.length})
            </Title>
          </div>

          {selectedEmails.length > 0 && (
            <div className="flex items-center space-x-2">
              <Text className="text-sm text-gray-600">
                已选择 {selectedEmails.length} 封邮件
              </Text>
              <Button size="small" onClick={clearSelection}>
                取消选择
              </Button>
            </div>
          )}
        </div>
      </div>

      {/* 邮件列表 */}
      <div className="flex-1 overflow-auto">
        <List
          dataSource={emails}
          renderItem={renderEmailItem}
          className="bg-white"
          split={false}
        />

        {/* 加载更多 */}
        {hasNextPage && (
          <div className="text-center p-4">
            <Button
              loading={isFetchingNextPage}
              onClick={() => fetchNextPage()}
              type="link"
            >
              加载更多
            </Button>
          </div>
        )}

        {isFetchingNextPage && (
          <div className="text-center p-4">
            <Spin />
          </div>
        )}
      </div>
    </div>
  );
};

export default EmailList;
