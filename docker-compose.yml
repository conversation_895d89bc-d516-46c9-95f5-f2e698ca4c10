version: '3.8'

services:
  # 前端服务
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
      target: production
    container_name: email-frontend
    ports:
      - "80:80"
    depends_on:
      - backend
    environment:
      - NODE_ENV=production
    networks:
      - email-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # 后端服务
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
      target: production
    container_name: email-backend
    ports:
      - "5000:5000"
      - "9090:9090"  # 监控端口
    depends_on:
      - postgres
      - redis
    environment:
      - NODE_ENV=production
      - DATABASE_URL=**********************************************/emaildb
      - REDIS_URL=redis://redis:6379
    env_file:
      - ./backend/.env.production
    volumes:
      - uploads:/app/uploads
      - logs:/app/logs
    networks:
      - email-network
    restart: unless-stopped
    healthcheck:
      test: ["C<PERSON>", "curl", "-f", "http://localhost:5000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # PostgreSQL数据库
  postgres:
    image: postgres:15-alpine
    container_name: email-postgres
    environment:
      - POSTGRES_DB=emaildb
      - POSTGRES_USER=emailuser
      - POSTGRES_PASSWORD=emailpass
      - POSTGRES_INITDB_ARGS=--encoding=UTF-8 --lc-collate=C --lc-ctype=C
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backend/database/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - email-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U emailuser -d emaildb"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Redis缓存
  redis:
    image: redis:7-alpine
    container_name: email-redis
    command: redis-server --appendonly yes --requirepass redispass
    volumes:
      - redis_data:/data
    networks:
      - email-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Nginx负载均衡器（可选）
  nginx:
    image: nginx:alpine
    container_name: email-nginx
    ports:
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
    depends_on:
      - frontend
    networks:
      - email-network
    restart: unless-stopped
    profiles:
      - production

  # 监控服务（可选）
  prometheus:
    image: prom/prometheus:latest
    container_name: email-prometheus
    ports:
      - "9091:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - email-network
    restart: unless-stopped
    profiles:
      - monitoring

  grafana:
    image: grafana/grafana:latest
    container_name: email-grafana
    ports:
      - "3001:3000"
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/provisioning:/etc/grafana/provisioning
    environment:
      - GF_SECURITY_ADMIN_USER=admin
      - GF_SECURITY_ADMIN_PASSWORD=admin
      - GF_USERS_ALLOW_SIGN_UP=false
    networks:
      - email-network
    restart: unless-stopped
    profiles:
      - monitoring

# 网络配置
networks:
  email-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# 数据卷
volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  uploads:
    driver: local
  logs:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
