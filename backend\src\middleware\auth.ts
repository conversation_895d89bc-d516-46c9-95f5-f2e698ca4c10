import { Request, Response, NextFunction } from "express";
import jwt from "jsonwebtoken";
import type { JwtPayload } from "../types";
import { AppError } from "../types";
import { memoryDB } from "../database/memoryStore";

// JWT认证中间件
export const authenticateToken = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.split(" ")[1]; // Bearer TOKEN

    if (!token) {
      throw new AppError("访问令牌缺失", 401);
    }

    const jwtSecret = process.env.JWT_SECRET;
    if (!jwtSecret) {
      throw new AppError("JWT密钥未配置", 500);
    }

    // 验证token
    const decoded = jwt.verify(token, jwtSecret) as JwtPayload;

    // 获取用户信息
    const user = await memoryDB.getUserById(decoded.userId);
    if (!user) {
      throw new AppError("用户不存在", 401);
    }

    if (!user.isActive) {
      throw new AppError("用户账户已被禁用", 401);
    }

    // 将用户信息添加到请求对象
    req.user = user;
    next();
  } catch (error) {
    if (error instanceof jwt.JsonWebTokenError) {
      next(new AppError("无效的访问令牌", 401));
    } else if (error instanceof jwt.TokenExpiredError) {
      next(new AppError("访问令牌已过期", 401));
    } else {
      next(error);
    }
  }
};

// 可选认证中间件（用于某些不强制要求登录的接口）
export const optionalAuth = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.split(" ")[1];

    if (token) {
      const jwtSecret = process.env.JWT_SECRET;
      if (jwtSecret) {
        try {
          const decoded = jwt.verify(token, jwtSecret) as JwtPayload;
          const user = await memoryDB.getUserById(decoded.userId);
          if (user && user.isActive) {
            req.user = user;
          }
        } catch (error) {
          // 忽略token验证错误，继续处理请求
        }
      }
    }

    next();
  } catch (error) {
    next(error);
  }
};

// 生成JWT token
export const generateTokens = (user: {
  id: string;
  email: string;
  username: string;
}) => {
  const jwtSecret = process.env.JWT_SECRET!;
  const jwtExpiresIn = process.env.JWT_EXPIRES_IN || "7d";
  const refreshExpiresIn = process.env.JWT_REFRESH_EXPIRES_IN || "30d";

  const payload: JwtPayload = {
    userId: user.id,
    email: user.email,
    username: user.username,
  };
  const accessToken = jwt.sign(payload, jwtSecret, { expiresIn: jwtExpiresIn } as jwt.SignOptions);
  const refreshToken = jwt.sign(payload, jwtSecret, {
    expiresIn: refreshExpiresIn,
  } as jwt.SignOptions);

  return {
    accessToken,
    refreshToken,
  };
};
