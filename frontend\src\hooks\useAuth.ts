import React from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useNavigate } from 'react-router-dom';
import { apiService } from '../services/api';
import { queryKeys, queryOptions, getRelatedQueryKeys } from '../lib/queryKeys';
import { showErrorMessage, showErrorNotification } from './useErrorHandler';
import { message } from 'antd';
import type { User, LoginRequest, RegisterRequest } from '../types';

// 获取当前用户信息
export const useCurrentUser = () => {
  return useQuery({
    queryKey: queryKeys.auth.user(),
    queryFn: async () => {
      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('No token found');
      }
      
      const response = await apiService.getCurrentUser();
      return response.data.data;
    },
    enabled: !!localStorage.getItem('token'),
    ...queryOptions.longTerm,
    retry: (failureCount, error: any) => {
      // 对于认证错误不重试
      if (error?.response?.status === 401) {
        return false;
      }
      return failureCount < 2;
    },
  });
};

// 登录
export const useLogin = () => {
  const queryClient = useQueryClient();
  const navigate = useNavigate();

  return useMutation({
    mutationFn: async (credentials: LoginRequest) => {
      const response = await apiService.login(credentials);
      return response.data.data;
    },
    onSuccess: (authData) => {
      // 保存认证信息
      localStorage.setItem('token', authData.token);
      localStorage.setItem('refreshToken', authData.refreshToken);

      // 设置用户数据到缓存
      queryClient.setQueryData(queryKeys.auth.user(), authData.user);

      // 预取用户相关数据
      queryClient.prefetchQuery({
        queryKey: queryKeys.folders.lists(),
        queryFn: async () => {
          const response = await apiService.getFolders();
          return response.data.data;
        },
        ...queryOptions.longTerm,
      });

      message.success('登录成功');
      navigate('/');
    },
    onError: (error) => {
      showErrorNotification(error, '登录失败');
    },
  });
};

// 注册
export const useRegister = () => {
  const queryClient = useQueryClient();
  const navigate = useNavigate();

  return useMutation({
    mutationFn: async (userData: RegisterRequest) => {
      const response = await apiService.register(userData);
      return response.data.data;
    },
    onSuccess: (authData) => {
      // 保存认证信息
      localStorage.setItem('token', authData.token);
      localStorage.setItem('refreshToken', authData.refreshToken);

      // 设置用户数据到缓存
      queryClient.setQueryData(queryKeys.auth.user(), authData.user);

      message.success('注册成功');
      navigate('/');
    },
    onError: (error) => {
      showErrorNotification(error, '注册失败');
    },
  });
};

// 登出
export const useLogout = () => {
  const queryClient = useQueryClient();
  const navigate = useNavigate();

  return useMutation({
    mutationFn: async () => {
      // 调用后端登出API（可选）
      try {
        await apiService.logout();
      } catch (error) {
        // 忽略登出API错误
        console.warn('Logout API failed:', error);
      }
    },
    onSuccess: () => {
      // 清除本地存储
      localStorage.removeItem('token');
      localStorage.removeItem('refreshToken');

      // 清除所有查询缓存
      queryClient.clear();

      message.success('已安全退出');
      navigate('/login');
    },
    onError: (error) => {
      // 即使API失败也要清除本地数据
      localStorage.removeItem('token');
      localStorage.removeItem('refreshToken');
      queryClient.clear();
      
      showErrorMessage(error, '退出时发生错误，但已清除本地数据');
      navigate('/login');
    },
  });
};

// 刷新Token
export const useRefreshToken = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async () => {
      const refreshToken = localStorage.getItem('refreshToken');
      if (!refreshToken) {
        throw new Error('No refresh token found');
      }

      const response = await apiService.refreshToken(refreshToken);
      return response.data.data;
    },
    onSuccess: (tokenData) => {
      // 更新token
      localStorage.setItem('token', tokenData.token);
      
      // 重新获取用户信息
      queryClient.invalidateQueries({ queryKey: queryKeys.auth.user() });
    },
    onError: (error) => {
      // 刷新失败，清除认证状态
      localStorage.removeItem('token');
      localStorage.removeItem('refreshToken');
      
      // 清除所有查询缓存
      queryClient.clear();
      
      showErrorMessage(error, 'Token刷新失败，请重新登录');
      window.location.href = '/login';
    },
  });
};

// 更新用户信息
export const useUpdateUser = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (updates: Partial<User>) => {
      const response = await apiService.updateUser(updates);
      return response.data.data;
    },
    onMutate: async (updates) => {
      // 乐观更新
      await queryClient.cancelQueries({ queryKey: queryKeys.auth.user() });
      
      const previousUser = queryClient.getQueryData(queryKeys.auth.user());
      
      if (previousUser) {
        queryClient.setQueryData(queryKeys.auth.user(), {
          ...previousUser,
          ...updates,
        });
      }

      return { previousUser };
    },
    onError: (error, updates, context) => {
      // 回滚乐观更新
      if (context?.previousUser) {
        queryClient.setQueryData(queryKeys.auth.user(), context.previousUser);
      }
      showErrorMessage(error, '更新用户信息失败');
    },
    onSettled: () => {
      // 重新获取数据确保一致性
      queryClient.invalidateQueries({ queryKey: queryKeys.auth.user() });
    },
    onSuccess: () => {
      message.success('用户信息更新成功');
    },
  });
};

// 检查认证状态
export const useAuthStatus = () => {
  const { data: user, isLoading, error } = useCurrentUser();
  
  const isAuthenticated = !!user && !!localStorage.getItem('token');
  const isUnauthenticated = !isLoading && (!user || !localStorage.getItem('token'));

  return {
    user,
    isAuthenticated,
    isUnauthenticated,
    isLoading,
    error,
  };
};

// 自动刷新Token Hook
export const useAutoRefreshToken = () => {
  const { mutate: refreshToken } = useRefreshToken();
  const { isAuthenticated } = useAuthStatus();

  // 设置定时刷新Token（可选）
  React.useEffect(() => {
    if (!isAuthenticated) return;

    const interval = setInterval(() => {
      const token = localStorage.getItem('token');
      if (token) {
        try {
          // 解析JWT token获取过期时间
          const payload = JSON.parse(atob(token.split('.')[1]));
          const now = Date.now() / 1000;
          
          // 如果token在5分钟内过期，则刷新
          if (payload.exp - now < 5 * 60) {
            refreshToken();
          }
        } catch (error) {
          console.warn('Failed to parse token:', error);
        }
      }
    }, 60 * 1000); // 每分钟检查一次

    return () => clearInterval(interval);
  }, [isAuthenticated, refreshToken]);
};

// 权限检查Hook
export const usePermissions = () => {
  const { user } = useAuthStatus();

  const hasPermission = (permission: string) => {
    if (!user) return false;
    // 这里可以根据实际的权限系统实现
    return user.permissions?.includes(permission) || user.role === 'admin';
  };

  const hasRole = (role: string) => {
    if (!user) return false;
    return user.role === role;
  };

  return {
    hasPermission,
    hasRole,
    isAdmin: hasRole('admin'),
    isUser: hasRole('user'),
  };
};
