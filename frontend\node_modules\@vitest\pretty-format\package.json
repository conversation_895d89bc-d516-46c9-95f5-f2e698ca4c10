{"name": "@vitest/pretty-format", "type": "module", "version": "3.2.3", "description": "Fork of pretty-format with support for ESM", "license": "MIT", "funding": "https://opencollective.com/vitest", "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "repository": {"type": "git", "url": "git+https://github.com/vitest-dev/vitest.git", "directory": "packages/pretty-format"}, "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "sideEffects": false, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "main": "./dist/index.js", "module": "./dist/index.js", "types": "./dist/index.d.ts", "files": ["*.d.ts", "dist"], "dependencies": {"tinyrainbow": "^2.0.0"}, "devDependencies": {"@types/react-is": "^19.0.0", "react-is": "^19.1.0", "react-is-18": "npm:react-is@18.3.1"}, "scripts": {"build": "rimraf dist && rollup -c", "dev": "rollup -c --watch"}}