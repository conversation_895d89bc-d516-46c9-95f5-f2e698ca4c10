# Dovecot SQL 认证配置

# ================================
# 密码数据库
# ================================
passdb {
  driver = sql
  args = /etc/dovecot/dovecot-sql.conf.ext
}

# ================================
# 用户数据库
# ================================
userdb {
  driver = sql
  args = /etc/dovecot/dovecot-sql.conf.ext
}

# ================================
# 认证设置
# ================================
# 认证缓存
auth_cache_size = 10M
auth_cache_ttl = 1 hour
auth_cache_negative_ttl = 1 hour

# 认证工作进程
auth_worker_max_count = 30

# 认证失败延迟
auth_failure_delay = 2 secs

# 认证详细信息
auth_verbose = yes
auth_verbose_passwords = no
auth_debug = no
