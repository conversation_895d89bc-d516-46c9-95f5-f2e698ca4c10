# Postfix 主配置文件
# 邮件系统 - 发送邮件服务器配置

# ================================
# 基本设置
# ================================
myhostname = mail.yourdomain.com
mydomain = yourdomain.com
myorigin = $mydomain
inet_interfaces = all
inet_protocols = ipv4
mydestination = localhost

# ================================
# 网络和安全设置
# ================================
# 允许的网络
mynetworks = *********/8, 10.0.0.0/8, **********/12, ***********/16

# 邮件大小限制 (25MB)
message_size_limit = 26214400
mailbox_size_limit = 0

# 连接限制
smtpd_client_connection_count_limit = 50
smtpd_client_connection_rate_limit = 30

# ================================
# SMTP 认证和安全
# ================================
# 启用 SASL 认证
smtpd_sasl_auth_enable = yes
smtpd_sasl_type = dovecot
smtpd_sasl_path = private/auth
smtpd_sasl_security_options = noanonymous, noplaintext
smtpd_sasl_tls_security_options = noanonymous

# TLS 设置
smtpd_tls_security_level = may
smtpd_tls_cert_file = /etc/ssl/certs/mail.crt
smtpd_tls_key_file = /etc/ssl/private/mail.key
smtpd_tls_session_cache_database = btree:${data_directory}/smtpd_scache
smtpd_tls_session_cache_timeout = 3600s
smtpd_tls_loglevel = 1

# 客户端 TLS
smtp_tls_security_level = may
smtp_tls_session_cache_database = btree:${data_directory}/smtp_scache

# ================================
# 访问控制
# ================================
# 收件人限制
smtpd_recipient_restrictions = 
    permit_mynetworks,
    permit_sasl_authenticated,
    reject_unauth_destination,
    reject_invalid_hostname,
    reject_non_fqdn_hostname,
    reject_non_fqdn_sender,
    reject_non_fqdn_recipient,
    reject_unknown_sender_domain,
    reject_unknown_recipient_domain,
    reject_rbl_client zen.spamhaus.org,
    reject_rbl_client bl.spamcop.net,
    permit

# 发件人限制
smtpd_sender_restrictions = 
    permit_mynetworks,
    permit_sasl_authenticated,
    reject_non_fqdn_sender,
    reject_unknown_sender_domain,
    permit

# HELO 限制
smtpd_helo_restrictions = 
    permit_mynetworks,
    permit_sasl_authenticated,
    reject_invalid_helo_hostname,
    reject_non_fqdn_helo_hostname,
    permit

# ================================
# MySQL 虚拟域名和用户
# ================================
# 虚拟域名
virtual_mailbox_domains = mysql:/etc/postfix/mysql-virtual-mailbox-domains.cf
virtual_mailbox_maps = mysql:/etc/postfix/mysql-virtual-mailbox-maps.cf
virtual_alias_maps = mysql:/etc/postfix/mysql-virtual-alias-maps.cf

# 虚拟传输
virtual_transport = dovecot
dovecot_destination_recipient_limit = 1

# ================================
# 邮件队列设置
# ================================
# 队列目录
queue_directory = /var/spool/postfix

# 重试设置
maximal_queue_lifetime = 5d
bounce_queue_lifetime = 5d
maximal_backoff_time = 4000s
minimal_backoff_time = 300s
queue_run_delay = 300s

# ================================
# 日志设置
# ================================
# 邮件日志
maillog_file = /var/log/postfix/postfix.log
maillog_file_rotate_suffix = %Y%m%d-%H%M%S

# ================================
# 其他设置
# ================================
# 兼容性
compatibility_level = 3.6

# 进程限制
default_process_limit = 100
smtpd_process_limit = 20
smtp_process_limit = 20

# 头部检查
header_checks = regexp:/etc/postfix/header_checks

# 内容过滤器 (可选，用于垃圾邮件过滤)
# content_filter = spamassassin

# 邮件ID
enable_long_queue_ids = yes

# 地址重写
canonical_maps = hash:/etc/postfix/canonical
sender_canonical_maps = hash:/etc/postfix/sender_canonical
recipient_canonical_maps = hash:/etc/postfix/recipient_canonical
