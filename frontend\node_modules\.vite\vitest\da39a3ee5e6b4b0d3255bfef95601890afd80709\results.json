{"version": "3.2.3", "results": [[":src/tests/e2e/email-workflow.test.ts", {"duration": 0, "failed": true}], [":src/tests/functional/basic-functionality.test.tsx", {"duration": 383.41499999999996, "failed": false}], [":src/tests/integration/state-management.test.ts", {"duration": 0, "failed": true}], [":src/tests/integration/state-management.test.tsx", {"duration": 251.09340000000066, "failed": false}], [":src/tests/e2e/email-workflow.test.tsx", {"duration": 1571.4674000000005, "failed": true}]]}