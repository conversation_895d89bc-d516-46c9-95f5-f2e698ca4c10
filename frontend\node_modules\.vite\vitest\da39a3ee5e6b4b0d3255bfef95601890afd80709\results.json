{"version": "3.2.3", "results": [[":src/tests/e2e/email-workflow.test.ts", {"duration": 0, "failed": true}], [":src/tests/functional/basic-functionality.test.tsx", {"duration": 389.4386999999997, "failed": false}], [":src/tests/integration/state-management.test.ts", {"duration": 0, "failed": true}], [":src/tests/integration/state-management.test.tsx", {"duration": 267.6251000000002, "failed": false}], [":src/tests/e2e/email-workflow.test.tsx", {"duration": 3771.5662, "failed": true}]]}