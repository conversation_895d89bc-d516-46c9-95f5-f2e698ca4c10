import React, { Component, ErrorInfo, ReactNode } from 'react';
import { Result, Button, Typography, Collapse, Space } from 'antd';
import { BugOutlined, ReloadOutlined, HomeOutlined } from '@ant-design/icons';

const { Text, Paragraph } = Typography;
const { Panel } = Collapse;

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
  errorId: string;
}

class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: '',
    };
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    // 生成错误ID
    const errorId = `ERR_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    return {
      hasError: true,
      error,
      errorId,
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    // 记录错误信息
    console.error('ErrorBoundary caught an error:', error, errorInfo);
    
    // 调用外部错误处理函数
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }

    // 发送错误报告到监控服务（如果配置了）
    this.reportError(error, errorInfo);

    this.setState({
      errorInfo,
    });
  }

  private reportError = (error: Error, errorInfo: ErrorInfo) => {
    // 这里可以集成错误监控服务，如 Sentry, LogRocket 等
    const errorReport = {
      message: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      errorId: this.state.errorId,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href,
    };

    // 发送到错误监控服务
    if (process.env.NODE_ENV === 'production') {
      // 这里可以调用实际的错误报告API
      console.log('Error report:', errorReport);
    }
  };

  private handleReload = () => {
    window.location.reload();
  };

  private handleGoHome = () => {
    window.location.href = '/';
  };

  private handleRetry = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: '',
    });
  };

  render() {
    if (this.state.hasError) {
      // 如果提供了自定义fallback，使用它
      if (this.props.fallback) {
        return this.props.fallback;
      }

      // 默认错误页面
      return (
        <div style={{ padding: '50px', minHeight: '100vh', backgroundColor: '#f5f5f5' }}>
          <Result
            status="error"
            icon={<BugOutlined style={{ color: '#ff4d4f' }} />}
            title="页面出现了错误"
            subTitle={
              <Space direction="vertical" size="middle">
                <Text type="secondary">
                  抱歉，页面遇到了意外错误。我们已经记录了这个问题，请稍后重试。
                </Text>
                <Text code style={{ fontSize: '12px' }}>
                  错误ID: {this.state.errorId}
                </Text>
              </Space>
            }
            extra={
              <Space size="middle">
                <Button type="primary" icon={<ReloadOutlined />} onClick={this.handleRetry}>
                  重试
                </Button>
                <Button icon={<ReloadOutlined />} onClick={this.handleReload}>
                  刷新页面
                </Button>
                <Button icon={<HomeOutlined />} onClick={this.handleGoHome}>
                  返回首页
                </Button>
              </Space>
            }
          >
            {/* 开发环境显示详细错误信息 */}
            {process.env.NODE_ENV === 'development' && this.state.error && (
              <div style={{ marginTop: '20px', textAlign: 'left' }}>
                <Collapse ghost>
                  <Panel header="错误详情（开发模式）" key="1">
                    <Space direction="vertical" style={{ width: '100%' }}>
                      <div>
                        <Text strong>错误消息:</Text>
                        <Paragraph code copyable style={{ marginTop: '8px' }}>
                          {this.state.error.message}
                        </Paragraph>
                      </div>
                      
                      {this.state.error.stack && (
                        <div>
                          <Text strong>错误堆栈:</Text>
                          <Paragraph 
                            code 
                            copyable 
                            style={{ 
                              marginTop: '8px',
                              maxHeight: '200px',
                              overflow: 'auto',
                              fontSize: '12px'
                            }}
                          >
                            {this.state.error.stack}
                          </Paragraph>
                        </div>
                      )}
                      
                      {this.state.errorInfo?.componentStack && (
                        <div>
                          <Text strong>组件堆栈:</Text>
                          <Paragraph 
                            code 
                            copyable 
                            style={{ 
                              marginTop: '8px',
                              maxHeight: '200px',
                              overflow: 'auto',
                              fontSize: '12px'
                            }}
                          >
                            {this.state.errorInfo.componentStack}
                          </Paragraph>
                        </div>
                      )}
                    </Space>
                  </Panel>
                </Collapse>
              </div>
            )}
          </Result>
        </div>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;
