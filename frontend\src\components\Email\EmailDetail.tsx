import React, { useEffect } from "react";
import { usePara<PERSON>, useNavigate } from "react-router-dom";
import {
  Card,
  Avatar,
  Typography,
  Button,
  Space,
  Divider,
  Spin,
  message,
  Dropdown,
} from "antd";
import {
  ArrowLeftOutlined,
  StarOutlined,
  StarFilled,
  // ReplyOutlined,
  ForwardOutlined,
  DeleteOutlined,
  DownloadOutlined,
  MoreOutlined,
  PaperClipOutlined,
} from "@ant-design/icons";
import { useEmail, useUpdateEmail, useDeleteEmail } from "../../hooks/useEmails";
import { showErrorMessage } from "../../utils/errorHandler";
import { format } from "date-fns";
import { zhCN } from "date-fns/locale";
import type { Attachment } from "../../types";

const { Title, Text, Paragraph } = Typography;

const EmailDetail: React.FC = () => {
  const { emailId } = useParams<{ emailId: string }>();
  const navigate = useNavigate();

  // 使用新的hooks
  const { data: currentEmail, isLoading, error } = useEmail(emailId || '', !!emailId);
  const updateEmailMutation = useUpdateEmail();
  const deleteEmailMutation = useDeleteEmail();

  // 错误处理
  useEffect(() => {
    if (error) {
      showErrorMessage(error, '获取邮件详情失败');
    }
  }, [error]);

  const handleBack = () => {
    navigate(-1);
  };

  const handleStarToggle = () => {
    if (!currentEmail) return;

    updateEmailMutation.mutate({
      id: currentEmail.id,
      updates: { isStarred: !currentEmail.isStarred },
    });
  };

  const handleReply = () => {
    if (!currentEmail) return;
    navigate(`/compose?reply=${currentEmail.id}`);
  };

  const handleForward = () => {
    if (!currentEmail) return;
    navigate(`/compose?forward=${currentEmail.id}`);
  };

  const handleDelete = () => {
    if (!currentEmail) return;

    deleteEmailMutation.mutate(currentEmail.id, {
      onSuccess: () => {
        message.success("邮件已删除");
        navigate(-1);
      },
    });
  };

  const handleDownloadAttachment = (attachment: Attachment) => {
    // 创建下载链接
    const link = document.createElement("a");
    link.href = attachment.url;
    link.download = attachment.filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const moreMenuItems = [
    {
      key: "markUnread",
      label: "标记为未读",
      onClick: () => {
        // TODO: 实现标记为未读
      },
    },
    {
      key: "moveToFolder",
      label: "移动到文件夹",
      onClick: () => {
        // TODO: 实现移动到文件夹
      },
    },
    {
      key: "addLabel",
      label: "添加标签",
      onClick: () => {
        // TODO: 实现添加标签
      },
    },
  ];

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Spin size="large" />
      </div>
    );
  }

  if (!currentEmail) {
    return (
      <div className="flex justify-center items-center h-64">
        <Text>邮件不存在或已被删除</Text>
      </div>
    );
  }

  const formatDate = (dateString: string) => {
    return format(new Date(dateString), "yyyy年MM月dd日 HH:mm", {
      locale: zhCN,
    });
  };

  return (
    <div className="h-full flex flex-col bg-gray-50">
      {/* 工具栏 */}
      <div className="bg-white border-b border-gray-200 p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Button
              icon={<ArrowLeftOutlined />}
              onClick={handleBack}
              type="text"
            >
              返回
            </Button>
            <Title level={4} className="mb-0" ellipsis>
              {currentEmail.subject || "(无主题)"}
            </Title>
          </div>

          <Space>
            <Button
              icon={
                currentEmail.isStarred ? (
                  <StarFilled className="text-yellow-500" />
                ) : (
                  <StarOutlined />
                )
              }
              onClick={handleStarToggle}
              type="text"
            />
            <Button onClick={handleReply} type="text">
              回复
            </Button>
            <Button
              icon={<ForwardOutlined />}
              onClick={handleForward}
              type="text"
            >
              转发
            </Button>
            <Button
              icon={<DeleteOutlined />}
              onClick={handleDelete}
              type="text"
              danger
            >
              删除
            </Button>
            <Dropdown menu={{ items: moreMenuItems }} placement="bottomRight">
              <Button icon={<MoreOutlined />} type="text" />
            </Dropdown>
          </Space>
        </div>
      </div>

      {/* 邮件内容 */}
      <div className="flex-1 overflow-auto p-6">
        <Card className="max-w-4xl mx-auto">
          {/* 邮件头部信息 */}
          <div className="mb-6">
            <div className="flex items-start space-x-4 mb-4">
              <Avatar size="large" className="bg-primary-500">
                {currentEmail.senderName?.charAt(0) ||
                  currentEmail.senderEmail.charAt(0).toUpperCase()}
              </Avatar>
              <div className="flex-1">
                <div className="flex items-center justify-between mb-2">
                  <div>
                    <Text className="text-lg font-semibold">
                      {currentEmail.senderName || currentEmail.senderEmail}
                    </Text>
                    <br />
                    <Text className="text-sm text-gray-500">
                      {currentEmail.senderEmail}
                    </Text>
                  </div>
                  <Text className="text-sm text-gray-500">
                    {formatDate(
                      currentEmail.receivedAt || currentEmail.createdAt
                    )}
                  </Text>
                </div>

                <div className="space-y-1">
                  <div>
                    <Text className="text-sm text-gray-600">收件人：</Text>
                    <Text className="text-sm">
                      {currentEmail.recipients.join(", ")}
                    </Text>
                  </div>

                  {currentEmail.ccRecipients &&
                    currentEmail.ccRecipients.length > 0 && (
                      <div>
                        <Text className="text-sm text-gray-600">抄送：</Text>
                        <Text className="text-sm">
                          {currentEmail.ccRecipients.join(", ")}
                        </Text>
                      </div>
                    )}
                </div>
              </div>
            </div>

            {/* 附件 */}
            {currentEmail.attachments.length > 0 && (
              <div className="mb-4">
                <div className="flex items-center space-x-2 mb-2">
                  <PaperClipOutlined />
                  <Text className="font-medium">
                    附件 ({currentEmail.attachments.length})
                  </Text>
                </div>
                <div className="space-y-2">
                  {currentEmail.attachments.map((attachment, index) => (
                    <div
                      key={index}
                      className="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
                    >
                      <div className="flex items-center space-x-2">
                        <PaperClipOutlined className="text-gray-400" />
                        <div>
                          <Text className="font-medium">
                            {attachment.filename}
                          </Text>
                          <br />
                          <Text className="text-xs text-gray-500">
                            {(attachment.size / 1024 / 1024).toFixed(2)} MB
                          </Text>
                        </div>
                      </div>
                      <Button
                        icon={<DownloadOutlined />}
                        size="small"
                        onClick={() => handleDownloadAttachment(attachment)}
                      >
                        下载
                      </Button>
                    </div>
                  ))}
                </div>
              </div>
            )}

            <Divider />
          </div>

          {/* 邮件正文 */}
          <div className="prose max-w-none">
            {currentEmail.contentHtml ? (
              <div
                dangerouslySetInnerHTML={{ __html: currentEmail.contentHtml }}
                className="email-content"
              />
            ) : (
              <Paragraph style={{ whiteSpace: "pre-wrap" }}>
                {currentEmail.contentText}
              </Paragraph>
            )}
          </div>
        </Card>
      </div>
    </div>
  );
};

export default EmailDetail;
