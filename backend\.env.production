# 生产环境配置
NODE_ENV=production
PORT=5000

# 数据库配置
DATABASE_URL=***************************************/emaildb
REDIS_URL=redis://prod-redis:6379

# JWT配置
JWT_SECRET=your-super-secure-production-jwt-secret-key-here
JWT_EXPIRES_IN=7d
JWT_REFRESH_EXPIRES_IN=30d

# 邮件服务配置
SMTP_HOST=smtp.yourdomain.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-smtp-password

# IMAP配置
IMAP_HOST=imap.yourdomain.com
IMAP_PORT=993
IMAP_USER=<EMAIL>
IMAP_PASS=your-imap-password

# 文件上传配置
MAX_FILE_SIZE=10485760
UPLOAD_PATH=/app/uploads
STATIC_PATH=/app/static

# 安全配置
CORS_ORIGIN=https://yourdomain.com,https://www.yourdomain.com
RATE_LIMIT_WINDOW=15
RATE_LIMIT_MAX=100
BCRYPT_ROUNDS=12

# 日志配置
LOG_LEVEL=info
LOG_FORMAT=json
LOG_FILE=/app/logs/app.log

# 监控配置
ENABLE_METRICS=true
METRICS_PORT=9090
HEALTH_CHECK_PATH=/health

# 缓存配置
CACHE_TTL=3600
CACHE_MAX_SIZE=1000

# 邮件处理配置
EMAIL_BATCH_SIZE=50
EMAIL_PROCESS_INTERVAL=30000
EMAIL_RETRY_ATTEMPTS=3

# 安全头配置
HELMET_ENABLED=true
CSP_ENABLED=true

# 压缩配置
COMPRESSION_ENABLED=true
COMPRESSION_LEVEL=6

# SSL配置
SSL_CERT_PATH=/app/certs/cert.pem
SSL_KEY_PATH=/app/certs/key.pem
