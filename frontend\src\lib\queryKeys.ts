// React Query 查询键工厂
// 统一管理所有查询键，避免重复和冲突

export const queryKeys = {
  // 认证相关
  auth: {
    all: ['auth'] as const,
    user: () => [...queryKeys.auth.all, 'user'] as const,
  },

  // 邮件相关
  emails: {
    all: ['emails'] as const,
    lists: () => [...queryKeys.emails.all, 'list'] as const,
    list: (filters: {
      folderId?: string;
      page?: number;
      limit?: number;
      search?: any;
    }) => [...queryKeys.emails.lists(), filters] as const,
    details: () => [...queryKeys.emails.all, 'detail'] as const,
    detail: (id: string) => [...queryKeys.emails.details(), id] as const,
    search: (params: any) => [...queryKeys.emails.all, 'search', params] as const,
  },

  // 文件夹相关
  folders: {
    all: ['folders'] as const,
    lists: () => [...queryKeys.folders.all, 'list'] as const,
    details: () => [...queryKeys.folders.all, 'detail'] as const,
    detail: (id: string) => [...queryKeys.folders.details(), id] as const,
    stats: (id: string) => [...queryKeys.folders.detail(id), 'stats'] as const,
  },

  // 联系人相关
  contacts: {
    all: ['contacts'] as const,
    lists: () => [...queryKeys.contacts.all, 'list'] as const,
    details: () => [...queryKeys.contacts.all, 'detail'] as const,
    detail: (id: string) => [...queryKeys.contacts.details(), id] as const,
  },

  // 统计数据
  stats: {
    all: ['stats'] as const,
    dashboard: () => [...queryKeys.stats.all, 'dashboard'] as const,
    emailCounts: () => [...queryKeys.stats.all, 'emailCounts'] as const,
  },
} as const;

// 查询键类型
export type QueryKeys = typeof queryKeys;

// 辅助函数：获取所有相关的查询键
export const getRelatedQueryKeys = {
  // 当邮件发生变化时，需要失效的查询
  onEmailChange: (emailId?: string, folderId?: string) => [
    queryKeys.emails.all,
    queryKeys.folders.all,
    queryKeys.stats.all,
    ...(emailId ? [queryKeys.emails.detail(emailId)] : []),
    ...(folderId ? [queryKeys.folders.detail(folderId)] : []),
  ],

  // 当文件夹发生变化时，需要失效的查询
  onFolderChange: (folderId?: string) => [
    queryKeys.folders.all,
    queryKeys.emails.all,
    queryKeys.stats.all,
    ...(folderId ? [queryKeys.folders.detail(folderId)] : []),
  ],

  // 当用户认证状态变化时，需要失效的查询
  onAuthChange: () => [
    queryKeys.auth.all,
    queryKeys.emails.all,
    queryKeys.folders.all,
    queryKeys.contacts.all,
    queryKeys.stats.all,
  ],
};

// 查询选项预设
export const queryOptions = {
  // 实时数据（经常变化）
  realtime: {
    staleTime: 0,
    gcTime: 5 * 60 * 1000, // 5分钟
    refetchInterval: 30 * 1000, // 30秒自动刷新
  },

  // 短期缓存（偶尔变化）
  shortTerm: {
    staleTime: 2 * 60 * 1000, // 2分钟
    gcTime: 10 * 60 * 1000, // 10分钟
  },

  // 长期缓存（很少变化）
  longTerm: {
    staleTime: 15 * 60 * 1000, // 15分钟
    gcTime: 30 * 60 * 1000, // 30分钟
  },

  // 静态数据（基本不变）
  static: {
    staleTime: 60 * 60 * 1000, // 1小时
    gcTime: 24 * 60 * 60 * 1000, // 24小时
  },
} as const;
