var t,e,s,i,n,r,a,o,h,u,l,c,d,f,p,v,y,b,g,m,w,O,P,M,k,R,S,C,x,E,F,q,Q,W,A,D,j,U,T,K,I,L,_,H,G,N,B,z,$,J,V,X,Y,Z,tt,et,st,it,nt,rt,at,ot,ht,ut,lt,ct,dt,ft,pt,vt,yt,bt,gt,mt,wt,Ot=Object.defineProperty,Pt=Object.defineProperties,Mt=Object.getOwnPropertyDescriptors,kt=Object.getOwnPropertySymbols,Rt=Object.prototype.hasOwnProperty,St=Object.prototype.propertyIsEnumerable,Ct=t=>{throw TypeError(t)},xt=Math.pow,Et=(t,e,s)=>e in t?Ot(t,e,{enumerable:!0,configurable:!0,writable:!0,value:s}):t[e]=s,Ft=(t,e)=>{for(var s in e||(e={}))Rt.call(e,s)&&Et(t,s,e[s]);if(kt)for(var s of kt(e))St.call(e,s)&&Et(t,s,e[s]);return t},qt=(t,e)=>Pt(t,Mt(e)),Qt=(t,e,s)=>e.has(t)||Ct("Cannot "+s),Wt=(t,e,s)=>(Qt(t,e,"read from private field"),s?s.call(t):e.get(t)),At=(t,e,s)=>e.has(t)?Ct("Cannot add the same private member more than once"):e instanceof WeakSet?e.add(t):e.set(t,s),Dt=(t,e,s,i)=>(Qt(t,e,"write to private field"),i?i.call(t,s):e.set(t,s),s),jt=(t,e,s)=>(Qt(t,e,"access private method"),s),Ut=(t,e,s,i)=>({set _(i){Dt(t,e,i,s)},get _(){return Wt(t,e,i)}}),Tt=(t,e,s)=>new Promise(((i,n)=>{var r=t=>{try{o(s.next(t))}catch(e){n(e)}},a=t=>{try{o(s.throw(t))}catch(e){n(e)}},o=t=>t.done?i(t.value):Promise.resolve(t.value).then(r,a);o((s=s.apply(t,e)).next())}));import{r as Kt,g as It}from"./chunk-CMmhtoO5.js";function Lt(t,e){for(var s=0;s<e.length;s++){const i=e[s];if("string"!=typeof i&&!Array.isArray(i))for(const e in i)if("default"!==e&&!(e in t)){const s=Object.getOwnPropertyDescriptor(i,e);s&&Object.defineProperty(t,e,s.get?s:{enumerable:!0,get:()=>i[e]})}}return Object.freeze(Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}))}var _t,Ht,Gt={exports:{}},Nt={};var Bt=(Ht||(Ht=1,Gt.exports=function(){if(_t)return Nt;_t=1;var t=Symbol.for("react.transitional.element"),e=Symbol.for("react.fragment");function s(e,s,i){var n=null;if(void 0!==i&&(n=""+i),void 0!==s.key&&(n=""+s.key),"key"in s)for(var r in i={},s)"key"!==r&&(i[r]=s[r]);else i=s;return s=i.ref,{$$typeof:t,type:e,key:n,ref:void 0!==s?s:null,props:i}}return Nt.Fragment=e,Nt.jsx=s,Nt.jsxs=s,Nt}()),Gt.exports),zt=Kt();const $t=It(zt),Jt=Lt({__proto__:null,default:$t},[zt]);var Vt=class{constructor(){this.listeners=new Set,this.subscribe=this.subscribe.bind(this)}subscribe(t){return this.listeners.add(t),this.onSubscribe(),()=>{this.listeners.delete(t),this.onUnsubscribe()}}hasListeners(){return this.listeners.size>0}onSubscribe(){}onUnsubscribe(){}},Xt="undefined"==typeof window||"Deno"in globalThis;function Yt(){}function Zt(t){return"number"==typeof t&&t>=0&&t!==1/0}function te(t,e){return Math.max(t+(e||0)-Date.now(),0)}function ee(t,e){return"function"==typeof t?t(e):t}function se(t,e){return"function"==typeof t?t(e):t}function ie(t,e){const{type:s="all",exact:i,fetchStatus:n,predicate:r,queryKey:a,stale:o}=t;if(a)if(i){if(e.queryHash!==re(a,e.options))return!1}else if(!oe(e.queryKey,a))return!1;if("all"!==s){const t=e.isActive();if("active"===s&&!t)return!1;if("inactive"===s&&t)return!1}return("boolean"!=typeof o||e.isStale()===o)&&((!n||n===e.state.fetchStatus)&&!(r&&!r(e)))}function ne(t,e){const{exact:s,status:i,predicate:n,mutationKey:r}=t;if(r){if(!e.options.mutationKey)return!1;if(s){if(ae(e.options.mutationKey)!==ae(r))return!1}else if(!oe(e.options.mutationKey,r))return!1}return(!i||e.state.status===i)&&!(n&&!n(e))}function re(t,e){return((null==e?void 0:e.queryKeyHashFn)||ae)(t)}function ae(t){return JSON.stringify(t,((t,e)=>ce(e)?Object.keys(e).sort().reduce(((t,s)=>(t[s]=e[s],t)),{}):e))}function oe(t,e){return t===e||typeof t==typeof e&&(!(!t||!e||"object"!=typeof t||"object"!=typeof e)&&Object.keys(e).every((s=>oe(t[s],e[s]))))}function he(t,e){if(t===e)return t;const s=le(t)&&le(e);if(s||ce(t)&&ce(e)){const i=s?t:Object.keys(t),n=i.length,r=s?e:Object.keys(e),a=r.length,o=s?[]:{},h=new Set(i);let u=0;for(let l=0;l<a;l++){const i=s?l:r[l];(!s&&h.has(i)||s)&&void 0===t[i]&&void 0===e[i]?(o[i]=void 0,u++):(o[i]=he(t[i],e[i]),o[i]===t[i]&&void 0!==t[i]&&u++)}return n===a&&u===n?t:o}return e}function ue(t,e){if(!e||Object.keys(t).length!==Object.keys(e).length)return!1;for(const s in t)if(t[s]!==e[s])return!1;return!0}function le(t){return Array.isArray(t)&&t.length===Object.keys(t).length}function ce(t){if(!de(t))return!1;const e=t.constructor;if(void 0===e)return!0;const s=e.prototype;return!!de(s)&&(!!s.hasOwnProperty("isPrototypeOf")&&Object.getPrototypeOf(t)===Object.prototype)}function de(t){return"[object Object]"===Object.prototype.toString.call(t)}function fe(t,e,s){return"function"==typeof s.structuralSharing?s.structuralSharing(t,e):!1!==s.structuralSharing?he(t,e):e}function pe(t,e,s=0){const i=[...t,e];return s&&i.length>s?i.slice(1):i}function ve(t,e,s=0){const i=[e,...t];return s&&i.length>s?i.slice(0,-1):i}var ye=Symbol();function be(t,e){return!t.queryFn&&(null==e?void 0:e.initialPromise)?()=>e.initialPromise:t.queryFn&&t.queryFn!==ye?t.queryFn:()=>Promise.reject(new Error(`Missing queryFn: '${t.queryHash}'`))}function ge(t,e){return"function"==typeof t?t(...e):!!t}var me=new(i=class extends Vt{constructor(){super(),At(this,t),At(this,e),At(this,s),Dt(this,s,(t=>{if(!Xt&&window.addEventListener){const e=()=>t();return window.addEventListener("visibilitychange",e,!1),()=>{window.removeEventListener("visibilitychange",e)}}}))}onSubscribe(){Wt(this,e)||this.setEventListener(Wt(this,s))}onUnsubscribe(){var t;this.hasListeners()||(null==(t=Wt(this,e))||t.call(this),Dt(this,e,void 0))}setEventListener(t){var i;Dt(this,s,t),null==(i=Wt(this,e))||i.call(this),Dt(this,e,t((t=>{"boolean"==typeof t?this.setFocused(t):this.onFocus()})))}setFocused(e){Wt(this,t)!==e&&(Dt(this,t,e),this.onFocus())}onFocus(){const t=this.isFocused();this.listeners.forEach((e=>{e(t)}))}isFocused(){var e;return"boolean"==typeof Wt(this,t)?Wt(this,t):"hidden"!==(null==(e=globalThis.document)?void 0:e.visibilityState)}},t=new WeakMap,e=new WeakMap,s=new WeakMap,i),we=new(o=class extends Vt{constructor(){super(),At(this,n,!0),At(this,r),At(this,a),Dt(this,a,(t=>{if(!Xt&&window.addEventListener){const e=()=>t(!0),s=()=>t(!1);return window.addEventListener("online",e,!1),window.addEventListener("offline",s,!1),()=>{window.removeEventListener("online",e),window.removeEventListener("offline",s)}}}))}onSubscribe(){Wt(this,r)||this.setEventListener(Wt(this,a))}onUnsubscribe(){var t;this.hasListeners()||(null==(t=Wt(this,r))||t.call(this),Dt(this,r,void 0))}setEventListener(t){var e;Dt(this,a,t),null==(e=Wt(this,r))||e.call(this),Dt(this,r,t(this.setOnline.bind(this)))}setOnline(t){Wt(this,n)!==t&&(Dt(this,n,t),this.listeners.forEach((e=>{e(t)})))}isOnline(){return Wt(this,n)}},n=new WeakMap,r=new WeakMap,a=new WeakMap,o);function Oe(){let t,e;const s=new Promise(((s,i)=>{t=s,e=i}));function i(t){Object.assign(s,t),delete s.resolve,delete s.reject}return s.status="pending",s.catch((()=>{})),s.resolve=e=>{i({status:"fulfilled",value:e}),t(e)},s.reject=t=>{i({status:"rejected",reason:t}),e(t)},s}function Pe(t){return Math.min(1e3*xt(2,t),3e4)}function Me(t){return"online"!==(null!=t?t:"online")||we.isOnline()}var ke=class extends Error{constructor(t){super("CancelledError"),this.revert=null==t?void 0:t.revert,this.silent=null==t?void 0:t.silent}};function Re(t){return t instanceof ke}function Se(t){let e,s=!1,i=0,n=!1;const r=Oe(),a=()=>me.isFocused()&&("always"===t.networkMode||we.isOnline())&&t.canRun(),o=()=>Me(t.networkMode)&&t.canRun(),h=s=>{var i;n||(n=!0,null==(i=t.onSuccess)||i.call(t,s),null==e||e(),r.resolve(s))},u=s=>{var i;n||(n=!0,null==(i=t.onError)||i.call(t,s),null==e||e(),r.reject(s))},l=()=>new Promise((s=>{var i;e=t=>{(n||a())&&s(t)},null==(i=t.onPause)||i.call(t)})).then((()=>{var s;e=void 0,n||null==(s=t.onContinue)||s.call(t)})),c=()=>{if(n)return;let e;const r=0===i?t.initialPromise:void 0;try{e=null!=r?r:t.fn()}catch(o){e=Promise.reject(o)}Promise.resolve(e).then(h).catch((e=>{var r,o,h;if(n)return;const d=null!=(r=t.retry)?r:Xt?0:3,f=null!=(o=t.retryDelay)?o:Pe,p="function"==typeof f?f(i,e):f,v=!0===d||"number"==typeof d&&i<d||"function"==typeof d&&d(i,e);var y;!s&&v?(i++,null==(h=t.onFail)||h.call(t,i,e),(y=p,new Promise((t=>{setTimeout(t,y)}))).then((()=>a()?void 0:l())).then((()=>{s?u(e):c()}))):u(e)}))};return{promise:r,cancel:e=>{var s;n||(u(new ke(e)),null==(s=t.abort)||s.call(t))},continue:()=>(null==e||e(),r),cancelRetry:()=>{s=!0},continueRetry:()=>{s=!1},canStart:o,start:()=>(o()?c():l().then(c),r)}}var Ce=t=>setTimeout(t,0);var xe=function(){let t=[],e=0,s=t=>{t()},i=t=>{t()},n=Ce;const r=i=>{e?t.push(i):n((()=>{s(i)}))};return{batch:r=>{let a;e++;try{a=r()}finally{e--,e||(()=>{const e=t;t=[],e.length&&n((()=>{i((()=>{e.forEach((t=>{s(t)}))}))}))})()}return a},batchCalls:t=>(...e)=>{r((()=>{t(...e)}))},schedule:r,setNotifyFunction:t=>{s=t},setBatchNotifyFunction:t=>{i=t},setScheduler:t=>{n=t}}}(),Ee=(u=class{constructor(){At(this,h)}destroy(){this.clearGcTimeout()}scheduleGc(){this.clearGcTimeout(),Zt(this.gcTime)&&Dt(this,h,setTimeout((()=>{this.optionalRemove()}),this.gcTime))}updateGcTime(t){this.gcTime=Math.max(this.gcTime||0,null!=t?t:Xt?1/0:3e5)}clearGcTimeout(){Wt(this,h)&&(clearTimeout(Wt(this,h)),Dt(this,h,void 0))}},h=new WeakMap,u),Fe=(m=class extends Ee{constructor(t){var e;super(),At(this,b),At(this,l),At(this,c),At(this,d),At(this,f),At(this,p),At(this,v),At(this,y),Dt(this,y,!1),Dt(this,v,t.defaultOptions),this.setOptions(t.options),this.observers=[],Dt(this,f,t.client),Dt(this,d,Wt(this,f).getQueryCache()),this.queryKey=t.queryKey,this.queryHash=t.queryHash,Dt(this,l,function(t){const e="function"==typeof t.initialData?t.initialData():t.initialData,s=void 0!==e,i=s?"function"==typeof t.initialDataUpdatedAt?t.initialDataUpdatedAt():t.initialDataUpdatedAt:0;return{data:e,dataUpdateCount:0,dataUpdatedAt:s?null!=i?i:Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchFailureReason:null,fetchMeta:null,isInvalidated:!1,status:s?"success":"pending",fetchStatus:"idle"}}(this.options)),this.state=null!=(e=t.state)?e:Wt(this,l),this.scheduleGc()}get meta(){return this.options.meta}get promise(){var t;return null==(t=Wt(this,p))?void 0:t.promise}setOptions(t){this.options=Ft(Ft({},Wt(this,v)),t),this.updateGcTime(this.options.gcTime)}optionalRemove(){this.observers.length||"idle"!==this.state.fetchStatus||Wt(this,d).remove(this)}setData(t,e){const s=fe(this.state.data,t,this.options);return jt(this,b,g).call(this,{data:s,type:"success",dataUpdatedAt:null==e?void 0:e.updatedAt,manual:null==e?void 0:e.manual}),s}setState(t,e){jt(this,b,g).call(this,{type:"setState",state:t,setStateOptions:e})}cancel(t){var e,s;const i=null==(e=Wt(this,p))?void 0:e.promise;return null==(s=Wt(this,p))||s.cancel(t),i?i.then(Yt).catch(Yt):Promise.resolve()}destroy(){super.destroy(),this.cancel({silent:!0})}reset(){this.destroy(),this.setState(Wt(this,l))}isActive(){return this.observers.some((t=>!1!==se(t.options.enabled,this)))}isDisabled(){return this.getObserversCount()>0?!this.isActive():this.options.queryFn===ye||this.state.dataUpdateCount+this.state.errorUpdateCount===0}isStatic(){return this.getObserversCount()>0&&this.observers.some((t=>"static"===ee(t.options.staleTime,this)))}isStale(){return this.getObserversCount()>0?this.observers.some((t=>t.getCurrentResult().isStale)):void 0===this.state.data||this.state.isInvalidated}isStaleByTime(t=0){return void 0===this.state.data||"static"!==t&&(!!this.state.isInvalidated||!te(this.state.dataUpdatedAt,t))}onFocus(){var t;const e=this.observers.find((t=>t.shouldFetchOnWindowFocus()));null==e||e.refetch({cancelRefetch:!1}),null==(t=Wt(this,p))||t.continue()}onOnline(){var t;const e=this.observers.find((t=>t.shouldFetchOnReconnect()));null==e||e.refetch({cancelRefetch:!1}),null==(t=Wt(this,p))||t.continue()}addObserver(t){this.observers.includes(t)||(this.observers.push(t),this.clearGcTimeout(),Wt(this,d).notify({type:"observerAdded",query:this,observer:t}))}removeObserver(t){this.observers.includes(t)&&(this.observers=this.observers.filter((e=>e!==t)),this.observers.length||(Wt(this,p)&&(Wt(this,y)?Wt(this,p).cancel({revert:!0}):Wt(this,p).cancelRetry()),this.scheduleGc()),Wt(this,d).notify({type:"observerRemoved",query:this,observer:t}))}getObserversCount(){return this.observers.length}invalidate(){this.state.isInvalidated||jt(this,b,g).call(this,{type:"invalidate"})}fetch(t,e){var s,i,n;if("idle"!==this.state.fetchStatus)if(void 0!==this.state.data&&(null==e?void 0:e.cancelRefetch))this.cancel({silent:!0});else if(Wt(this,p))return Wt(this,p).continueRetry(),Wt(this,p).promise;if(t&&this.setOptions(t),!this.options.queryFn){const t=this.observers.find((t=>t.options.queryFn));t&&this.setOptions(t.options)}const r=new AbortController,a=t=>{Object.defineProperty(t,"signal",{enumerable:!0,get:()=>(Dt(this,y,!0),r.signal)})},o=()=>{const t=be(this.options,e),s=(()=>{const t={client:Wt(this,f),queryKey:this.queryKey,meta:this.meta};return a(t),t})();return Dt(this,y,!1),this.options.persister?this.options.persister(t,s,this):t(s)},h=(()=>{const t={fetchOptions:e,options:this.options,queryKey:this.queryKey,client:Wt(this,f),state:this.state,fetchFn:o};return a(t),t})();null==(s=this.options.behavior)||s.onFetch(h,this),Dt(this,c,this.state),"idle"!==this.state.fetchStatus&&this.state.fetchMeta===(null==(i=h.fetchOptions)?void 0:i.meta)||jt(this,b,g).call(this,{type:"fetch",meta:null==(n=h.fetchOptions)?void 0:n.meta});const u=t=>{var e,s,i,n;Re(t)&&t.silent||jt(this,b,g).call(this,{type:"error",error:t}),Re(t)||(null==(s=(e=Wt(this,d).config).onError)||s.call(e,t,this),null==(n=(i=Wt(this,d).config).onSettled)||n.call(i,this.state.data,t,this)),this.scheduleGc()};return Dt(this,p,Se({initialPromise:null==e?void 0:e.initialPromise,fn:h.fetchFn,abort:r.abort.bind(r),onSuccess:t=>{var e,s,i,n;if(void 0!==t){try{this.setData(t)}catch(r){return void u(r)}null==(s=(e=Wt(this,d).config).onSuccess)||s.call(e,t,this),null==(n=(i=Wt(this,d).config).onSettled)||n.call(i,t,this.state.error,this),this.scheduleGc()}else u(new Error(`${this.queryHash} data is undefined`))},onError:u,onFail:(t,e)=>{jt(this,b,g).call(this,{type:"failed",failureCount:t,error:e})},onPause:()=>{jt(this,b,g).call(this,{type:"pause"})},onContinue:()=>{jt(this,b,g).call(this,{type:"continue"})},retry:h.options.retry,retryDelay:h.options.retryDelay,networkMode:h.options.networkMode,canRun:()=>!0})),Wt(this,p).start()}},l=new WeakMap,c=new WeakMap,d=new WeakMap,f=new WeakMap,p=new WeakMap,v=new WeakMap,y=new WeakMap,b=new WeakSet,g=function(t){this.state=(e=>{var s,i;switch(t.type){case"failed":return qt(Ft({},e),{fetchFailureCount:t.failureCount,fetchFailureReason:t.error});case"pause":return qt(Ft({},e),{fetchStatus:"paused"});case"continue":return qt(Ft({},e),{fetchStatus:"fetching"});case"fetch":return qt(Ft(Ft({},e),qe(e.data,this.options)),{fetchMeta:null!=(s=t.meta)?s:null});case"success":return Ft(qt(Ft({},e),{data:t.data,dataUpdateCount:e.dataUpdateCount+1,dataUpdatedAt:null!=(i=t.dataUpdatedAt)?i:Date.now(),error:null,isInvalidated:!1,status:"success"}),!t.manual&&{fetchStatus:"idle",fetchFailureCount:0,fetchFailureReason:null});case"error":const n=t.error;return Re(n)&&n.revert&&Wt(this,c)?qt(Ft({},Wt(this,c)),{fetchStatus:"idle"}):qt(Ft({},e),{error:n,errorUpdateCount:e.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:e.fetchFailureCount+1,fetchFailureReason:n,fetchStatus:"idle",status:"error"});case"invalidate":return qt(Ft({},e),{isInvalidated:!0});case"setState":return Ft(Ft({},e),t.state)}})(this.state),xe.batch((()=>{this.observers.forEach((t=>{t.onQueryUpdate()})),Wt(this,d).notify({query:this,type:"updated",action:t})}))},m);function qe(t,e){return Ft({fetchFailureCount:0,fetchFailureReason:null,fetchStatus:Me(e.networkMode)?"fetching":"paused"},void 0===t&&{error:null,status:"pending"})}var Qe=(O=class extends Vt{constructor(t={}){super(),At(this,w),this.config=t,Dt(this,w,new Map)}build(t,e,s){var i;const n=e.queryKey,r=null!=(i=e.queryHash)?i:re(n,e);let a=this.get(r);return a||(a=new Fe({client:t,queryKey:n,queryHash:r,options:t.defaultQueryOptions(e),state:s,defaultOptions:t.getQueryDefaults(n)}),this.add(a)),a}add(t){Wt(this,w).has(t.queryHash)||(Wt(this,w).set(t.queryHash,t),this.notify({type:"added",query:t}))}remove(t){const e=Wt(this,w).get(t.queryHash);e&&(t.destroy(),e===t&&Wt(this,w).delete(t.queryHash),this.notify({type:"removed",query:t}))}clear(){xe.batch((()=>{this.getAll().forEach((t=>{this.remove(t)}))}))}get(t){return Wt(this,w).get(t)}getAll(){return[...Wt(this,w).values()]}find(t){const e=Ft({exact:!0},t);return this.getAll().find((t=>ie(e,t)))}findAll(t={}){const e=this.getAll();return Object.keys(t).length>0?e.filter((e=>ie(t,e))):e}notify(t){xe.batch((()=>{this.listeners.forEach((e=>{e(t)}))}))}onFocus(){xe.batch((()=>{this.getAll().forEach((t=>{t.onFocus()}))}))}onOnline(){xe.batch((()=>{this.getAll().forEach((t=>{t.onOnline()}))}))}},w=new WeakMap,O),We=(C=class extends Ee{constructor(t){super(),At(this,R),At(this,P),At(this,M),At(this,k),this.mutationId=t.mutationId,Dt(this,M,t.mutationCache),Dt(this,P,[]),this.state=t.state||{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0},this.setOptions(t.options),this.scheduleGc()}setOptions(t){this.options=t,this.updateGcTime(this.options.gcTime)}get meta(){return this.options.meta}addObserver(t){Wt(this,P).includes(t)||(Wt(this,P).push(t),this.clearGcTimeout(),Wt(this,M).notify({type:"observerAdded",mutation:this,observer:t}))}removeObserver(t){Dt(this,P,Wt(this,P).filter((e=>e!==t))),this.scheduleGc(),Wt(this,M).notify({type:"observerRemoved",mutation:this,observer:t})}optionalRemove(){Wt(this,P).length||("pending"===this.state.status?this.scheduleGc():Wt(this,M).remove(this))}continue(){var t,e;return null!=(e=null==(t=Wt(this,k))?void 0:t.continue())?e:this.execute(this.state.variables)}execute(t){return Tt(this,null,(function*(){var e,s,i,n,r,a,o,h,u,l,c,d,f,p,v,y,b,g,m,w,O;const P=()=>{jt(this,R,S).call(this,{type:"continue"})};Dt(this,k,Se({fn:()=>this.options.mutationFn?this.options.mutationFn(t):Promise.reject(new Error("No mutationFn found")),onFail:(t,e)=>{jt(this,R,S).call(this,{type:"failed",failureCount:t,error:e})},onPause:()=>{jt(this,R,S).call(this,{type:"pause"})},onContinue:P,retry:null!=(e=this.options.retry)?e:0,retryDelay:this.options.retryDelay,networkMode:this.options.networkMode,canRun:()=>Wt(this,M).canRun(this)}));const C="pending"===this.state.status,x=!Wt(this,k).canStart();try{if(C)P();else{jt(this,R,S).call(this,{type:"pending",variables:t,isPaused:x}),yield null==(i=(s=Wt(this,M).config).onMutate)?void 0:i.call(s,t,this);const e=yield null==(r=(n=this.options).onMutate)?void 0:r.call(n,t);e!==this.state.context&&jt(this,R,S).call(this,{type:"pending",context:e,variables:t,isPaused:x})}const e=yield Wt(this,k).start();return yield null==(o=(a=Wt(this,M).config).onSuccess)?void 0:o.call(a,e,t,this.state.context,this),yield null==(u=(h=this.options).onSuccess)?void 0:u.call(h,e,t,this.state.context),yield null==(c=(l=Wt(this,M).config).onSettled)?void 0:c.call(l,e,null,this.state.variables,this.state.context,this),yield null==(f=(d=this.options).onSettled)?void 0:f.call(d,e,null,t,this.state.context),jt(this,R,S).call(this,{type:"success",data:e}),e}catch(E){try{throw yield null==(v=(p=Wt(this,M).config).onError)?void 0:v.call(p,E,t,this.state.context,this),yield null==(b=(y=this.options).onError)?void 0:b.call(y,E,t,this.state.context),yield null==(m=(g=Wt(this,M).config).onSettled)?void 0:m.call(g,void 0,E,this.state.variables,this.state.context,this),yield null==(O=(w=this.options).onSettled)?void 0:O.call(w,void 0,E,t,this.state.context),E}finally{jt(this,R,S).call(this,{type:"error",error:E})}}finally{Wt(this,M).runNext(this)}}))}},P=new WeakMap,M=new WeakMap,k=new WeakMap,R=new WeakSet,S=function(t){this.state=(e=>{switch(t.type){case"failed":return qt(Ft({},e),{failureCount:t.failureCount,failureReason:t.error});case"pause":return qt(Ft({},e),{isPaused:!0});case"continue":return qt(Ft({},e),{isPaused:!1});case"pending":return qt(Ft({},e),{context:t.context,data:void 0,failureCount:0,failureReason:null,error:null,isPaused:t.isPaused,status:"pending",variables:t.variables,submittedAt:Date.now()});case"success":return qt(Ft({},e),{data:t.data,failureCount:0,failureReason:null,error:null,status:"success",isPaused:!1});case"error":return qt(Ft({},e),{data:void 0,error:t.error,failureCount:e.failureCount+1,failureReason:t.error,isPaused:!1,status:"error"})}})(this.state),xe.batch((()=>{Wt(this,P).forEach((e=>{e.onMutationUpdate(t)})),Wt(this,M).notify({mutation:this,type:"updated",action:t})}))},C);var Ae=(q=class extends Vt{constructor(t={}){super(),At(this,x),At(this,E),At(this,F),this.config=t,Dt(this,x,new Set),Dt(this,E,new Map),Dt(this,F,0)}build(t,e,s){const i=new We({mutationCache:this,mutationId:++Ut(this,F)._,options:t.defaultMutationOptions(e),state:s});return this.add(i),i}add(t){Wt(this,x).add(t);const e=De(t);if("string"==typeof e){const s=Wt(this,E).get(e);s?s.push(t):Wt(this,E).set(e,[t])}this.notify({type:"added",mutation:t})}remove(t){if(Wt(this,x).delete(t)){const e=De(t);if("string"==typeof e){const s=Wt(this,E).get(e);if(s)if(s.length>1){const e=s.indexOf(t);-1!==e&&s.splice(e,1)}else s[0]===t&&Wt(this,E).delete(e)}}this.notify({type:"removed",mutation:t})}canRun(t){const e=De(t);if("string"==typeof e){const s=Wt(this,E).get(e),i=null==s?void 0:s.find((t=>"pending"===t.state.status));return!i||i===t}return!0}runNext(t){var e,s;const i=De(t);if("string"==typeof i){const n=null==(e=Wt(this,E).get(i))?void 0:e.find((e=>e!==t&&e.state.isPaused));return null!=(s=null==n?void 0:n.continue())?s:Promise.resolve()}return Promise.resolve()}clear(){xe.batch((()=>{Wt(this,x).forEach((t=>{this.notify({type:"removed",mutation:t})})),Wt(this,x).clear(),Wt(this,E).clear()}))}getAll(){return Array.from(Wt(this,x))}find(t){const e=Ft({exact:!0},t);return this.getAll().find((t=>ne(e,t)))}findAll(t={}){return this.getAll().filter((e=>ne(t,e)))}notify(t){xe.batch((()=>{this.listeners.forEach((e=>{e(t)}))}))}resumePausedMutations(){const t=this.getAll().filter((t=>t.state.isPaused));return xe.batch((()=>Promise.all(t.map((t=>t.continue().catch(Yt))))))}},x=new WeakMap,E=new WeakMap,F=new WeakMap,q);function De(t){var e;return null==(e=t.options.scope)?void 0:e.id}function je(t){return{onFetch:(e,s)=>{var i,n,r,a,o;const h=e.options,u=null==(r=null==(n=null==(i=e.fetchOptions)?void 0:i.meta)?void 0:n.fetchMore)?void 0:r.direction,l=(null==(a=e.state.data)?void 0:a.pages)||[],c=(null==(o=e.state.data)?void 0:o.pageParams)||[];let d={pages:[],pageParams:[]},f=0;const p=()=>Tt(null,null,(function*(){var s;let i=!1;const n=be(e.options,e.fetchOptions),r=(t,s,r)=>Tt(null,null,(function*(){if(i)return Promise.reject();if(null==s&&t.pages.length)return Promise.resolve(t);const a=(()=>{const t={client:e.client,queryKey:e.queryKey,pageParam:s,direction:r?"backward":"forward",meta:e.options.meta};var n;return n=t,Object.defineProperty(n,"signal",{enumerable:!0,get:()=>(e.signal.aborted?i=!0:e.signal.addEventListener("abort",(()=>{i=!0})),e.signal)}),t})(),o=yield n(a),{maxPages:h}=e.options,u=r?ve:pe;return{pages:u(t.pages,o,h),pageParams:u(t.pageParams,s,h)}}));if(u&&l.length){const t="backward"===u,e={pages:l,pageParams:c},s=(t?Te:Ue)(h,e);d=yield r(e,s,t)}else{const e=null!=t?t:l.length;do{const t=0===f?null!=(s=c[0])?s:h.initialPageParam:Ue(h,d);if(f>0&&null==t)break;d=yield r(d,t),f++}while(f<e)}return d}));e.options.persister?e.fetchFn=()=>{var t,i;return null==(i=(t=e.options).persister)?void 0:i.call(t,p,{client:e.client,queryKey:e.queryKey,meta:e.options.meta,signal:e.signal},s)}:e.fetchFn=p}}}function Ue(t,{pages:e,pageParams:s}){const i=e.length-1;return e.length>0?t.getNextPageParam(e[i],e,s[i],s):void 0}function Te(t,{pages:e,pageParams:s}){var i;return e.length>0?null==(i=t.getPreviousPageParam)?void 0:i.call(t,e[0],e,s[0],s):void 0}function Ke(t,e){return!!e&&null!=Ue(t,e)}function Ie(t,e){return!(!e||!t.getPreviousPageParam)&&null!=Te(t,e)}var Le=(I=class{constructor(t={}){At(this,Q),At(this,W),At(this,A),At(this,D),At(this,j),At(this,U),At(this,T),At(this,K),Dt(this,Q,t.queryCache||new Qe),Dt(this,W,t.mutationCache||new Ae),Dt(this,A,t.defaultOptions||{}),Dt(this,D,new Map),Dt(this,j,new Map),Dt(this,U,0)}mount(){Ut(this,U)._++,1===Wt(this,U)&&(Dt(this,T,me.subscribe((t=>Tt(this,null,(function*(){t&&(yield this.resumePausedMutations(),Wt(this,Q).onFocus())}))))),Dt(this,K,we.subscribe((t=>Tt(this,null,(function*(){t&&(yield this.resumePausedMutations(),Wt(this,Q).onOnline())}))))))}unmount(){var t,e;Ut(this,U)._--,0===Wt(this,U)&&(null==(t=Wt(this,T))||t.call(this),Dt(this,T,void 0),null==(e=Wt(this,K))||e.call(this),Dt(this,K,void 0))}isFetching(t){return Wt(this,Q).findAll(qt(Ft({},t),{fetchStatus:"fetching"})).length}isMutating(t){return Wt(this,W).findAll(qt(Ft({},t),{status:"pending"})).length}getQueryData(t){var e;const s=this.defaultQueryOptions({queryKey:t});return null==(e=Wt(this,Q).get(s.queryHash))?void 0:e.state.data}ensureQueryData(t){const e=this.defaultQueryOptions(t),s=Wt(this,Q).build(this,e),i=s.state.data;return void 0===i?this.fetchQuery(t):(t.revalidateIfStale&&s.isStaleByTime(ee(e.staleTime,s))&&this.prefetchQuery(e),Promise.resolve(i))}getQueriesData(t){return Wt(this,Q).findAll(t).map((({queryKey:t,state:e})=>[t,e.data]))}setQueryData(t,e,s){const i=this.defaultQueryOptions({queryKey:t}),n=Wt(this,Q).get(i.queryHash),r=function(t,e){return"function"==typeof t?t(e):t}(e,null==n?void 0:n.state.data);if(void 0!==r)return Wt(this,Q).build(this,i).setData(r,qt(Ft({},s),{manual:!0}))}setQueriesData(t,e,s){return xe.batch((()=>Wt(this,Q).findAll(t).map((({queryKey:t})=>[t,this.setQueryData(t,e,s)]))))}getQueryState(t){var e;const s=this.defaultQueryOptions({queryKey:t});return null==(e=Wt(this,Q).get(s.queryHash))?void 0:e.state}removeQueries(t){const e=Wt(this,Q);xe.batch((()=>{e.findAll(t).forEach((t=>{e.remove(t)}))}))}resetQueries(t,e){const s=Wt(this,Q);return xe.batch((()=>(s.findAll(t).forEach((t=>{t.reset()})),this.refetchQueries(Ft({type:"active"},t),e))))}cancelQueries(t,e={}){const s=Ft({revert:!0},e),i=xe.batch((()=>Wt(this,Q).findAll(t).map((t=>t.cancel(s)))));return Promise.all(i).then(Yt).catch(Yt)}invalidateQueries(t,e={}){return xe.batch((()=>{var s,i;return Wt(this,Q).findAll(t).forEach((t=>{t.invalidate()})),"none"===(null==t?void 0:t.refetchType)?Promise.resolve():this.refetchQueries(qt(Ft({},t),{type:null!=(i=null!=(s=null==t?void 0:t.refetchType)?s:null==t?void 0:t.type)?i:"active"}),e)}))}refetchQueries(t,e={}){var s;const i=qt(Ft({},e),{cancelRefetch:null==(s=e.cancelRefetch)||s}),n=xe.batch((()=>Wt(this,Q).findAll(t).filter((t=>!t.isDisabled()&&!t.isStatic())).map((t=>{let e=t.fetch(void 0,i);return i.throwOnError||(e=e.catch(Yt)),"paused"===t.state.fetchStatus?Promise.resolve():e}))));return Promise.all(n).then(Yt)}fetchQuery(t){const e=this.defaultQueryOptions(t);void 0===e.retry&&(e.retry=!1);const s=Wt(this,Q).build(this,e);return s.isStaleByTime(ee(e.staleTime,s))?s.fetch(e):Promise.resolve(s.state.data)}prefetchQuery(t){return this.fetchQuery(t).then(Yt).catch(Yt)}fetchInfiniteQuery(t){return t.behavior=je(t.pages),this.fetchQuery(t)}prefetchInfiniteQuery(t){return this.fetchInfiniteQuery(t).then(Yt).catch(Yt)}ensureInfiniteQueryData(t){return t.behavior=je(t.pages),this.ensureQueryData(t)}resumePausedMutations(){return we.isOnline()?Wt(this,W).resumePausedMutations():Promise.resolve()}getQueryCache(){return Wt(this,Q)}getMutationCache(){return Wt(this,W)}getDefaultOptions(){return Wt(this,A)}setDefaultOptions(t){Dt(this,A,t)}setQueryDefaults(t,e){Wt(this,D).set(ae(t),{queryKey:t,defaultOptions:e})}getQueryDefaults(t){const e=[...Wt(this,D).values()],s={};return e.forEach((e=>{oe(t,e.queryKey)&&Object.assign(s,e.defaultOptions)})),s}setMutationDefaults(t,e){Wt(this,j).set(ae(t),{mutationKey:t,defaultOptions:e})}getMutationDefaults(t){const e=[...Wt(this,j).values()],s={};return e.forEach((e=>{oe(t,e.mutationKey)&&Object.assign(s,e.defaultOptions)})),s}defaultQueryOptions(t){if(t._defaulted)return t;const e=qt(Ft(Ft(Ft({},Wt(this,A).queries),this.getQueryDefaults(t.queryKey)),t),{_defaulted:!0});return e.queryHash||(e.queryHash=re(e.queryKey,e)),void 0===e.refetchOnReconnect&&(e.refetchOnReconnect="always"!==e.networkMode),void 0===e.throwOnError&&(e.throwOnError=!!e.suspense),!e.networkMode&&e.persister&&(e.networkMode="offlineFirst"),e.queryFn===ye&&(e.enabled=!1),e}defaultMutationOptions(t){return(null==t?void 0:t._defaulted)?t:qt(Ft(Ft(Ft({},Wt(this,A).mutations),(null==t?void 0:t.mutationKey)&&this.getMutationDefaults(t.mutationKey)),t),{_defaulted:!0})}clear(){Wt(this,Q).clear(),Wt(this,W).clear()}},Q=new WeakMap,W=new WeakMap,A=new WeakMap,D=new WeakMap,j=new WeakMap,U=new WeakMap,T=new WeakMap,K=new WeakMap,I),_e=(dt=class extends Vt{constructor(t,e){super(),At(this,st),At(this,L),At(this,_),At(this,H),At(this,G),At(this,N),At(this,B),At(this,z),At(this,$),At(this,J),At(this,V),At(this,X),At(this,Y),At(this,Z),At(this,tt),At(this,et,new Set),this.options=e,Dt(this,L,t),Dt(this,$,null),Dt(this,z,Oe()),this.options.experimental_prefetchInRender||Wt(this,z).reject(new Error("experimental_prefetchInRender feature flag is not enabled")),this.bindMethods(),this.setOptions(e)}bindMethods(){this.refetch=this.refetch.bind(this)}onSubscribe(){1===this.listeners.size&&(Wt(this,_).addObserver(this),He(Wt(this,_),this.options)?jt(this,st,it).call(this):this.updateResult(),jt(this,st,ot).call(this))}onUnsubscribe(){this.hasListeners()||this.destroy()}shouldFetchOnReconnect(){return Ge(Wt(this,_),this.options,this.options.refetchOnReconnect)}shouldFetchOnWindowFocus(){return Ge(Wt(this,_),this.options,this.options.refetchOnWindowFocus)}destroy(){this.listeners=new Set,jt(this,st,ht).call(this),jt(this,st,ut).call(this),Wt(this,_).removeObserver(this)}setOptions(t){const e=this.options,s=Wt(this,_);if(this.options=Wt(this,L).defaultQueryOptions(t),void 0!==this.options.enabled&&"boolean"!=typeof this.options.enabled&&"function"!=typeof this.options.enabled&&"boolean"!=typeof se(this.options.enabled,Wt(this,_)))throw new Error("Expected enabled to be a boolean or a callback that returns a boolean");jt(this,st,lt).call(this),Wt(this,_).setOptions(this.options),e._defaulted&&!ue(this.options,e)&&Wt(this,L).getQueryCache().notify({type:"observerOptionsUpdated",query:Wt(this,_),observer:this});const i=this.hasListeners();i&&Ne(Wt(this,_),s,this.options,e)&&jt(this,st,it).call(this),this.updateResult(),!i||Wt(this,_)===s&&se(this.options.enabled,Wt(this,_))===se(e.enabled,Wt(this,_))&&ee(this.options.staleTime,Wt(this,_))===ee(e.staleTime,Wt(this,_))||jt(this,st,nt).call(this);const n=jt(this,st,rt).call(this);!i||Wt(this,_)===s&&se(this.options.enabled,Wt(this,_))===se(e.enabled,Wt(this,_))&&n===Wt(this,tt)||jt(this,st,at).call(this,n)}getOptimisticResult(t){const e=Wt(this,L).getQueryCache().build(Wt(this,L),t),s=this.createResult(e,t);return function(t,e){if(!ue(t.getCurrentResult(),e))return!0;return!1}(this,s)&&(Dt(this,G,s),Dt(this,B,this.options),Dt(this,N,Wt(this,_).state)),s}getCurrentResult(){return Wt(this,G)}trackResult(t,e){return new Proxy(t,{get:(t,s)=>(this.trackProp(s),null==e||e(s),Reflect.get(t,s))})}trackProp(t){Wt(this,et).add(t)}getCurrentQuery(){return Wt(this,_)}refetch(t={}){var e=((t,e)=>{var s={};for(var i in t)Rt.call(t,i)&&e.indexOf(i)<0&&(s[i]=t[i]);if(null!=t&&kt)for(var i of kt(t))e.indexOf(i)<0&&St.call(t,i)&&(s[i]=t[i]);return s})(t,[]);return this.fetch(Ft({},e))}fetchOptimistic(t){const e=Wt(this,L).defaultQueryOptions(t),s=Wt(this,L).getQueryCache().build(Wt(this,L),e);return s.fetch().then((()=>this.createResult(s,e)))}fetch(t){var e;return jt(this,st,it).call(this,qt(Ft({},t),{cancelRefetch:null==(e=t.cancelRefetch)||e})).then((()=>(this.updateResult(),Wt(this,G))))}createResult(t,e){var s;const i=Wt(this,_),n=this.options,r=Wt(this,G),a=Wt(this,N),o=Wt(this,B),h=t!==i?t.state:Wt(this,H),{state:u}=t;let l,c=Ft({},u),d=!1;if(e._optimisticResults){const s=this.hasListeners(),r=!s&&He(t,e),a=s&&Ne(t,i,e,n);(r||a)&&(c=Ft(Ft({},c),qe(u.data,t.options))),"isRestoring"===e._optimisticResults&&(c.fetchStatus="idle")}let{error:f,errorUpdatedAt:p,status:v}=c;l=c.data;let y=!1;if(void 0!==e.placeholderData&&void 0===l&&"pending"===v){let t;(null==r?void 0:r.isPlaceholderData)&&e.placeholderData===(null==o?void 0:o.placeholderData)?(t=r.data,y=!0):t="function"==typeof e.placeholderData?e.placeholderData(null==(s=Wt(this,X))?void 0:s.state.data,Wt(this,X)):e.placeholderData,void 0!==t&&(v="success",l=fe(null==r?void 0:r.data,t,e),d=!0)}if(e.select&&void 0!==l&&!y)if(r&&l===(null==a?void 0:a.data)&&e.select===Wt(this,J))l=Wt(this,V);else try{Dt(this,J,e.select),l=e.select(l),l=fe(null==r?void 0:r.data,l,e),Dt(this,V,l),Dt(this,$,null)}catch(M){Dt(this,$,M)}Wt(this,$)&&(f=Wt(this,$),l=Wt(this,V),p=Date.now(),v="error");const b="fetching"===c.fetchStatus,g="pending"===v,m="error"===v,w=g&&b,O=void 0!==l,P={status:v,fetchStatus:c.fetchStatus,isPending:g,isSuccess:"success"===v,isError:m,isInitialLoading:w,isLoading:w,data:l,dataUpdatedAt:c.dataUpdatedAt,error:f,errorUpdatedAt:p,failureCount:c.fetchFailureCount,failureReason:c.fetchFailureReason,errorUpdateCount:c.errorUpdateCount,isFetched:c.dataUpdateCount>0||c.errorUpdateCount>0,isFetchedAfterMount:c.dataUpdateCount>h.dataUpdateCount||c.errorUpdateCount>h.errorUpdateCount,isFetching:b,isRefetching:b&&!g,isLoadingError:m&&!O,isPaused:"paused"===c.fetchStatus,isPlaceholderData:d,isRefetchError:m&&O,isStale:Be(t,e),refetch:this.refetch,promise:Wt(this,z)};if(this.options.experimental_prefetchInRender){const e=t=>{"error"===P.status?t.reject(P.error):void 0!==P.data&&t.resolve(P.data)},s=()=>{const t=Dt(this,z,P.promise=Oe());e(t)},n=Wt(this,z);switch(n.status){case"pending":t.queryHash===i.queryHash&&e(n);break;case"fulfilled":"error"!==P.status&&P.data===n.value||s();break;case"rejected":"error"===P.status&&P.error===n.reason||s()}}return P}updateResult(){const t=Wt(this,G),e=this.createResult(Wt(this,_),this.options);if(Dt(this,N,Wt(this,_).state),Dt(this,B,this.options),void 0!==Wt(this,N).data&&Dt(this,X,Wt(this,_)),ue(e,t))return;Dt(this,G,e);jt(this,st,ct).call(this,{listeners:(()=>{if(!t)return!0;const{notifyOnChangeProps:e}=this.options,s="function"==typeof e?e():e;if("all"===s||!s&&!Wt(this,et).size)return!0;const i=new Set(null!=s?s:Wt(this,et));return this.options.throwOnError&&i.add("error"),Object.keys(Wt(this,G)).some((e=>{const s=e;return Wt(this,G)[s]!==t[s]&&i.has(s)}))})()})}onQueryUpdate(){this.updateResult(),this.hasListeners()&&jt(this,st,ot).call(this)}},L=new WeakMap,_=new WeakMap,H=new WeakMap,G=new WeakMap,N=new WeakMap,B=new WeakMap,z=new WeakMap,$=new WeakMap,J=new WeakMap,V=new WeakMap,X=new WeakMap,Y=new WeakMap,Z=new WeakMap,tt=new WeakMap,et=new WeakMap,st=new WeakSet,it=function(t){jt(this,st,lt).call(this);let e=Wt(this,_).fetch(this.options,t);return(null==t?void 0:t.throwOnError)||(e=e.catch(Yt)),e},nt=function(){jt(this,st,ht).call(this);const t=ee(this.options.staleTime,Wt(this,_));if(Xt||Wt(this,G).isStale||!Zt(t))return;const e=te(Wt(this,G).dataUpdatedAt,t);Dt(this,Y,setTimeout((()=>{Wt(this,G).isStale||this.updateResult()}),e+1))},rt=function(){var t;return null!=(t="function"==typeof this.options.refetchInterval?this.options.refetchInterval(Wt(this,_)):this.options.refetchInterval)&&t},at=function(t){jt(this,st,ut).call(this),Dt(this,tt,t),!Xt&&!1!==se(this.options.enabled,Wt(this,_))&&Zt(Wt(this,tt))&&0!==Wt(this,tt)&&Dt(this,Z,setInterval((()=>{(this.options.refetchIntervalInBackground||me.isFocused())&&jt(this,st,it).call(this)}),Wt(this,tt)))},ot=function(){jt(this,st,nt).call(this),jt(this,st,at).call(this,jt(this,st,rt).call(this))},ht=function(){Wt(this,Y)&&(clearTimeout(Wt(this,Y)),Dt(this,Y,void 0))},ut=function(){Wt(this,Z)&&(clearInterval(Wt(this,Z)),Dt(this,Z,void 0))},lt=function(){const t=Wt(this,L).getQueryCache().build(Wt(this,L),this.options);if(t===Wt(this,_))return;const e=Wt(this,_);Dt(this,_,t),Dt(this,H,t.state),this.hasListeners()&&(null==e||e.removeObserver(this),t.addObserver(this))},ct=function(t){xe.batch((()=>{t.listeners&&this.listeners.forEach((t=>{t(Wt(this,G))})),Wt(this,L).getQueryCache().notify({query:Wt(this,_),type:"observerResultsUpdated"})}))},dt);function He(t,e){return function(t,e){return!1!==se(e.enabled,t)&&void 0===t.state.data&&!("error"===t.state.status&&!1===e.retryOnMount)}(t,e)||void 0!==t.state.data&&Ge(t,e,e.refetchOnMount)}function Ge(t,e,s){if(!1!==se(e.enabled,t)&&"static"!==ee(e.staleTime,t)){const i="function"==typeof s?s(t):s;return"always"===i||!1!==i&&Be(t,e)}return!1}function Ne(t,e,s,i){return(t!==e||!1===se(i.enabled,t))&&(!s.suspense||"error"!==t.state.status)&&Be(t,s)}function Be(t,e){return!1!==se(e.enabled,t)&&t.isStaleByTime(ee(e.staleTime,t))}var ze=class extends _e{constructor(t,e){super(t,e)}bindMethods(){super.bindMethods(),this.fetchNextPage=this.fetchNextPage.bind(this),this.fetchPreviousPage=this.fetchPreviousPage.bind(this)}setOptions(t){super.setOptions(qt(Ft({},t),{behavior:je()}))}getOptimisticResult(t){return t.behavior=je(),super.getOptimisticResult(t)}fetchNextPage(t){return this.fetch(qt(Ft({},t),{meta:{fetchMore:{direction:"forward"}}}))}fetchPreviousPage(t){return this.fetch(qt(Ft({},t),{meta:{fetchMore:{direction:"backward"}}}))}createResult(t,e){var s,i;const{state:n}=t,r=super.createResult(t,e),{isFetching:a,isRefetching:o,isError:h,isRefetchError:u}=r,l=null==(i=null==(s=n.fetchMeta)?void 0:s.fetchMore)?void 0:i.direction,c=h&&"forward"===l,d=a&&"forward"===l,f=h&&"backward"===l,p=a&&"backward"===l;return qt(Ft({},r),{fetchNextPage:this.fetchNextPage,fetchPreviousPage:this.fetchPreviousPage,hasNextPage:Ke(e,n.data),hasPreviousPage:Ie(e,n.data),isFetchNextPageError:c,isFetchingNextPage:d,isFetchPreviousPageError:f,isFetchingPreviousPage:p,isRefetchError:u&&!c&&!f,isRefetching:o&&!d&&!p})}},$e=(wt=class extends Vt{constructor(t,e){super(),At(this,bt),At(this,ft),At(this,pt),At(this,vt),At(this,yt),Dt(this,ft,t),this.setOptions(e),this.bindMethods(),jt(this,bt,gt).call(this)}bindMethods(){this.mutate=this.mutate.bind(this),this.reset=this.reset.bind(this)}setOptions(t){var e;const s=this.options;this.options=Wt(this,ft).defaultMutationOptions(t),ue(this.options,s)||Wt(this,ft).getMutationCache().notify({type:"observerOptionsUpdated",mutation:Wt(this,vt),observer:this}),(null==s?void 0:s.mutationKey)&&this.options.mutationKey&&ae(s.mutationKey)!==ae(this.options.mutationKey)?this.reset():"pending"===(null==(e=Wt(this,vt))?void 0:e.state.status)&&Wt(this,vt).setOptions(this.options)}onUnsubscribe(){var t;this.hasListeners()||null==(t=Wt(this,vt))||t.removeObserver(this)}onMutationUpdate(t){jt(this,bt,gt).call(this),jt(this,bt,mt).call(this,t)}getCurrentResult(){return Wt(this,pt)}reset(){var t;null==(t=Wt(this,vt))||t.removeObserver(this),Dt(this,vt,void 0),jt(this,bt,gt).call(this),jt(this,bt,mt).call(this)}mutate(t,e){var s;return Dt(this,yt,e),null==(s=Wt(this,vt))||s.removeObserver(this),Dt(this,vt,Wt(this,ft).getMutationCache().build(Wt(this,ft),this.options)),Wt(this,vt).addObserver(this),Wt(this,vt).execute(t)}},ft=new WeakMap,pt=new WeakMap,vt=new WeakMap,yt=new WeakMap,bt=new WeakSet,gt=function(){var t,e;const s=null!=(e=null==(t=Wt(this,vt))?void 0:t.state)?e:{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0};Dt(this,pt,qt(Ft({},s),{isPending:"pending"===s.status,isSuccess:"success"===s.status,isError:"error"===s.status,isIdle:"idle"===s.status,mutate:this.mutate,reset:this.reset}))},mt=function(t){xe.batch((()=>{var e,s,i,n,r,a,o,h;if(Wt(this,yt)&&this.hasListeners()){const u=Wt(this,pt).variables,l=Wt(this,pt).context;"success"===(null==t?void 0:t.type)?(null==(s=(e=Wt(this,yt)).onSuccess)||s.call(e,t.data,u,l),null==(n=(i=Wt(this,yt)).onSettled)||n.call(i,t.data,null,u,l)):"error"===(null==t?void 0:t.type)&&(null==(a=(r=Wt(this,yt)).onError)||a.call(r,t.error,u,l),null==(h=(o=Wt(this,yt)).onSettled)||h.call(o,void 0,t.error,u,l))}this.listeners.forEach((t=>{t(Wt(this,pt))}))}))},wt),Je=zt.createContext(void 0),Ve=t=>{const e=zt.useContext(Je);if(!e)throw new Error("No QueryClient set, use QueryClientProvider to set one");return e},Xe=({client:t,children:e})=>(zt.useEffect((()=>(t.mount(),()=>{t.unmount()})),[t]),Bt.jsx(Je.Provider,{value:t,children:e})),Ye=zt.createContext(!1);Ye.Provider;var Ze=zt.createContext(function(){let t=!1;return{clearReset:()=>{t=!1},reset:()=>{t=!0},isReset:()=>t}}()),ts=(t,e,s)=>e.fetchOptimistic(t).catch((()=>{s.clearReset()}));function es(t,e,s){var i,n,r,a,o;const h=zt.useContext(Ye),u=zt.useContext(Ze),l=Ve(),c=l.defaultQueryOptions(t);null==(n=null==(i=l.getDefaultOptions().queries)?void 0:i._experimental_beforeQuery)||n.call(i,c),c._optimisticResults=h?"isRestoring":"optimistic",(t=>{if(t.suspense){const e=t=>"static"===t?t:Math.max(null!=t?t:1e3,1e3),s=t.staleTime;t.staleTime="function"==typeof s?(...t)=>e(s(...t)):e(s),"number"==typeof t.gcTime&&(t.gcTime=Math.max(t.gcTime,1e3))}})(c),((t,e)=>{(t.suspense||t.throwOnError||t.experimental_prefetchInRender)&&(e.isReset()||(t.retryOnMount=!1))})(c,u),(t=>{zt.useEffect((()=>{t.clearReset()}),[t])})(u);const d=!l.getQueryCache().get(c.queryHash),[f]=zt.useState((()=>new e(l,c))),p=f.getOptimisticResult(c),v=!h&&!1!==t.subscribed;if(zt.useSyncExternalStore(zt.useCallback((t=>{const e=v?f.subscribe(xe.batchCalls(t)):Yt;return f.updateResult(),e}),[f,v]),(()=>f.getCurrentResult()),(()=>f.getCurrentResult())),zt.useEffect((()=>{f.setOptions(c)}),[c,f]),((t,e)=>(null==t?void 0:t.suspense)&&e.isPending)(c,p))throw ts(c,f,u);if((({result:t,errorResetBoundary:e,throwOnError:s,query:i,suspense:n})=>t.isError&&!e.isReset()&&!t.isFetching&&i&&(n&&void 0===t.data||ge(s,[t.error,i])))({result:p,errorResetBoundary:u,throwOnError:c.throwOnError,query:l.getQueryCache().get(c.queryHash),suspense:c.suspense}))throw p.error;if(null==(a=null==(r=l.getDefaultOptions().queries)?void 0:r._experimental_afterQuery)||a.call(r,c,p),c.experimental_prefetchInRender&&!Xt&&((t,e)=>t.isLoading&&t.isFetching&&!e)(p,h)){const t=d?ts(c,f,u):null==(o=l.getQueryCache().get(c.queryHash))?void 0:o.promise;null==t||t.catch(Yt).finally((()=>{f.updateResult()}))}return c.notifyOnChangeProps?p:f.trackResult(p)}function ss(t,e){return es(t,_e)}function is(t,e){const s=Ve(),[i]=zt.useState((()=>new $e(s,t)));zt.useEffect((()=>{i.setOptions(t)}),[i,t]);const n=zt.useSyncExternalStore(zt.useCallback((t=>i.subscribe(xe.batchCalls(t))),[i]),(()=>i.getCurrentResult()),(()=>i.getCurrentResult())),r=zt.useCallback(((t,e)=>{i.mutate(t,e).catch(Yt)}),[i]);if(n.error&&ge(i.options.throwOnError,[n.error]))throw n.error;return qt(Ft({},n),{mutate:r,mutateAsync:n.mutate})}function ns(t,e){return es(t,ze)}export{Le as Q,$t as R,Jt as a,ns as b,Ve as c,is as d,Xe as e,Bt as j,zt as r,ss as u};
