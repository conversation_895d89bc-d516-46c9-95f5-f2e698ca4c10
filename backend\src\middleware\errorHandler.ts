import { Request, Response, NextFunction } from 'express';
import { AppError } from '../types';
import { logger } from '../utils/logger';

// 全局错误处理中间件
export const errorHandler = (
  error: Error,
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  let err = error;

  // 记录错误日志
  logger.error('Error occurred:', {
    message: err.message,
    stack: err.stack,
    url: req.url,
    method: req.method,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
  });

  // 如果不是AppError，转换为AppError
  if (!(err instanceof AppError)) {
    err = new AppError('服务器内部错误', 500);
  }

  const appError = err as AppError;

  // 开发环境返回详细错误信息
  if (process.env.NODE_ENV === 'development') {
    res.status(appError.statusCode).json({
      success: false,
      error: appError.message,
      stack: appError.stack,
      details: error.message !== appError.message ? error.message : undefined,
    });
  } else {
    // 生产环境只返回安全的错误信息
    if (appError.isOperational) {
      res.status(appError.statusCode).json({
        success: false,
        error: appError.message,
      });
    } else {
      res.status(500).json({
        success: false,
        error: '服务器内部错误',
      });
    }
  }
};

// 404错误处理
export const notFoundHandler = (req: Request, res: Response): void => {
  res.status(404).json({
    success: false,
    error: `路由 ${req.originalUrl} 不存在`,
  });
};

// 异步错误捕获包装器
export const asyncHandler = (fn: Function) => {
  return (req: Request, res: Response, next: NextFunction) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};
