import nodemailer from "nodemailer";
import { logger } from "../utils/logger";
import { Email } from "../types";

class EmailService {
  private transporter: nodemailer.Transporter;

  constructor() {
    this.transporter = nodemailer.createTransport({
      host: process.env.SMTP_HOST,
      port: parseInt(process.env.SMTP_PORT || "587"),
      secure: false, // true for 465, false for other ports
      auth: {
        user: process.env.SMTP_USER,
        pass: process.env.SMTP_PASS,
      },
    });
  }

  async sendEmail(emailData: {
    from: string;
    to: string[];
    cc?: string[];
    bcc?: string[];
    subject: string;
    html: string;
    attachments?: any[];
  }) {
    try {
      const result = await this.transporter.sendMail({
        from: emailData.from,
        to: emailData.to.join(","),
        cc: emailData.cc?.join(","),
        bcc: emailData.bcc?.join(","),
        subject: emailData.subject,
        html: emailData.html,
        attachments: emailData.attachments,
      });

      logger.info("Em<PERSON> sent successfully", { messageId: result.messageId });
      return result;
    } catch (error) {
      logger.error("Failed to send email", { error });
      throw new Error(`邮件发送失败: ${error}`);
    }
  }
}

export default new EmailService();
