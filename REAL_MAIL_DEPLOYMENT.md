# 📧 真实邮件系统部署指南

## 🎯 系统架构

本邮件系统采用经典的 **Postfix + Dovecot + MySQL** 架构，实现真正的邮件收发功能：

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端应用      │    │   后端API       │    │   邮件服务器    │
│   React + TS    │◄──►│   Node.js       │◄──►│   Postfix       │
│   Ant Design    │    │   Express       │    │   Dovecot       │
└─────────────────┘    └─────────────────┘    │   MySQL         │
                                              └─────────────────┘
```

### 核心组件

- **Postfix**: SMTP 邮件发送服务器
- **Dovecot**: IMAP/POP3 邮件接收服务器  
- **MySQL**: 用户数据和邮件元数据存储
- **Node.js API**: 邮件系统业务逻辑
- **React 前端**: 用户界面

## 🗄️ 数据库设计

### 核心表结构

1. **用户管理**
   - `users` - 用户基础信息
   - `mail_domains` - 邮件域名管理
   - `mail_accounts` - 邮箱账户 (Postfix/Dovecot使用)

2. **邮件存储**
   - `emails` - 邮件主表
   - `user_emails` - 用户邮件关联
   - `email_attachments` - 附件管理

3. **文件夹管理**
   - `mail_folders` - 邮件文件夹
   - `contacts` - 联系人管理

4. **系统日志**
   - `mail_send_logs` - 发送日志
   - `system_logs` - 操作日志

## 🚀 快速部署

### 1. 环境要求

- Docker 20.10+
- Docker Compose 2.0+
- 至少 4GB RAM
- 20GB 磁盘空间

### 2. 一键部署

```bash
# 克隆项目
git clone <repository-url>
cd email-system

# 启动基础邮件系统
.\scripts\deploy-mail-system.ps1 -Action start -Profile basic

# 启动完整系统（包含Web邮件客户端）
.\scripts\deploy-mail-system.ps1 -Action start -Profile full -Build

# 测试模式（使用MailHog）
.\scripts\deploy-mail-system.ps1 -Action start -Profile testing
```

### 3. 验证部署

```bash
# 检查服务状态
.\scripts\deploy-mail-system.ps1 -Action status

# 查看日志
.\scripts\deploy-mail-system.ps1 -Action logs
```

## 📧 邮件服务配置

### Postfix 配置要点

1. **虚拟域名支持**: 通过MySQL查询支持多域名
2. **SASL认证**: 集成Dovecot认证
3. **TLS加密**: 支持STARTTLS和SSL/TLS
4. **反垃圾邮件**: 集成RBL和基础过滤

### Dovecot 配置要点

1. **SQL认证**: 从MySQL读取用户信息
2. **Maildir格式**: 使用Maildir存储邮件
3. **配额管理**: 支持用户邮箱配额
4. **Sieve过滤**: 支持邮件过滤规则

### MySQL 配置要点

1. **用户权限分离**: Postfix、Dovecot、应用使用不同用户
2. **索引优化**: 针对邮件查询优化索引
3. **存储过程**: 提供邮箱管理存储过程
4. **触发器**: 自动维护统计信息

## 🔧 API集成

### 邮件发送

```typescript
// 发送邮件
POST /api/emails
{
  "to": [{"address": "<EMAIL>", "name": "用户"}],
  "subject": "测试邮件",
  "textContent": "邮件内容",
  "htmlContent": "<p>邮件内容</p>",
  "priority": "normal"
}
```

### 邮件接收

```typescript
// 获取邮件列表
GET /api/emails?folderId=inbox&page=1&limit=20

// 获取邮件详情
GET /api/emails/:id

// 同步IMAP邮件
POST /api/emails/sync
```

### 文件夹管理

```typescript
// 获取文件夹列表
GET /api/emails/folders

// 邮件搜索
POST /api/emails/search
{
  "query": "搜索关键词",
  "folderId": "inbox"
}
```

## 🔐 安全配置

### 1. SSL/TLS 证书

```bash
# 生成自签名证书（开发环境）
openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
  -keyout ssl/mail.key \
  -out ssl/mail.crt \
  -subj "/C=CN/ST=Beijing/L=Beijing/O=Email System/CN=mail.yourdomain.com"

# 生产环境建议使用 Let's Encrypt
certbot certonly --standalone -d mail.yourdomain.com
```

### 2. 防火墙配置

```bash
# 开放邮件服务端口
ufw allow 25/tcp    # SMTP
ufw allow 587/tcp   # Submission
ufw allow 465/tcp   # SMTPS
ufw allow 143/tcp   # IMAP
ufw allow 993/tcp   # IMAPS
ufw allow 110/tcp   # POP3
ufw allow 995/tcp   # POP3S
```

### 3. DNS 配置

```dns
# MX 记录
yourdomain.com.     IN  MX  10  mail.yourdomain.com.

# A 记录
mail.yourdomain.com. IN  A   YOUR_SERVER_IP

# SPF 记录
yourdomain.com.     IN  TXT "v=spf1 mx ~all"

# DKIM 记录（可选）
default._domainkey.yourdomain.com. IN TXT "v=DKIM1; k=rsa; p=YOUR_PUBLIC_KEY"

# DMARC 记录（可选）
_dmarc.yourdomain.com. IN TXT "v=DMARC1; p=quarantine; rua=mailto:<EMAIL>"
```

## 👥 用户管理

### 创建邮箱用户

```bash
# 使用脚本创建
.\scripts\deploy-mail-system.ps1 -Action user

# 或直接使用SQL
docker-compose -f docker-compose.mail.yml exec mysql mysql -u root -p
```

```sql
-- 创建用户邮箱
CALL CreateUserMailbox(
  'username',           -- 用户名
  '<EMAIL>', -- 邮箱地址
  'password123',        -- 密码
  '用户显示名',          -- 显示名称
  'yourdomain.com',     -- 域名
  **********           -- 配额(1GB)
);
```

### 管理邮箱配额

```sql
-- 查看配额使用情况
SELECT * FROM v_user_mailbox_stats;

-- 更新用户配额
UPDATE mail_accounts 
SET quota = **********  -- 2GB
WHERE email = '<EMAIL>';
```

## 📊 监控和维护

### 1. 服务监控

```bash
# 检查服务状态
docker-compose -f docker-compose.mail.yml ps

# 查看实时日志
docker-compose -f docker-compose.mail.yml logs -f postfix
docker-compose -f docker-compose.mail.yml logs -f dovecot
```

### 2. 邮件队列管理

```bash
# 查看邮件队列
docker-compose -f docker-compose.mail.yml exec postfix postqueue -p

# 刷新队列
docker-compose -f docker-compose.mail.yml exec postfix postfix flush
```

### 3. 数据备份

```bash
# 备份数据库
docker-compose -f docker-compose.mail.yml exec mysql \
  mysqldump -u root -p email_system > backup_$(date +%Y%m%d).sql

# 备份邮件数据
tar -czf mail_backup_$(date +%Y%m%d).tar.gz mail-data/
```

## 🧪 测试验证

### 1. SMTP 测试

```bash
# 使用 telnet 测试 SMTP
telnet localhost 25
EHLO test.com
MAIL FROM: <EMAIL>
RCPT TO: <EMAIL>
DATA
Subject: Test Email
This is a test email.
.
QUIT
```

### 2. IMAP 测试

```bash
# 使用 openssl 测试 IMAPS
openssl s_client -connect localhost:993
a1 LOGIN <EMAIL> password123
a2 LIST "" "*"
a3 SELECT INBOX
a4 FETCH 1 BODY[]
a5 LOGOUT
```

### 3. API 测试

```bash
# 测试登录
curl -X POST http://localhost:5000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"admin123"}'

# 测试发送邮件
curl -X POST http://localhost:5000/api/emails \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "to":[{"address":"<EMAIL>"}],
    "subject":"API测试邮件",
    "textContent":"这是通过API发送的测试邮件"
  }'
```

## 🔧 故障排除

### 常见问题

1. **邮件发送失败**
   - 检查 Postfix 日志
   - 验证 DNS 配置
   - 确认端口开放

2. **邮件接收失败**
   - 检查 Dovecot 配置
   - 验证用户认证
   - 检查磁盘空间

3. **数据库连接失败**
   - 检查 MySQL 服务状态
   - 验证用户权限
   - 检查网络连接

### 日志位置

- Postfix: `/var/log/postfix/`
- Dovecot: `/var/log/dovecot/`
- MySQL: `/var/log/mysql/`
- 应用日志: `./logs/`

## 🎉 部署完成

恭喜！你已经成功部署了一个功能完整的真实邮件系统。

### 访问地址

- **前端应用**: http://localhost
- **后端API**: http://localhost:5000
- **Roundcube**: http://localhost:8080 (如果启用)
- **MailHog**: http://localhost:8025 (测试模式)

### 默认账户

- **管理员**: <EMAIL> / admin123
- **测试用户1**: <EMAIL> / user123
- **测试用户2**: <EMAIL> / user123

现在你可以使用这个系统进行真实的邮件收发了！📧✨
