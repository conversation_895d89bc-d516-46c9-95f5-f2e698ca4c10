import { Request, Response } from 'express';
import { memoryDB } from '../database/memoryStore';
import { AppError, ApiResponse, Folder } from '../types';
import { asyncHandler } from '../middleware/errorHandler';

// 获取用户的文件夹列表
export const getFolders = asyncHandler(async (req: Request, res: Response) => {
  if (!req.user) {
    throw new AppError('用户未认证', 401);
  }

  const folders = await memoryDB.getFoldersByUserId(req.user.id);

  // 更新文件夹的邮件统计
  const foldersWithStats = await Promise.all(
    folders.map(async (folder) => {
      const emails = await memoryDB.getEmailsByUserId(req.user!.id, folder.id);
      const emailCount = emails.length;
      const unreadCount = emails.filter(email => !email.isRead).length;

      return {
        ...folder,
        emailCount,
        unreadCount,
      };
    })
  );

  const response: ApiResponse<Folder[]> = {
    success: true,
    data: foldersWithStats,
    message: '获取文件夹列表成功',
  };

  res.json(response);
});

// 获取单个文件夹信息
export const getFolderById = asyncHandler(async (req: Request, res: Response) => {
  if (!req.user) {
    throw new AppError('用户未认证', 401);
  }

  const { id } = req.params;
  const folder = await memoryDB.getFolderById(id);

  if (!folder) {
    throw new AppError('文件夹不存在', 404);
  }

  // 检查文件夹是否属于当前用户
  if (folder.userId !== req.user.id) {
    throw new AppError('无权访问此文件夹', 403);
  }

  // 更新邮件统计
  const emails = await memoryDB.getEmailsByUserId(req.user.id, folder.id);
  const folderWithStats = {
    ...folder,
    emailCount: emails.length,
    unreadCount: emails.filter(email => !email.isRead).length,
  };

  const response: ApiResponse<Folder> = {
    success: true,
    data: folderWithStats,
    message: '获取文件夹信息成功',
  };

  res.json(response);
});
