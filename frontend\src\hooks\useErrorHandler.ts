import { useCallback } from 'react';
import { message, notification } from 'antd';
import { ErrorType, ErrorCode } from '../components/ErrorBoundary/ErrorMessage';

interface ErrorInfo {
  message: string;
  errorType?: ErrorType;
  errorCode?: ErrorCode;
  details?: any;
  timestamp?: string;
  requestId?: string;
}

interface UseErrorHandlerOptions {
  showNotification?: boolean;
  showMessage?: boolean;
  autoClose?: boolean;
  duration?: number;
  onError?: (error: ErrorInfo) => void;
}

export const useErrorHandler = (options: UseErrorHandlerOptions = {}) => {
  const {
    showNotification = true,
    showMessage = false,
    autoClose = true,
    duration = 4.5,
    onError,
  } = options;

  const handleError = useCallback((error: any, customMessage?: string) => {
    // 解析错误信息
    const errorInfo: ErrorInfo = parseError(error, customMessage);
    
    // 调用自定义错误处理函数
    if (onError) {
      onError(errorInfo);
    }

    // 显示错误通知
    if (showNotification) {
      showErrorNotification(errorInfo, { autoClose, duration });
    }

    // 显示错误消息
    if (showMessage) {
      showErrorMessage(errorInfo);
    }

    // 记录错误到控制台（开发环境）
    if (process.env.NODE_ENV === 'development') {
      console.error('Error handled:', errorInfo, error);
    }

    return errorInfo;
  }, [showNotification, showMessage, autoClose, duration, onError]);

  const handleAsyncError = useCallback(async (asyncFn: () => Promise<any>, customMessage?: string) => {
    try {
      return await asyncFn();
    } catch (error) {
      handleError(error, customMessage);
      throw error; // 重新抛出错误，让调用者决定如何处理
    }
  }, [handleError]);

  const clearErrors = useCallback(() => {
    message.destroy();
    notification.destroy();
  }, []);

  return {
    handleError,
    handleAsyncError,
    clearErrors,
  };
};

// 解析错误信息
const parseError = (error: any, customMessage?: string): ErrorInfo => {
  // 如果是自定义错误信息
  if (customMessage) {
    return {
      message: customMessage,
      details: error,
      timestamp: new Date().toISOString(),
    };
  }

  // 如果是API响应错误
  if (error?.response?.data) {
    const responseData = error.response.data;
    return {
      message: responseData.error || responseData.message || '请求失败',
      errorType: responseData.errorType,
      errorCode: responseData.errorCode,
      details: responseData.details,
      timestamp: responseData.timestamp,
      requestId: responseData.requestId,
    };
  }

  // 如果是网络错误
  if (error?.code === 'ERR_NETWORK' || error?.message?.includes('Network Error')) {
    return {
      message: '网络连接失败，请检查您的网络连接',
      errorType: ErrorType.NETWORK_ERROR,
      timestamp: new Date().toISOString(),
    };
  }

  // 如果是超时错误
  if (error?.code === 'ECONNABORTED' || error?.message?.includes('timeout')) {
    return {
      message: '请求超时，请稍后重试',
      errorType: ErrorType.NETWORK_ERROR,
      timestamp: new Date().toISOString(),
    };
  }

  // 默认错误处理
  return {
    message: error?.message || error?.toString() || '发生了未知错误',
    timestamp: new Date().toISOString(),
  };
};

// 显示错误通知
const showErrorNotification = (errorInfo: ErrorInfo, options: { autoClose: boolean; duration: number }) => {
  const { message, errorType, errorCode, requestId } = errorInfo;
  
  // 根据错误类型确定通知类型
  const notificationType = getNotificationType(errorType);
  
  notification[notificationType]({
    message: getErrorTitle(errorType, errorCode),
    description: (
      <div>
        <p>{message}</p>
        {requestId && (
          <p style={{ fontSize: '12px', color: '#666', marginTop: '8px' }}>
            错误ID: {requestId}
          </p>
        )}
      </div>
    ),
    duration: options.autoClose ? options.duration : 0,
    placement: 'topRight',
  });
};

// 显示错误消息
const showErrorMessage = (errorInfo: ErrorInfo) => {
  message.error(errorInfo.message);
};

// 获取通知类型
const getNotificationType = (errorType?: ErrorType): 'error' | 'warning' | 'info' => {
  switch (errorType) {
    case ErrorType.VALIDATION:
    case ErrorType.NOT_FOUND:
      return 'warning';
    case ErrorType.AUTHENTICATION:
    case ErrorType.AUTHORIZATION:
      return 'info';
    default:
      return 'error';
  }
};

// 获取错误标题
const getErrorTitle = (errorType?: ErrorType, errorCode?: ErrorCode): string => {
  if (errorCode) {
    switch (errorCode) {
      case ErrorCode.INVALID_CREDENTIALS:
        return '登录失败';
      case ErrorCode.TOKEN_EXPIRED:
        return '登录已过期';
      case ErrorCode.INSUFFICIENT_PERMISSIONS:
        return '权限不足';
      case ErrorCode.RESOURCE_NOT_FOUND:
        return '资源不存在';
      case ErrorCode.NETWORK_ERROR:
        return '网络错误';
      default:
        break;
    }
  }

  switch (errorType) {
    case ErrorType.VALIDATION:
      return '输入验证失败';
    case ErrorType.AUTHENTICATION:
      return '身份验证失败';
    case ErrorType.AUTHORIZATION:
      return '权限验证失败';
    case ErrorType.NOT_FOUND:
      return '资源不存在';
    case ErrorType.NETWORK_ERROR:
      return '网络连接错误';
    case ErrorType.SERVER_ERROR:
      return '服务器错误';
    default:
      return '操作失败';
  }
};

// 预定义的错误处理器
export const useApiErrorHandler = () => {
  return useErrorHandler({
    showNotification: true,
    showMessage: false,
    autoClose: true,
    duration: 5,
  });
};

export const useFormErrorHandler = () => {
  return useErrorHandler({
    showNotification: false,
    showMessage: true,
    autoClose: true,
    duration: 3,
  });
};

export const useNetworkErrorHandler = () => {
  return useErrorHandler({
    showNotification: true,
    showMessage: false,
    autoClose: false, // 网络错误不自动关闭
  });
};
