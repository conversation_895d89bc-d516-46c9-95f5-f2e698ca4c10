/**
 * 虚拟化邮件列表组件
 * 使用虚拟滚动优化大量邮件的渲染性能
 */

import React, { useEffect, useMemo, useCallback, useState } from 'react';
import { Card, Checkbox, Button, Tag, Space, Spin, Empty } from 'antd';
import { 
  StarOutlined, 
  StarFilled, 
  DeleteOutlined,
  ReloadOutlined,
  EyeOutlined,
  EyeInvisibleOutlined
} from '@ant-design/icons';
import VirtualList from '../VirtualScroll/VirtualList';
import { useInfiniteEmails, useUpdateEmail, useDeleteEmail, usePrefetchEmail } from '../../hooks/useEmails';
import { useUIStore } from '../../store/uiStore';
import { showErrorMessage } from '../../utils/errorHandler';
import { performanceMonitor, debounce } from '../../utils/performance';
import { VIRTUAL_SCROLL_CONFIG, DEBOUNCE_CONFIG } from '../../config/performance';
import type { Email } from '../../types';

interface EmailListVirtualizedProps {
  folderId?: string;
  containerHeight?: number;
}

// 邮件项组件 - 使用React.memo优化
const EmailItem = React.memo<{
  email: Email;
  isSelected: boolean;
  onSelect: (emailId: string, checked: boolean) => void;
  onClick: (email: Email) => void;
  onHover: (emailId: string) => void;
  onToggleStar: (email: Email, e: React.MouseEvent) => void;
  onToggleRead: (email: Email, e: React.MouseEvent) => void;
  onDelete: (emailId: string, e: React.MouseEvent) => void;
}>(({
  email,
  isSelected,
  onSelect,
  onClick,
  onHover,
  onToggleStar,
  onToggleRead,
  onDelete,
}) => {
  return (
    <div
      className={`email-item ${!email.isRead ? 'unread' : ''} ${isSelected ? 'selected' : ''}`}
      onClick={() => onClick(email)}
      onMouseEnter={() => onHover(email.id)}
      style={{
        cursor: 'pointer',
        padding: '12px 16px',
        borderBottom: '1px solid #f0f0f0',
        backgroundColor: isSelected ? '#e6f7ff' : 'transparent',
        display: 'flex',
        alignItems: 'center',
        height: VIRTUAL_SCROLL_CONFIG.ITEM_HEIGHT,
      }}
    >
      {/* 选择框 */}
      <Checkbox
        checked={isSelected}
        onChange={(e) => onSelect(email.id, e.target.checked)}
        onClick={(e) => e.stopPropagation()}
        style={{ marginRight: 12 }}
      />

      {/* 标星按钮 */}
      <Button
        type="text"
        size="small"
        icon={email.isStarred ? <StarFilled style={{ color: '#faad14' }} /> : <StarOutlined />}
        onClick={(e) => onToggleStar(email, e)}
        style={{ marginRight: 8 }}
      />

      {/* 邮件内容 */}
      <div style={{ flex: 1, minWidth: 0 }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <div style={{ display: 'flex', alignItems: 'center', minWidth: 0, flex: 1 }}>
            <span
              style={{
                fontWeight: email.isRead ? 'normal' : 'bold',
                marginRight: 8,
                minWidth: 120,
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap',
              }}
            >
              {email.senderName || email.senderEmail}
            </span>
            <span
              style={{
                fontWeight: email.isRead ? 'normal' : 'bold',
                flex: 1,
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap',
              }}
            >
              {email.subject}
            </span>
          </div>
          
          <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
            {/* 标签 */}
            {!email.isRead && <Tag color="blue">未读</Tag>}
            {email.attachments && email.attachments.length > 0 && <Tag color="orange">附件</Tag>}
            
            {/* 时间 */}
            <span style={{ color: '#666', fontSize: '12px', minWidth: 80, textAlign: 'right' }}>
              {new Date(email.createdAt).toLocaleDateString()}
            </span>

            {/* 操作按钮 */}
            <Space size="small">
              <Button
                type="text"
                size="small"
                icon={email.isRead ? <EyeInvisibleOutlined /> : <EyeOutlined />}
                onClick={(e) => onToggleRead(email, e)}
                title={email.isRead ? '标记为未读' : '标记为已读'}
              />
              <Button
                type="text"
                size="small"
                icon={<DeleteOutlined />}
                onClick={(e) => onDelete(email.id, e)}
                title="删除"
                danger
              />
            </Space>
          </div>
        </div>
        
        {/* 邮件预览 */}
        <div
          style={{
            color: '#666',
            fontSize: '12px',
            marginTop: 4,
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            whiteSpace: 'nowrap',
          }}
        >
          {email.contentText?.substring(0, 100)}...
        </div>
      </div>
    </div>
  );
});

EmailItem.displayName = 'EmailItem';

const EmailListVirtualized: React.FC<EmailListVirtualizedProps> = ({ 
  folderId, 
  containerHeight = 600 
}) => {
  const [isInitialized, setIsInitialized] = useState(false);
  
  const {
    currentFolder,
    selectedEmails,
    selectEmail,
    unselectEmail,
    clearSelection,
    setCurrentEmail,
  } = useUIStore();

  // 使用无限滚动查询
  const {
    data,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    isLoading,
    error,
    refetch,
  } = useInfiniteEmails({
    folderId: folderId || currentFolder?.id,
  });

  // 邮件操作mutations
  const updateEmailMutation = useUpdateEmail();
  const deleteEmailMutation = useDeleteEmail();
  const prefetchEmail = usePrefetchEmail();

  // 扁平化邮件数据
  const emails = useMemo(() => {
    return data?.pages.flatMap(page => page.data) || [];
  }, [data]);

  // 防抖的预取函数
  const debouncedPrefetch = useMemo(
    () => debounce((emailId: string) => {
      prefetchEmail(emailId);
    }, DEBOUNCE_CONFIG.SEARCH),
    [prefetchEmail]
  );

  // 处理邮件选择
  const handleEmailSelect = useCallback((emailId: string, checked: boolean) => {
    if (checked) {
      selectEmail(emailId);
    } else {
      unselectEmail(emailId);
    }
  }, [selectEmail, unselectEmail]);

  // 处理全选
  const handleSelectAll = useCallback((checked: boolean) => {
    if (checked) {
      emails.forEach(email => selectEmail(email.id));
    } else {
      clearSelection();
    }
  }, [emails, selectEmail, clearSelection]);

  // 处理邮件点击
  const handleEmailClick = useCallback((email: Email) => {
    performanceMonitor.startMeasure('email-click');
    
    setCurrentEmail(email);
    
    // 如果邮件未读，标记为已读
    if (!email.isRead) {
      updateEmailMutation.mutate({
        id: email.id,
        updates: { isRead: true },
      });
    }
    
    performanceMonitor.endMeasure('email-click');
  }, [setCurrentEmail, updateEmailMutation]);

  // 处理邮件悬停预取
  const handleEmailHover = useCallback((emailId: string) => {
    debouncedPrefetch(emailId);
  }, [debouncedPrefetch]);

  // 处理标星
  const handleToggleStar = useCallback((email: Email, e: React.MouseEvent) => {
    e.stopPropagation();
    
    updateEmailMutation.mutate({
      id: email.id,
      updates: { isStarred: !email.isStarred },
    });
  }, [updateEmailMutation]);

  // 处理标记已读/未读
  const handleToggleRead = useCallback((email: Email, e: React.MouseEvent) => {
    e.stopPropagation();
    
    updateEmailMutation.mutate({
      id: email.id,
      updates: { isRead: !email.isRead },
    });
  }, [updateEmailMutation]);

  // 处理删除
  const handleDelete = useCallback((emailId: string, e: React.MouseEvent) => {
    e.stopPropagation();
    
    deleteEmailMutation.mutate(emailId);
  }, [deleteEmailMutation]);

  // 批量操作
  const handleBatchMarkAsRead = useCallback(() => {
    if (selectedEmails.length === 0) return;

    selectedEmails.forEach(emailId => {
      updateEmailMutation.mutate({
        id: emailId,
        updates: { isRead: true },
      });
    });

    clearSelection();
  }, [selectedEmails, updateEmailMutation, clearSelection]);

  const handleBatchDelete = useCallback(() => {
    if (selectedEmails.length === 0) return;

    selectedEmails.forEach(emailId => {
      deleteEmailMutation.mutate(emailId);
    });

    clearSelection();
  }, [selectedEmails, deleteEmailMutation, clearSelection]);

  // 滚动到底部时加载更多
  const handleScroll = useCallback((scrollTop: number) => {
    const scrollThreshold = containerHeight * 0.8;
    const maxScroll = emails.length * VIRTUAL_SCROLL_CONFIG.ITEM_HEIGHT - containerHeight;
    
    if (scrollTop >= maxScroll - scrollThreshold && hasNextPage && !isFetchingNextPage) {
      fetchNextPage();
    }
  }, [containerHeight, emails.length, hasNextPage, isFetchingNextPage, fetchNextPage]);

  // 渲染邮件项
  const renderEmailItem = useCallback((email: Email, index: number) => {
    return (
      <EmailItem
        key={email.id}
        email={email}
        isSelected={selectedEmails.includes(email.id)}
        onSelect={handleEmailSelect}
        onClick={handleEmailClick}
        onHover={handleEmailHover}
        onToggleStar={handleToggleStar}
        onToggleRead={handleToggleRead}
        onDelete={handleDelete}
      />
    );
  }, [
    selectedEmails,
    handleEmailSelect,
    handleEmailClick,
    handleEmailHover,
    handleToggleStar,
    handleToggleRead,
    handleDelete,
  ]);

  // 错误处理
  useEffect(() => {
    if (error) {
      showErrorMessage(error, '获取邮件列表失败');
    }
  }, [error]);

  // 性能监控
  useEffect(() => {
    if (!isInitialized && emails.length > 0) {
      performanceMonitor.endMeasure('email-list-load');
      setIsInitialized(true);
    }
  }, [emails.length, isInitialized]);

  useEffect(() => {
    performanceMonitor.startMeasure('email-list-load');
  }, []);

  if (isLoading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
        <div style={{ marginTop: 16 }}>加载邮件中...</div>
      </div>
    );
  }

  if (emails.length === 0) {
    return (
      <Empty
        description="暂无邮件"
        image={Empty.PRESENTED_IMAGE_SIMPLE}
        style={{ padding: '50px' }}
      >
        <Button type="primary" icon={<ReloadOutlined />} onClick={() => refetch()}>
          刷新
        </Button>
      </Empty>
    );
  }

  return (
    <Card
      title={
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: 16 }}>
            <Checkbox
              indeterminate={selectedEmails.length > 0 && selectedEmails.length < emails.length}
              checked={selectedEmails.length === emails.length}
              onChange={(e) => handleSelectAll(e.target.checked)}
            >
              全选
            </Checkbox>
            <span>共 {emails.length} 封邮件</span>
          </div>
          
          {selectedEmails.length > 0 && (
            <Space>
              <Button size="small" onClick={handleBatchMarkAsRead}>
                标记已读 ({selectedEmails.length})
              </Button>
              <Button size="small" danger onClick={handleBatchDelete}>
                删除 ({selectedEmails.length})
              </Button>
            </Space>
          )}
        </div>
      }
      extra={
        <Button icon={<ReloadOutlined />} onClick={() => refetch()}>
          刷新
        </Button>
      }
      bodyStyle={{ padding: 0 }}
    >
      <VirtualList
        items={emails}
        itemHeight={VIRTUAL_SCROLL_CONFIG.ITEM_HEIGHT}
        containerHeight={containerHeight}
        renderItem={renderEmailItem}
        onScroll={handleScroll}
        overscan={VIRTUAL_SCROLL_CONFIG.OVERSCAN}
      />
      
      {/* 加载更多指示器 */}
      {isFetchingNextPage && (
        <div style={{ textAlign: 'center', padding: '16px' }}>
          <Spin size="small" />
          <span style={{ marginLeft: 8 }}>加载更多...</span>
        </div>
      )}
    </Card>
  );
};

export default React.memo(EmailListVirtualized);
