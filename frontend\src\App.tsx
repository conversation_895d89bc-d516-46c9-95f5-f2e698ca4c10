import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import { ConfigProvider } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import 'antd/dist/reset.css';
import './App.css';

// 导入新的QueryClient
import { queryClient } from './lib/queryClient';

// 组件导入
import LoginForm from './components/Auth/LoginForm';
import RegisterForm from './components/Auth/RegisterForm';
import MainLayout from './components/Layout/MainLayout';
import EmailList from './components/Email/EmailList';
import EmailDetail from './components/Email/EmailDetail';
import EmailComposer from './components/Email/EmailComposer';
import Dashboard from './components/Dashboard/Dashboard';
import ProtectedRoute from './components/ProtectedRoute';
import ErrorBoundary from './components/ErrorBoundary/ErrorBoundary';
import NetworkErrorHandler from './components/ErrorBoundary/NetworkErrorHandler';

// QueryClient 现在从 lib/queryClient 导入

function App() {
  return (
    <ErrorBoundary>
      <NetworkErrorHandler>
        <QueryClientProvider client={queryClient}>
          <ConfigProvider locale={zhCN}>
            <Router>
              <Routes>
            {/* 公开路由 */}
            <Route path="/login" element={<LoginForm />} />
            <Route path="/register" element={<RegisterForm />} />

            {/* 受保护的路由 */}
            <Route
              path="/"
              element={
                <ProtectedRoute>
                  <MainLayout />
                </ProtectedRoute>
              }
            >
              {/* 默认显示仪表板 */}
              <Route index element={<Dashboard />} />

              {/* 邮件相关路由 */}
              <Route path="folder/:folderId" element={<EmailList />} />
              <Route path="email/:emailId" element={<EmailDetail />} />
              <Route path="compose" element={<EmailComposer />} />

              {/* 其他功能路由 */}
              <Route path="search" element={<EmailList />} />
              <Route path="contacts" element={<div>联系人功能待实现</div>} />
              <Route path="settings" element={<div>设置功能待实现</div>} />
              <Route path="profile" element={<div>个人资料功能待实现</div>} />
            </Route>

              {/* 404 页面 */}
              <Route path="*" element={<Navigate to="/" replace />} />
            </Routes>
          </Router>
        </ConfigProvider>
        {/* React Query 开发工具 */}
        <ReactQueryDevtools initialIsOpen={false} />
      </QueryClientProvider>
    </NetworkErrorHandler>
  </ErrorBoundary>
  );
}

export default App;
