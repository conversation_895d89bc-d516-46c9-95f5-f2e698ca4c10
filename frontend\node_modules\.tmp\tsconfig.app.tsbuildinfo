{"root": ["../../src/app.tsx", "../../src/main.tsx", "../../src/vite-env.d.ts", "../../src/components/protectedroute.tsx", "../../src/components/auth/loginform.tsx", "../../src/components/auth/registerform.tsx", "../../src/components/dashboard/dashboard.tsx", "../../src/components/email/emailcomposer.tsx", "../../src/components/email/emaildetail.tsx", "../../src/components/email/emaillist.tsx", "../../src/components/email/emaillistoptimized.tsx", "../../src/components/email/emaillistvirtualized.tsx", "../../src/components/errorboundary/errorboundary.tsx", "../../src/components/errorboundary/errormessage.tsx", "../../src/components/errorboundary/networkerrorhandler.tsx", "../../src/components/layout/mainlayout.tsx", "../../src/components/virtualscroll/virtuallist.tsx", "../../src/config/performance.ts", "../../src/hooks/useauth.ts", "../../src/hooks/useemails.ts", "../../src/hooks/useerrorhandler.ts", "../../src/hooks/usefolders.ts", "../../src/hooks/useperformanceoptimization.ts", "../../src/hooks/usestats.ts", "../../src/lib/queryclient.ts", "../../src/lib/querykeys.ts", "../../src/services/api.ts", "../../src/store/authstore.ts", "../../src/store/uistore.ts", "../../src/tests/e2e/email-workflow.test.tsx", "../../src/tests/functional/basic-functionality.test.tsx", "../../src/tests/integration/state-management.test.tsx", "../../src/tests/setup/testsetup.ts", "../../src/types/index.ts", "../../src/utils/errorhandler.ts", "../../src/utils/mockdata.ts", "../../src/utils/performance.ts"], "errors": true, "version": "5.8.3"}