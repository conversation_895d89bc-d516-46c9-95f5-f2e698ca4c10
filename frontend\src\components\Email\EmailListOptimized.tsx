import React, { useEffect } from 'react';
import { List, Card, Button, Checkbox, Tag, Space, Spin, Empty, message } from 'antd';
import { 
  StarOutlined, 
  StarFilled, 
  DeleteOutlined, 
  MailOutlined,
  ReloadOutlined,
  EyeOutlined,
  EyeInvisibleOutlined
} from '@ant-design/icons';
import { useInfiniteEmails, useUpdateEmail, useDeleteEmail, usePrefetchEmail } from '../../hooks/useEmails';
import { useUIStore } from '../../store/uiStore';
import { showErrorMessage } from '../../utils/errorHandler';
import type { Email } from '../../types';

interface EmailListOptimizedProps {
  folderId?: string;
}

const EmailListOptimized: React.FC<EmailListOptimizedProps> = ({ folderId }) => {
  const {
    currentFolder,
    selectedEmails,
    viewMode,
    sortBy,
    sortOrder,
    selectEmail,
    unselectEmail,
    clearSelection,
    toggleEmailSelection,
    setCurrentEmail,
  } = useUIStore();

  // 使用无限滚动查询
  const {
    data,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    isLoading,
    error,
    refetch,
  } = useInfiniteEmails({
    folderId: folderId || currentFolder?.id,
  });

  // 邮件操作mutations
  const updateEmailMutation = useUpdateEmail();
  const deleteEmailMutation = useDeleteEmail();
  const prefetchEmail = usePrefetchEmail();

  // 扁平化邮件数据
  const emails = data?.pages.flatMap(page => page.data) || [];

  // 处理邮件选择
  const handleEmailSelect = (emailId: string, checked: boolean) => {
    if (checked) {
      selectEmail(emailId);
    } else {
      unselectEmail(emailId);
    }
  };

  // 处理全选
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      emails.forEach(email => selectEmail(email.id));
    } else {
      clearSelection();
    }
  };

  // 处理邮件点击
  const handleEmailClick = (email: Email) => {
    setCurrentEmail(email);
    
    // 如果邮件未读，标记为已读
    if (!email.isRead) {
      updateEmailMutation.mutate({
        id: email.id,
        updates: { isRead: true },
      });
    }
  };

  // 处理邮件悬停预取
  const handleEmailHover = (emailId: string) => {
    prefetchEmail(emailId);
  };

  // 处理标星
  const handleToggleStar = (email: Email, e: React.MouseEvent) => {
    e.stopPropagation();
    
    updateEmailMutation.mutate({
      id: email.id,
      updates: { isStarred: !email.isStarred },
    });
  };

  // 处理标记已读/未读
  const handleToggleRead = (email: Email, e: React.MouseEvent) => {
    e.stopPropagation();
    
    updateEmailMutation.mutate({
      id: email.id,
      updates: { isRead: !email.isRead },
    });
  };

  // 处理删除
  const handleDelete = (emailId: string, e: React.MouseEvent) => {
    e.stopPropagation();
    
    deleteEmailMutation.mutate(emailId);
  };

  // 批量操作
  const handleBatchMarkAsRead = () => {
    if (selectedEmails.length === 0) {
      message.warning('请先选择邮件');
      return;
    }

    selectedEmails.forEach(emailId => {
      updateEmailMutation.mutate({
        id: emailId,
        updates: { isRead: true },
      });
    });

    clearSelection();
  };

  const handleBatchDelete = () => {
    if (selectedEmails.length === 0) {
      message.warning('请先选择邮件');
      return;
    }

    selectedEmails.forEach(emailId => {
      deleteEmailMutation.mutate(emailId);
    });

    clearSelection();
  };

  // 加载更多
  const handleLoadMore = () => {
    if (hasNextPage && !isFetchingNextPage) {
      fetchNextPage();
    }
  };

  // 错误处理
  useEffect(() => {
    if (error) {
      showErrorMessage(error, '获取邮件列表失败');
    }
  }, [error]);

  // 渲染邮件项
  const renderEmailItem = (email: Email) => (
    <List.Item
      key={email.id}
      className={`email-item ${!email.isRead ? 'unread' : ''} ${
        selectedEmails.includes(email.id) ? 'selected' : ''
      }`}
      onClick={() => handleEmailClick(email)}
      onMouseEnter={() => handleEmailHover(email.id)}
      style={{
        cursor: 'pointer',
        padding: '12px 16px',
        borderBottom: '1px solid #f0f0f0',
        backgroundColor: selectedEmails.includes(email.id) ? '#e6f7ff' : 'transparent',
      }}
    >
      <div style={{ display: 'flex', alignItems: 'center', width: '100%' }}>
        {/* 选择框 */}
        <Checkbox
          checked={selectedEmails.includes(email.id)}
          onChange={(e) => handleEmailSelect(email.id, e.target.checked)}
          onClick={(e) => e.stopPropagation()}
          style={{ marginRight: 12 }}
        />

        {/* 标星按钮 */}
        <Button
          type="text"
          size="small"
          icon={email.isStarred ? <StarFilled style={{ color: '#faad14' }} /> : <StarOutlined />}
          onClick={(e) => handleToggleStar(email, e)}
          style={{ marginRight: 8 }}
        />

        {/* 邮件内容 */}
        <div style={{ flex: 1, minWidth: 0 }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <div style={{ display: 'flex', alignItems: 'center', minWidth: 0, flex: 1 }}>
              <span
                style={{
                  fontWeight: email.isRead ? 'normal' : 'bold',
                  marginRight: 8,
                  minWidth: 120,
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  whiteSpace: 'nowrap',
                }}
              >
                {email.sender.name || email.sender.email}
              </span>
              <span
                style={{
                  fontWeight: email.isRead ? 'normal' : 'bold',
                  flex: 1,
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  whiteSpace: 'nowrap',
                }}
              >
                {email.subject}
              </span>
            </div>
            
            <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
              {/* 标签 */}
              {!email.isRead && <Tag color="blue">未读</Tag>}
              {email.hasAttachments && <Tag color="orange">附件</Tag>}
              
              {/* 时间 */}
              <span style={{ color: '#666', fontSize: '12px', minWidth: 80, textAlign: 'right' }}>
                {new Date(email.createdAt).toLocaleDateString()}
              </span>

              {/* 操作按钮 */}
              <Space size="small">
                <Button
                  type="text"
                  size="small"
                  icon={email.isRead ? <EyeInvisibleOutlined /> : <EyeOutlined />}
                  onClick={(e) => handleToggleRead(email, e)}
                  title={email.isRead ? '标记为未读' : '标记为已读'}
                />
                <Button
                  type="text"
                  size="small"
                  icon={<DeleteOutlined />}
                  onClick={(e) => handleDelete(email.id, e)}
                  title="删除"
                  danger
                />
              </Space>
            </div>
          </div>
          
          {/* 邮件预览 */}
          <div
            style={{
              color: '#666',
              fontSize: '12px',
              marginTop: 4,
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              whiteSpace: 'nowrap',
            }}
          >
            {email.content.substring(0, 100)}...
          </div>
        </div>
      </div>
    </List.Item>
  );

  if (isLoading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
        <div style={{ marginTop: 16 }}>加载邮件中...</div>
      </div>
    );
  }

  if (emails.length === 0) {
    return (
      <Empty
        description="暂无邮件"
        image={Empty.PRESENTED_IMAGE_SIMPLE}
        style={{ padding: '50px' }}
      >
        <Button type="primary" icon={<ReloadOutlined />} onClick={() => refetch()}>
          刷新
        </Button>
      </Empty>
    );
  }

  return (
    <Card
      title={
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: 16 }}>
            <Checkbox
              indeterminate={selectedEmails.length > 0 && selectedEmails.length < emails.length}
              checked={selectedEmails.length === emails.length}
              onChange={(e) => handleSelectAll(e.target.checked)}
            >
              全选
            </Checkbox>
            <span>共 {emails.length} 封邮件</span>
          </div>
          
          {selectedEmails.length > 0 && (
            <Space>
              <Button size="small" onClick={handleBatchMarkAsRead}>
                标记已读 ({selectedEmails.length})
              </Button>
              <Button size="small" danger onClick={handleBatchDelete}>
                删除 ({selectedEmails.length})
              </Button>
            </Space>
          )}
        </div>
      }
      extra={
        <Button icon={<ReloadOutlined />} onClick={() => refetch()}>
          刷新
        </Button>
      }
      bodyStyle={{ padding: 0 }}
    >
      <List
        dataSource={emails}
        renderItem={renderEmailItem}
        style={{ maxHeight: 'calc(100vh - 200px)', overflow: 'auto' }}
      />
      
      {/* 加载更多 */}
      {hasNextPage && (
        <div style={{ textAlign: 'center', padding: '16px' }}>
          <Button
            loading={isFetchingNextPage}
            onClick={handleLoadMore}
            type="dashed"
            block
          >
            {isFetchingNextPage ? '加载中...' : '加载更多'}
          </Button>
        </div>
      )}
    </Card>
  );
};

export default EmailListOptimized;
