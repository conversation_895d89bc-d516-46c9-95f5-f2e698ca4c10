var e=Object.defineProperty,t=Object.defineProperties,n=Object.getOwnPropertyDescriptors,r=Object.getOwnPropertySymbols,a=Object.prototype.hasOwnProperty,o=Object.prototype.propertyIsEnumerable,i=(t,n,r)=>n in t?e(t,n,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[n]=r,l=(e,t)=>{for(var n in t||(t={}))a.call(t,n)&&i(e,n,t[n]);if(r)for(var n of r(t))o.call(t,n)&&i(e,n,t[n]);return e},u=(e,r)=>t(e,n(r)),s=(e,t)=>{var n={};for(var i in e)a.call(e,i)&&t.indexOf(i)<0&&(n[i]=e[i]);if(null!=e&&r)for(var i of r(e))t.indexOf(i)<0&&o.call(e,i)&&(n[i]=e[i]);return n},c=(e,t,n)=>new Promise(((r,a)=>{var o=e=>{try{l(n.next(e))}catch(t){a(t)}},i=e=>{try{l(n.throw(e))}catch(t){a(t)}},l=e=>e.done?r(e.value):Promise.resolve(e.value).then(o,i);l((n=n.apply(e,t)).next())}));import{r as h}from"./chunk-CkllRWJx.js";import"./chunk-Br9R7KlC.js";var p,d={};!function(){if(p)return d;p=1,Object.defineProperty(d,"__esModule",{value:!0}),d.parse=function(e,t){const n=new o,r=e.length;if(r<2)return n;const a=(null==t?void 0:t.decode)||u;let s=0;do{const t=e.indexOf("=",s);if(-1===t)break;const o=e.indexOf(";",s),u=-1===o?r:o;if(t>u){s=e.lastIndexOf(";",t-1)+1;continue}const c=i(e,s,t),h=l(e,t,c),p=e.slice(c,h);if(void 0===n[p]){let r=i(e,t+1,u),o=l(e,u,r);const s=a(e.slice(r,o));n[p]=s}s=u+1}while(s<r);return n},d.serialize=function(o,i,l){const u=(null==l?void 0:l.encode)||encodeURIComponent;if(!e.test(o))throw new TypeError(`argument name is invalid: ${o}`);const s=u(i);if(!t.test(s))throw new TypeError(`argument val is invalid: ${i}`);let c=o+"="+s;if(!l)return c;if(void 0!==l.maxAge){if(!Number.isInteger(l.maxAge))throw new TypeError(`option maxAge is invalid: ${l.maxAge}`);c+="; Max-Age="+l.maxAge}if(l.domain){if(!n.test(l.domain))throw new TypeError(`option domain is invalid: ${l.domain}`);c+="; Domain="+l.domain}if(l.path){if(!r.test(l.path))throw new TypeError(`option path is invalid: ${l.path}`);c+="; Path="+l.path}if(l.expires){if(!function(e){return"[object Date]"===a.call(e)}(l.expires)||!Number.isFinite(l.expires.valueOf()))throw new TypeError(`option expires is invalid: ${l.expires}`);c+="; Expires="+l.expires.toUTCString()}l.httpOnly&&(c+="; HttpOnly");l.secure&&(c+="; Secure");l.partitioned&&(c+="; Partitioned");if(l.priority){switch("string"==typeof l.priority?l.priority.toLowerCase():void 0){case"low":c+="; Priority=Low";break;case"medium":c+="; Priority=Medium";break;case"high":c+="; Priority=High";break;default:throw new TypeError(`option priority is invalid: ${l.priority}`)}}if(l.sameSite){switch("string"==typeof l.sameSite?l.sameSite.toLowerCase():l.sameSite){case!0:case"strict":c+="; SameSite=Strict";break;case"lax":c+="; SameSite=Lax";break;case"none":c+="; SameSite=None";break;default:throw new TypeError(`option sameSite is invalid: ${l.sameSite}`)}}return c};const e=/^[\u0021-\u003A\u003C\u003E-\u007E]+$/,t=/^[\u0021-\u003A\u003C-\u007E]*$/,n=/^([.]?[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)([.][a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/i,r=/^[\u0020-\u003A\u003D-\u007E]*$/,a=Object.prototype.toString,o=(()=>{const e=function(){};return e.prototype=Object.create(null),e})();function i(e,t,n){do{const n=e.charCodeAt(t);if(32!==n&&9!==n)return t}while(++t<n);return n}function l(e,t,n){for(;t>n;){const n=e.charCodeAt(--t);if(32!==n&&9!==n)return t+1}return n}function u(e){if(-1===e.indexOf("%"))return e;try{return decodeURIComponent(e)}catch(t){return e}}}();var m="popstate";function f(e={}){return function(e,t,n,r={}){let{window:a=document.defaultView,v5Compat:o=!1}=r,i=a.history,s="POP",c=null,h=p();null==h&&(h=0,i.replaceState(u(l({},i.state),{idx:h}),""));function p(){return(i.state||{idx:null}).idx}function d(){s="POP";let e=p(),t=null==e?null:e-h;h=e,c&&c({action:s,location:E.location,delta:t})}function f(e,t){s="PUSH";let n=w(E.location,e,t);h=p()+1;let r=g(n,h),l=E.createHref(n);try{i.pushState(r,"",l)}catch(u){if(u instanceof DOMException&&"DataCloneError"===u.name)throw u;a.location.assign(l)}o&&c&&c({action:s,location:E.location,delta:1})}function y(e,t){s="REPLACE";let n=w(E.location,e,t);h=p();let r=g(n,h),a=E.createHref(n);i.replaceState(r,"",a),o&&c&&c({action:s,location:E.location,delta:0})}function x(e){return function(e,t=!1){let n="http://localhost";"undefined"!=typeof window&&(n="null"!==window.location.origin?window.location.origin:window.location.href);v(n,"No window.location.(origin|href) available to create URL");let r="string"==typeof e?e:b(e);r=r.replace(/ $/,"%20"),!t&&r.startsWith("//")&&(r=n+r);return new URL(r,n)}(e)}let E={get action(){return s},get location(){return e(a,i)},listen(e){if(c)throw new Error("A history only accepts one active listener");return a.addEventListener(m,d),c=e,()=>{a.removeEventListener(m,d),c=null}},createHref:e=>t(a,e),createURL:x,encodeLocation(e){let t=x(e);return{pathname:t.pathname,search:t.search,hash:t.hash}},push:f,replace:y,go:e=>i.go(e)};return E}((function(e,t){let{pathname:n,search:r,hash:a}=e.location;return w("",{pathname:n,search:r,hash:a},t.state&&t.state.usr||null,t.state&&t.state.key||"default")}),(function(e,t){return"string"==typeof t?t:b(t)}),0,e)}function v(e,t){if(!1===e||null==e)throw new Error(t)}function y(e,t){if(!e)try{throw new Error(t)}catch(n){}}function g(e,t){return{usr:e.state,key:e.key,idx:t}}function w(e,t,n=null,r){return u(l({pathname:"string"==typeof e?e:e.pathname,search:"",hash:""},"string"==typeof t?x(t):t),{state:n,key:t&&t.key||r||Math.random().toString(36).substring(2,10)})}function b({pathname:e="/",search:t="",hash:n=""}){return t&&"?"!==t&&(e+="?"===t.charAt(0)?t:"?"+t),n&&"#"!==n&&(e+="#"===n.charAt(0)?n:"#"+n),e}function x(e){let t={};if(e){let n=e.indexOf("#");n>=0&&(t.hash=e.substring(n),e=e.substring(0,n));let r=e.indexOf("?");r>=0&&(t.search=e.substring(r),e=e.substring(0,r)),e&&(t.pathname=e)}return t}function E(e,t,n="/"){return function(e,t,n,r){let a="string"==typeof t?x(t):t,o=F(a.pathname||"/",n);if(null==o)return null;let i=C(e);!function(e){e.sort(((e,t)=>e.score!==t.score?t.score-e.score:function(e,t){let n=e.length===t.length&&e.slice(0,-1).every(((e,n)=>e===t[n]));return n?e[e.length-1]-t[t.length-1]:0}(e.routesMeta.map((e=>e.childrenIndex)),t.routesMeta.map((e=>e.childrenIndex)))))}(i);let l=null;for(let u=0;null==l&&u<i.length;++u){let e=D(o);l=N(i[u],e,r)}return l}(e,t,n,!1)}function C(e,t=[],n=[],r=""){let a=(e,a,o)=>{let i={relativePath:void 0===o?e.path||"":o,caseSensitive:!0===e.caseSensitive,childrenIndex:a,route:e};i.relativePath.startsWith("/")&&(v(i.relativePath.startsWith(r),`Absolute route path "${i.relativePath}" nested under path "${r}" is not valid. An absolute child route path must start with the combined path of all its parent routes.`),i.relativePath=i.relativePath.slice(r.length));let l=B([r,i.relativePath]),u=n.concat(i);e.children&&e.children.length>0&&(v(!0!==e.index,`Index routes must not have child routes. Please remove all child routes from route path "${l}".`),C(e.children,t,u,l)),(null!=e.path||e.index)&&t.push({path:l,score:A(l,e.index),routesMeta:u})};return e.forEach(((e,t)=>{var n;if(""!==e.path&&(null==(n=e.path)?void 0:n.includes("?")))for(let r of R(e.path))a(e,t,r);else a(e,t)})),t}function R(e){let t=e.split("/");if(0===t.length)return[];let[n,...r]=t,a=n.endsWith("?"),o=n.replace(/\?$/,"");if(0===r.length)return a?[o,""]:[o];let i=R(r.join("/")),l=[];return l.push(...i.map((e=>""===e?o:[o,e].join("/")))),a&&l.push(...i),l.map((t=>e.startsWith("/")&&""===t?"/":t))}var S=/^:[\w-]+$/,$=3,P=2,k=1,L=10,T=-2,O=e=>"*"===e;function A(e,t){let n=e.split("/"),r=n.length;return n.some(O)&&(r+=T),t&&(r+=P),n.filter((e=>!O(e))).reduce(((e,t)=>e+(S.test(t)?$:""===t?k:L)),r)}function N(e,t,n=!1){let{routesMeta:r}=e,a={},o="/",i=[];for(let l=0;l<r.length;++l){let e=r[l],u=l===r.length-1,s="/"===o?t:t.slice(o.length)||"/",c=M({path:e.relativePath,caseSensitive:e.caseSensitive,end:u},s),h=e.route;if(!c&&u&&n&&!r[r.length-1].route.index&&(c=M({path:e.relativePath,caseSensitive:e.caseSensitive,end:!1},s)),!c)return null;Object.assign(a,c.params),i.push({params:a,pathname:B([o,c.pathname]),pathnameBase:I(B([o,c.pathnameBase])),route:h}),"/"!==c.pathnameBase&&(o=B([o,c.pathnameBase]))}return i}function M(e,t){"string"==typeof e&&(e={path:e,caseSensitive:!1,end:!0});let[n,r]=function(e,t=!1,n=!0){y("*"===e||!e.endsWith("*")||e.endsWith("/*"),`Route path "${e}" will be treated as if it were "${e.replace(/\*$/,"/*")}" because the \`*\` character must always follow a \`/\` in the pattern. To get rid of this warning, please change the route path to "${e.replace(/\*$/,"/*")}".`);let r=[],a="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,((e,t,n)=>(r.push({paramName:t,isOptional:null!=n}),n?"/?([^\\/]+)?":"/([^\\/]+)")));e.endsWith("*")?(r.push({paramName:"*"}),a+="*"===e||"/*"===e?"(.*)$":"(?:\\/(.+)|\\/*)$"):n?a+="\\/*$":""!==e&&"/"!==e&&(a+="(?:(?=\\/|$))");let o=new RegExp(a,t?void 0:"i");return[o,r]}(e.path,e.caseSensitive,e.end),a=t.match(n);if(!a)return null;let o=a[0],i=o.replace(/(.)\/+$/,"$1"),l=a.slice(1);return{params:r.reduce(((e,{paramName:t,isOptional:n},r)=>{if("*"===t){let e=l[r]||"";i=o.slice(0,o.length-e.length).replace(/(.)\/+$/,"$1")}const a=l[r];return e[t]=n&&!a?void 0:(a||"").replace(/%2F/g,"/"),e}),{}),pathname:o,pathnameBase:i,pattern:e}}function D(e){try{return e.split("/").map((e=>decodeURIComponent(e).replace(/\//g,"%2F"))).join("/")}catch(t){return y(!1,`The URL path "${e}" could not be decoded because it is a malformed URL segment. This is probably due to a bad percent encoding (${t}).`),e}}function F(e,t){if("/"===t)return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let n=t.endsWith("/")?t.length-1:t.length,r=e.charAt(n);return r&&"/"!==r?null:e.slice(n)||"/"}function j(e,t,n,r){return`Cannot include a '${e}' character in a manually specified \`to.${t}\` field [${JSON.stringify(r)}].  Please separate it out to the \`to.${n}\` field. Alternatively you may provide the full path as a string in <Link to="..."> and the router will parse it for you.`}function U(e){let t=function(e){return e.filter(((e,t)=>0===t||e.route.path&&e.route.path.length>0))}(e);return t.map(((e,n)=>n===t.length-1?e.pathname:e.pathnameBase))}function W(e,t,n,r=!1){let a;"string"==typeof e?a=x(e):(a=l({},e),v(!a.pathname||!a.pathname.includes("?"),j("?","pathname","search",a)),v(!a.pathname||!a.pathname.includes("#"),j("#","pathname","hash",a)),v(!a.search||!a.search.includes("#"),j("#","search","hash",a)));let o,i=""===e||""===a.pathname,u=i?"/":a.pathname;if(null==u)o=n;else{let e=t.length-1;if(!r&&u.startsWith("..")){let t=u.split("/");for(;".."===t[0];)t.shift(),e-=1;a.pathname=t.join("/")}o=e>=0?t[e]:"/"}let s=function(e,t="/"){let{pathname:n,search:r="",hash:a=""}="string"==typeof e?x(e):e,o=n?n.startsWith("/")?n:function(e,t){let n=t.replace(/\/+$/,"").split("/");return e.split("/").forEach((e=>{".."===e?n.length>1&&n.pop():"."!==e&&n.push(e)})),n.length>1?n.join("/"):"/"}(n,t):t;return{pathname:o,search:z(r),hash:_(a)}}(a,o),c=u&&"/"!==u&&u.endsWith("/"),h=(i||"."===u)&&n.endsWith("/");return s.pathname.endsWith("/")||!c&&!h||(s.pathname+="/"),s}var B=e=>e.join("/").replace(/\/\/+/g,"/"),I=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),z=e=>e&&"?"!==e?e.startsWith("?")?e:"?"+e:"",_=e=>e&&"#"!==e?e.startsWith("#")?e:"#"+e:"";var H=["POST","PUT","PATCH","DELETE"];new Set(H);var J=["GET",...H];new Set(J);var Y=h.createContext(null);Y.displayName="DataRouter";var K=h.createContext(null);K.displayName="DataRouterState";var V=h.createContext({isTransitioning:!1});V.displayName="ViewTransition",h.createContext(new Map).displayName="Fetchers",h.createContext(null).displayName="Await";var q=h.createContext(null);q.displayName="Navigation";var G=h.createContext(null);G.displayName="Location";var X=h.createContext({outlet:null,matches:[],isDataRoute:!1});X.displayName="Route";var Q=h.createContext(null);function Z(){return null!=h.useContext(G)}function ee(){return v(Z(),"useLocation() may be used only in the context of a <Router> component."),h.useContext(G).location}Q.displayName="RouteError";var te="You should call navigate() in a React.useEffect(), not when your component is first rendered.";function ne(e){h.useContext(q).static||h.useLayoutEffect(e)}function re(){let{isDataRoute:e}=h.useContext(X);return e?function(){let{router:e}=function(e){let t=h.useContext(Y);return v(t,pe(e)),t}("useNavigate"),t=de("useNavigate"),n=h.useRef(!1);return ne((()=>{n.current=!0})),h.useCallback(((r,...a)=>c(null,[r,...a],(function*(r,a={}){y(n.current,te),n.current&&("number"==typeof r?e.navigate(r):yield e.navigate(r,l({fromRouteId:t},a)))}))),[e,t])}():function(){v(Z(),"useNavigate() may be used only in the context of a <Router> component.");let e=h.useContext(Y),{basename:t,navigator:n}=h.useContext(q),{matches:r}=h.useContext(X),{pathname:a}=ee(),o=JSON.stringify(U(r)),i=h.useRef(!1);return ne((()=>{i.current=!0})),h.useCallback(((r,l={})=>{if(y(i.current,te),!i.current)return;if("number"==typeof r)return void n.go(r);let u=W(r,JSON.parse(o),a,"path"===l.relative);null==e&&"/"!==t&&(u.pathname="/"===u.pathname?t:B([t,u.pathname])),(l.replace?n.replace:n.push)(u,l.state,l)}),[t,n,o,a,e])}()}var ae=h.createContext(null);function oe(){let{matches:e}=h.useContext(X),t=e[e.length-1];return t?t.params:{}}function ie(e,{relative:t}={}){let{matches:n}=h.useContext(X),{pathname:r}=ee(),a=JSON.stringify(U(n));return h.useMemo((()=>W(e,JSON.parse(a),r,"path"===t)),[e,a,r,t])}function le(e,t,n,r){var a;v(Z(),"useRoutes() may be used only in the context of a <Router> component.");let{navigator:o}=h.useContext(q),{matches:i}=h.useContext(X),u=i[i.length-1],s=u?u.params:{},c=u?u.pathname:"/",p=u?u.pathnameBase:"/",d=u&&u.route;{let e=d&&d.path||"";fe(c,!d||e.endsWith("*")||e.endsWith("*?"),`You rendered descendant <Routes> (or called \`useRoutes()\`) at "${c}" (under <Route path="${e}">) but the parent route path has no trailing "*". This means if you navigate deeper, the parent won't match anymore and therefore the child routes will never render.\n\nPlease change the parent <Route path="${e}"> to <Route path="${"/"===e?"*":`${e}/*`}">.`)}let m,f=ee();if(t){let e="string"==typeof t?x(t):t;v("/"===p||(null==(a=e.pathname)?void 0:a.startsWith(p)),`When overriding the location using \`<Routes location>\` or \`useRoutes(routes, location)\`, the location pathname must begin with the portion of the URL pathname that was matched by all parent routes. The current pathname base is "${p}" but pathname "${e.pathname}" was given in the \`location\` prop.`),m=e}else m=f;let g=m.pathname||"/",w=g;if("/"!==p){let e=p.replace(/^\//,"").split("/");w="/"+g.replace(/^\//,"").split("/").slice(e.length).join("/")}let b=E(e,{pathname:w});y(d||null!=b,`No routes matched location "${m.pathname}${m.search}${m.hash}" `),y(null==b||void 0!==b[b.length-1].route.element||void 0!==b[b.length-1].route.Component||void 0!==b[b.length-1].route.lazy,`Matched leaf route at location "${m.pathname}${m.search}${m.hash}" does not have an element or Component. This means it will render an <Outlet /> with a null value by default resulting in an "empty" page.`);let C=function(e,t=[],n=null){if(null==e){if(!n)return null;if(n.errors)e=n.matches;else{if(0!==t.length||n.initialized||!(n.matches.length>0))return null;e=n.matches}}let r=e,a=null==n?void 0:n.errors;if(null!=a){let e=r.findIndex((e=>e.route.id&&void 0!==(null==a?void 0:a[e.route.id])));v(e>=0,`Could not find a matching route for errors on route IDs: ${Object.keys(a).join(",")}`),r=r.slice(0,Math.min(r.length,e+1))}let o=!1,i=-1;if(n)for(let l=0;l<r.length;l++){let e=r[l];if((e.route.HydrateFallback||e.route.hydrateFallbackElement)&&(i=l),e.route.id){let{loaderData:t,errors:a}=n,l=e.route.loader&&!t.hasOwnProperty(e.route.id)&&(!a||void 0===a[e.route.id]);if(e.route.lazy||l){o=!0,r=i>=0?r.slice(0,i+1):[r[0]];break}}}return r.reduceRight(((e,l,u)=>{let s,c=!1,p=null,d=null;n&&(s=a&&l.route.id?a[l.route.id]:void 0,p=l.route.errorElement||se,o&&(i<0&&0===u?(fe("route-fallback",!1,"No `HydrateFallback` element provided to render during initial hydration"),c=!0,d=null):i===u&&(c=!0,d=l.route.hydrateFallbackElement||null)));let m=t.concat(r.slice(0,u+1)),f=()=>{let t;return t=s?p:c?d:l.route.Component?h.createElement(l.route.Component,null):l.route.element?l.route.element:e,h.createElement(he,{match:l,routeContext:{outlet:e,matches:m,isDataRoute:null!=n},children:t})};return n&&(l.route.ErrorBoundary||l.route.errorElement||0===u)?h.createElement(ce,{location:n.location,revalidation:n.revalidation,component:p,error:s,children:f(),routeContext:{outlet:null,matches:m,isDataRoute:!0}}):f()}),null)}(b&&b.map((e=>Object.assign({},e,{params:Object.assign({},s,e.params),pathname:B([p,o.encodeLocation?o.encodeLocation(e.pathname).pathname:e.pathname]),pathnameBase:"/"===e.pathnameBase?p:B([p,o.encodeLocation?o.encodeLocation(e.pathnameBase).pathname:e.pathnameBase])}))),i,n,r);return t&&C?h.createElement(G.Provider,{value:{location:l({pathname:"/",search:"",hash:"",state:null,key:"default"},m),navigationType:"POP"}},C):C}function ue(){let e=function(){var e;let t=h.useContext(Q),n=function(e){let t=h.useContext(K);return v(t,pe(e)),t}("useRouteError"),r=de("useRouteError");if(void 0!==t)return t;return null==(e=n.errors)?void 0:e[r]}(),t=function(e){return null!=e&&"number"==typeof e.status&&"string"==typeof e.statusText&&"boolean"==typeof e.internal&&"data"in e}(e)?`${e.status} ${e.statusText}`:e instanceof Error?e.message:JSON.stringify(e),n=e instanceof Error?e.stack:null,r="rgba(200,200,200, 0.5)",a={padding:"0.5rem",backgroundColor:r},o={padding:"2px 4px",backgroundColor:r},i=null;return i=h.createElement(h.Fragment,null,h.createElement("p",null,"💿 Hey developer 👋"),h.createElement("p",null,"You can provide a way better UX than this when your app throws errors by providing your own ",h.createElement("code",{style:o},"ErrorBoundary")," or"," ",h.createElement("code",{style:o},"errorElement")," prop on your route.")),h.createElement(h.Fragment,null,h.createElement("h2",null,"Unexpected Application Error!"),h.createElement("h3",{style:{fontStyle:"italic"}},t),n?h.createElement("pre",{style:a},n):null,i)}var se=h.createElement(ue,null),ce=class extends h.Component{constructor(e){super(e),this.state={location:e.location,revalidation:e.revalidation,error:e.error}}static getDerivedStateFromError(e){return{error:e}}static getDerivedStateFromProps(e,t){return t.location!==e.location||"idle"!==t.revalidation&&"idle"===e.revalidation?{error:e.error,location:e.location,revalidation:e.revalidation}:{error:void 0!==e.error?e.error:t.error,location:t.location,revalidation:e.revalidation||t.revalidation}}componentDidCatch(e,t){}render(){return void 0!==this.state.error?h.createElement(X.Provider,{value:this.props.routeContext},h.createElement(Q.Provider,{value:this.state.error,children:this.props.component})):this.props.children}};function he({routeContext:e,match:t,children:n}){let r=h.useContext(Y);return r&&r.static&&r.staticContext&&(t.route.errorElement||t.route.ErrorBoundary)&&(r.staticContext._deepestRenderedBoundaryId=t.route.id),h.createElement(X.Provider,{value:e},n)}function pe(e){return`${e} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function de(e){let t=function(e){let t=h.useContext(X);return v(t,pe(e)),t}(e),n=t.matches[t.matches.length-1];return v(n.route.id,`${e} can only be used on routes that contain a unique "id"`),n.route.id}var me={};function fe(e,t,n){t||me[e]||(me[e]=!0,y(!1,n))}function ve({to:e,replace:t,state:n,relative:r}){v(Z(),"<Navigate> may be used only in the context of a <Router> component.");let{static:a}=h.useContext(q);y(!a,"<Navigate> must not be used on the initial render in a <StaticRouter>. This is a no-op, but you should modify your code so the <Navigate> is only ever rendered in response to some user interaction or state change.");let{matches:o}=h.useContext(X),{pathname:i}=ee(),l=re(),u=W(e,U(o),i,"path"===r),s=JSON.stringify(u);return h.useEffect((()=>{l(JSON.parse(s),{replace:t,state:n,relative:r})}),[l,s,r,t,n]),null}function ye(e){return function(e){let t=h.useContext(X).outlet;return t?h.createElement(ae.Provider,{value:e},t):t}(e.context)}function ge(e){v(!1,"A <Route> is only ever to be used as the child of <Routes> element, never rendered directly. Please wrap your <Route> in a <Routes>.")}function we({basename:e="/",children:t=null,location:n,navigationType:r="POP",navigator:a,static:o=!1}){v(!Z(),"You cannot render a <Router> inside another <Router>. You should never have more than one in your app.");let i=e.replace(/^\/*/,"/"),l=h.useMemo((()=>({basename:i,navigator:a,static:o,future:{}})),[i,a,o]);"string"==typeof n&&(n=x(n));let{pathname:u="/",search:s="",hash:c="",state:p=null,key:d="default"}=n,m=h.useMemo((()=>{let e=F(u,i);return null==e?null:{location:{pathname:e,search:s,hash:c,state:p,key:d},navigationType:r}}),[i,u,s,c,p,d,r]);return y(null!=m,`<Router basename="${i}"> is not able to match the URL "${u}${s}${c}" because it does not start with the basename, so the <Router> won't render anything.`),null==m?null:h.createElement(q.Provider,{value:l},h.createElement(G.Provider,{children:t,value:m}))}function be({children:e,location:t}){return le(xe(e),t)}function xe(e,t=[]){let n=[];return h.Children.forEach(e,((e,r)=>{if(!h.isValidElement(e))return;let a=[...t,r];if(e.type===h.Fragment)return void n.push.apply(n,xe(e.props.children,a));v(e.type===ge,`[${"string"==typeof e.type?e.type:e.type.name}] is not a <Route> component. All component children of <Routes> must be a <Route> or <React.Fragment>`),v(!e.props.index||!e.props.children,"An index route cannot have child routes.");let o={id:e.props.id||a.join("-"),caseSensitive:e.props.caseSensitive,element:e.props.element,Component:e.props.Component,index:e.props.index,path:e.props.path,loader:e.props.loader,action:e.props.action,hydrateFallbackElement:e.props.hydrateFallbackElement,HydrateFallback:e.props.HydrateFallback,errorElement:e.props.errorElement,ErrorBoundary:e.props.ErrorBoundary,hasErrorBoundary:!0===e.props.hasErrorBoundary||null!=e.props.ErrorBoundary||null!=e.props.errorElement,shouldRevalidate:e.props.shouldRevalidate,handle:e.props.handle,lazy:e.props.lazy};e.props.children&&(o.children=xe(e.props.children,a)),n.push(o)})),n}h.memo((function({routes:e,future:t,state:n}){return le(e,void 0,n,t)}));var Ee="get",Ce="application/x-www-form-urlencoded";function Re(e){return null!=e&&"string"==typeof e.tagName}function Se(e=""){return new URLSearchParams("string"==typeof e||Array.isArray(e)||e instanceof URLSearchParams?e:Object.keys(e).reduce(((t,n)=>{let r=e[n];return t.concat(Array.isArray(r)?r.map((e=>[n,e])):[[n,r]])}),[]))}var $e=null;var Pe=new Set(["application/x-www-form-urlencoded","multipart/form-data","text/plain"]);function ke(e){return null==e||Pe.has(e)?e:(y(!1,`"${e}" is not a valid \`encType\` for \`<Form>\`/\`<fetcher.Form>\` and will default to "${Ce}"`),null)}function Le(e,t){let n,r,a,o,i;if(Re(l=e)&&"form"===l.tagName.toLowerCase()){let i=e.getAttribute("action");r=i?F(i,t):null,n=e.getAttribute("method")||Ee,a=ke(e.getAttribute("enctype"))||Ce,o=new FormData(e)}else if(function(e){return Re(e)&&"button"===e.tagName.toLowerCase()}(e)||function(e){return Re(e)&&"input"===e.tagName.toLowerCase()}(e)&&("submit"===e.type||"image"===e.type)){let i=e.form;if(null==i)throw new Error('Cannot submit a <button> or <input type="submit"> without a <form>');let l=e.getAttribute("formaction")||i.getAttribute("action");if(r=l?F(l,t):null,n=e.getAttribute("formmethod")||i.getAttribute("method")||Ee,a=ke(e.getAttribute("formenctype"))||ke(i.getAttribute("enctype"))||Ce,o=new FormData(i,e),!function(){if(null===$e)try{new FormData(document.createElement("form"),0),$e=!1}catch(e){$e=!0}return $e}()){let{name:t,type:n,value:r}=e;if("image"===n){let e=t?`${t}.`:"";o.append(`${e}x`,"0"),o.append(`${e}y`,"0")}else t&&o.append(t,r)}}else{if(Re(e))throw new Error('Cannot submit element that is not <form>, <button>, or <input type="submit|image">');n=Ee,r=null,a=Ce,i=e}var l;return o&&"text/plain"===a&&(i=o,o=void 0),{action:r,method:n.toLowerCase(),encType:a,formData:o,body:i}}function Te(e,t){if(!1===e||null==e)throw new Error(t)}function Oe(e){return null!=e&&(null==e.href?"preload"===e.rel&&"string"==typeof e.imageSrcSet&&"string"==typeof e.imageSizes:"string"==typeof e.rel&&"string"==typeof e.href)}function Ae(e,t,n){return c(this,null,(function*(){return function(e,t){let n=new Set;return new Set(t),e.reduce(((e,t)=>{let r=JSON.stringify(function(e){let t={},n=Object.keys(e).sort();for(let r of n)t[r]=e[r];return t}(t));return n.has(r)||(n.add(r),e.push({key:r,link:t})),e}),[])}((yield Promise.all(e.map((e=>c(null,null,(function*(){let r=t.routes[e.route.id];if(r){let e=yield function(e,t){return c(this,null,(function*(){if(e.id in t)return t[e.id];try{let n=yield import(e.module);return t[e.id]=n,n}catch(n){return window.__reactRouterContext&&window.__reactRouterContext.isSpaMode,window.location.reload(),new Promise((()=>{}))}}))}(r,n);return e.links?e.links():[]}return[]})))))).flat(1).filter(Oe).filter((e=>"stylesheet"===e.rel||"preload"===e.rel)).map((e=>"stylesheet"===e.rel?u(l({},e),{rel:"prefetch",as:"style"}):u(l({},e),{rel:"prefetch"}))))}))}function Ne(e,t,n,r,a,o){let i=(e,t)=>!n[t]||e.route.id!==n[t].route.id,l=(e,t)=>{var r;return n[t].pathname!==e.pathname||(null==(r=n[t].route.path)?void 0:r.endsWith("*"))&&n[t].params["*"]!==e.params["*"]};return"assets"===o?t.filter(((e,t)=>i(e,t)||l(e,t))):"data"===o?t.filter(((t,o)=>{var u;let s=r.routes[t.route.id];if(!s||!s.hasLoader)return!1;if(i(t,o)||l(t,o))return!0;if(t.route.shouldRevalidate){let r=t.route.shouldRevalidate({currentUrl:new URL(a.pathname+a.search+a.hash,window.origin),currentParams:(null==(u=n[0])?void 0:u.params)||{},nextUrl:new URL(e,window.origin),nextParams:t.params,defaultShouldRevalidate:!0});if("boolean"==typeof r)return r}return!0})):[]}function Me(e,t,{includeHydrateFallback:n}={}){return r=e.map((e=>{let r=t.routes[e.route.id];if(!r)return[];let a=[r.module];return r.clientActionModule&&(a=a.concat(r.clientActionModule)),r.clientLoaderModule&&(a=a.concat(r.clientLoaderModule)),n&&r.hydrateFallbackModule&&(a=a.concat(r.hydrateFallbackModule)),r.imports&&(a=a.concat(r.imports)),a})).flat(1),[...new Set(r)];var r}Object.getOwnPropertyNames(Object.prototype).sort().join("\0");function De(){let e=h.useContext(Y);return Te(e,"You must render this element inside a <DataRouterContext.Provider> element"),e}var Fe=h.createContext(void 0);function je(){let e=h.useContext(Fe);return Te(e,"You must render this element inside a <HydratedRouter> element"),e}function Ue(e,t){return n=>{e&&e(n),n.defaultPrevented||t(n)}}function We(e){var t=e,{page:n}=t,r=s(t,["page"]);let{router:a}=De(),o=h.useMemo((()=>E(a.routes,n,a.basename)),[a.routes,n,a.basename]);return o?h.createElement(Be,l({page:n,matches:o},r)):null}function Be(e){var t=e,{page:n,matches:r}=t,a=s(t,["page","matches"]);let o=ee(),{manifest:i,routeModules:u}=je(),{basename:c}=De(),{loaderData:p,matches:d}=function(){let e=h.useContext(K);return Te(e,"You must render this element inside a <DataRouterStateContext.Provider> element"),e}(),m=h.useMemo((()=>Ne(n,r,d,i,o,"data")),[n,r,d,i,o]),f=h.useMemo((()=>Ne(n,r,d,i,o,"assets")),[n,r,d,i,o]),v=h.useMemo((()=>{if(n===o.pathname+o.search+o.hash)return[];let e=new Set,t=!1;if(r.forEach((n=>{var r;let a=i.routes[n.route.id];a&&a.hasLoader&&(!m.some((e=>e.route.id===n.route.id))&&n.route.id in p&&(null==(r=u[n.route.id])?void 0:r.shouldRevalidate)||a.hasClientLoader?t=!0:e.add(n.route.id))})),0===e.size)return[];let a=function(e,t){let n="string"==typeof e?new URL(e,"undefined"==typeof window?"server://singlefetch/":window.location.origin):e;return"/"===n.pathname?n.pathname="_root.data":t&&"/"===F(n.pathname,t)?n.pathname=`${t.replace(/\/$/,"")}/_root.data`:n.pathname=`${n.pathname.replace(/\/$/,"")}.data`,n}(n,c);return t&&e.size>0&&a.searchParams.set("_routes",r.filter((t=>e.has(t.route.id))).map((e=>e.route.id)).join(",")),[a.pathname+a.search]}),[c,p,o,i,m,r,n,u]),y=h.useMemo((()=>Me(f,i)),[f,i]),g=function(e){let{manifest:t,routeModules:n}=je(),[r,a]=h.useState([]);return h.useEffect((()=>{let r=!1;return Ae(e,t,n).then((e=>{r||a(e)})),()=>{r=!0}}),[e,t,n]),r}(f);return h.createElement(h.Fragment,null,v.map((e=>h.createElement("link",l({key:e,rel:"prefetch",as:"fetch",href:e},a)))),y.map((e=>h.createElement("link",l({key:e,rel:"modulepreload",href:e},a)))),g.map((({key:e,link:t})=>h.createElement("link",l({key:e},t)))))}function Ie(...e){return t=>{e.forEach((e=>{"function"==typeof e?e(t):null!=e&&(e.current=t)}))}}Fe.displayName="FrameworkContext";var ze="undefined"!=typeof window&&void 0!==window.document&&void 0!==window.document.createElement;try{ze&&(window.__reactRouterVersion="7.6.2")}catch(Ge){}function _e({basename:e,children:t,window:n}){let r=h.useRef();null==r.current&&(r.current=f({window:n,v5Compat:!0}));let a=r.current,[o,i]=h.useState({action:a.action,location:a.location}),l=h.useCallback((e=>{h.startTransition((()=>i(e)))}),[i]);return h.useLayoutEffect((()=>a.listen(l)),[a,l]),h.createElement(we,{basename:e,children:t,location:o.location,navigationType:o.action,navigator:a})}var He=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,Je=h.forwardRef((function(e,t){var n=e,{onClick:r,discover:a="render",prefetch:o="none",relative:i,reloadDocument:c,replace:p,state:d,target:m,to:f,preventScrollReset:g,viewTransition:w}=n,x=s(n,["onClick","discover","prefetch","relative","reloadDocument","replace","state","target","to","preventScrollReset","viewTransition"]);let E,{basename:C}=h.useContext(q),R="string"==typeof f&&He.test(f),S=!1;if("string"==typeof f&&R&&(E=f,ze))try{let e=new URL(window.location.href),t=f.startsWith("//")?new URL(e.protocol+f):new URL(f),n=F(t.pathname,C);t.origin===e.origin&&null!=n?f=n+t.search+t.hash:S=!0}catch(Ge){y(!1,`<Link to="${f}"> contains an invalid URL which will probably break when clicked - please update to a valid URL path.`)}let $=function(e,{relative:t}={}){v(Z(),"useHref() may be used only in the context of a <Router> component.");let{basename:n,navigator:r}=h.useContext(q),{hash:a,pathname:o,search:i}=ie(e,{relative:t}),l=o;return"/"!==n&&(l="/"===o?n:B([n,o])),r.createHref({pathname:l,search:i,hash:a})}(f,{relative:i}),[P,k,L]=function(e,t){let n=h.useContext(Fe),[r,a]=h.useState(!1),[o,i]=h.useState(!1),{onFocus:l,onBlur:u,onMouseEnter:s,onMouseLeave:c,onTouchStart:p}=t,d=h.useRef(null);h.useEffect((()=>{if("render"===e&&i(!0),"viewport"===e){let e=new IntersectionObserver((e=>{e.forEach((e=>{i(e.isIntersecting)}))}),{threshold:.5});return d.current&&e.observe(d.current),()=>{e.disconnect()}}}),[e]),h.useEffect((()=>{if(r){let e=setTimeout((()=>{i(!0)}),100);return()=>{clearTimeout(e)}}}),[r]);let m=()=>{a(!0)},f=()=>{a(!1),i(!1)};return n?"intent"!==e?[o,d,{}]:[o,d,{onFocus:Ue(l,m),onBlur:Ue(u,f),onMouseEnter:Ue(s,m),onMouseLeave:Ue(c,f),onTouchStart:Ue(p,m)}]:[!1,d,{}]}(o,x),T=function(e,{target:t,replace:n,state:r,preventScrollReset:a,relative:o,viewTransition:i}={}){let l=re(),u=ee(),s=ie(e,{relative:o});return h.useCallback((c=>{if(function(e,t){return!(0!==e.button||t&&"_self"!==t||function(e){return!!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)}(e))}(c,t)){c.preventDefault();let t=void 0!==n?n:b(u)===b(s);l(e,{replace:t,state:r,preventScrollReset:a,relative:o,viewTransition:i})}}),[u,l,s,n,r,t,e,a,o,i])}(f,{replace:p,state:d,target:m,preventScrollReset:g,relative:i,viewTransition:w});let O=h.createElement("a",u(l(l({},x),L),{href:E||$,onClick:S||c?r:function(e){r&&r(e),e.defaultPrevented||T(e)},ref:Ie(t,k),target:m,"data-discover":R||"render"!==a?void 0:"true"}));return P&&!R?h.createElement(h.Fragment,null,O,h.createElement(We,{page:$})):O}));function Ye(e){let t=h.useContext(Y);return v(t,function(e){return`${e} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}(e)),t}function Ke(e){y("undefined"!=typeof URLSearchParams,"You cannot use the `useSearchParams` hook in a browser that does not support the URLSearchParams API. If you need to support Internet Explorer 11, we recommend you load a polyfill such as https://github.com/ungap/url-search-params.");let t=h.useRef(Se(e)),n=h.useRef(!1),r=ee(),a=h.useMemo((()=>function(e,t){let n=Se(e);return t&&t.forEach(((e,r)=>{n.has(r)||t.getAll(r).forEach((e=>{n.append(r,e)}))})),n}(r.search,n.current?null:t.current)),[r.search]),o=re(),i=h.useCallback(((e,t)=>{const r=Se("function"==typeof e?e(a):e);n.current=!0,o("?"+r,t)}),[o,a]);return[a,i]}Je.displayName="Link",h.forwardRef((function(e,t){var n=e,{"aria-current":r="page",caseSensitive:a=!1,className:o="",end:i=!1,style:c,to:p,viewTransition:d,children:m}=n,f=s(n,["aria-current","caseSensitive","className","end","style","to","viewTransition","children"]);let y=ie(p,{relative:f.relative}),g=ee(),w=h.useContext(K),{navigator:b,basename:x}=h.useContext(q),E=null!=w&&function(e,t={}){let n=h.useContext(V);v(null!=n,"`useViewTransitionState` must be used within `react-router-dom`'s `RouterProvider`.  Did you accidentally import `RouterProvider` from `react-router`?");let{basename:r}=Ye("useViewTransitionState"),a=ie(e,{relative:t.relative});if(!n.isTransitioning)return!1;let o=F(n.currentLocation.pathname,r)||n.currentLocation.pathname,i=F(n.nextLocation.pathname,r)||n.nextLocation.pathname;return null!=M(a.pathname,i)||null!=M(a.pathname,o)}(y)&&!0===d,C=b.encodeLocation?b.encodeLocation(y).pathname:y.pathname,R=g.pathname,S=w&&w.navigation&&w.navigation.location?w.navigation.location.pathname:null;a||(R=R.toLowerCase(),S=S?S.toLowerCase():null,C=C.toLowerCase()),S&&x&&(S=F(S,x)||S);const $="/"!==C&&C.endsWith("/")?C.length-1:C.length;let P,k=R===C||!i&&R.startsWith(C)&&"/"===R.charAt($),L=null!=S&&(S===C||!i&&S.startsWith(C)&&"/"===S.charAt(C.length)),T={isActive:k,isPending:L,isTransitioning:E},O=k?r:void 0;P="function"==typeof o?o(T):[o,k?"active":null,L?"pending":null,E?"transitioning":null].filter(Boolean).join(" ");let A="function"==typeof c?c(T):c;return h.createElement(Je,u(l({},f),{"aria-current":O,className:P,ref:t,style:A,to:p,viewTransition:d}),"function"==typeof m?m(T):m)})).displayName="NavLink",h.forwardRef(((e,t)=>{var n=e,{discover:r="render",fetcherKey:a,navigate:o,reloadDocument:i,replace:p,state:d,method:m=Ee,action:f,onSubmit:y,relative:g,preventScrollReset:w,viewTransition:x}=n,E=s(n,["discover","fetcherKey","navigate","reloadDocument","replace","state","method","action","onSubmit","relative","preventScrollReset","viewTransition"]);let C=function(){let{router:e}=Ye("useSubmit"),{basename:t}=h.useContext(q),n=de("useRouteId");return h.useCallback(((r,...a)=>c(null,[r,...a],(function*(r,a={}){let{action:o,method:i,encType:l,formData:u,body:s}=Le(r,t);if(!1===a.navigate){let t=a.fetcherKey||qe();yield e.fetch(t,n,a.action||o,{preventScrollReset:a.preventScrollReset,formData:u,body:s,formMethod:a.method||i,formEncType:a.encType||l,flushSync:a.flushSync})}else yield e.navigate(a.action||o,{preventScrollReset:a.preventScrollReset,formData:u,body:s,formMethod:a.method||i,formEncType:a.encType||l,replace:a.replace,state:a.state,fromRouteId:n,flushSync:a.flushSync,viewTransition:a.viewTransition})}))),[e,t,n])}(),R=function(e,{relative:t}={}){let{basename:n}=h.useContext(q),r=h.useContext(X);v(r,"useFormAction must be used inside a RouteContext");let[a]=r.matches.slice(-1),o=l({},ie(e||".",{relative:t})),i=ee();if(null==e){o.search=i.search;let e=new URLSearchParams(o.search),t=e.getAll("index");if(t.some((e=>""===e))){e.delete("index"),t.filter((e=>e)).forEach((t=>e.append("index",t)));let n=e.toString();o.search=n?`?${n}`:""}}e&&"."!==e||!a.route.index||(o.search=o.search?o.search.replace(/^\?/,"?index&"):"?index");"/"!==n&&(o.pathname="/"===o.pathname?n:B([n,o.pathname]));return b(o)}(f,{relative:g}),S="get"===m.toLowerCase()?"get":"post",$="string"==typeof f&&He.test(f);return h.createElement("form",u(l({ref:t,method:S,action:R,onSubmit:i?y:e=>{if(y&&y(e),e.defaultPrevented)return;e.preventDefault();let t=e.nativeEvent.submitter,n=(null==t?void 0:t.getAttribute("formmethod"))||m;C(t||e.currentTarget,{fetcherKey:a,method:n,navigate:o,replace:p,state:d,relative:g,preventScrollReset:w,viewTransition:x})}},E),{"data-discover":$||"render"!==r?void 0:"true"}))})).displayName="Form";var Ve=0,qe=()=>`__${String(++Ve)}__`;export{_e as B,Je as L,ve as N,ye as O,be as R,oe as a,Ke as b,ee as c,ge as d,re as u};
