import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, Button, Space, Typography, Modal } from 'antd';
import { 
  WifiOutlined, 
  ReloadOutlined, 
  ExclamationCircleOutlined,
  CloseCircleOutlined 
} from '@ant-design/icons';

const { Text } = Typography;

interface NetworkErrorHandlerProps {
  children: React.ReactNode;
}

interface NetworkError {
  id: string;
  message: string;
  timestamp: Date;
  retryCount: number;
  canRetry: boolean;
}

const NetworkErrorHandler: React.FC<NetworkErrorHandlerProps> = ({ children }) => {
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const [networkErrors, setNetworkErrors] = useState<NetworkError[]>([]);
  const [showOfflineModal, setShowOfflineModal] = useState(false);

  useEffect(() => {
    const handleOnline = () => {
      setIsOnline(true);
      setShowOfflineModal(false);
      // 清除网络错误
      setNetworkErrors([]);
    };

    const handleOffline = () => {
      setIsOnline(false);
      setShowOfflineModal(true);
    };

    // 监听网络状态变化
    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    // 监听全局错误事件
    const handleGlobalError = (event: ErrorEvent) => {
      if (event.error && isNetworkError(event.error)) {
        addNetworkError(event.error);
      }
    };

    // 监听未处理的Promise拒绝
    const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
      if (isNetworkError(event.reason)) {
        addNetworkError(event.reason);
      }
    };

    window.addEventListener('error', handleGlobalError);
    window.addEventListener('unhandledrejection', handleUnhandledRejection);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
      window.removeEventListener('error', handleGlobalError);
      window.removeEventListener('unhandledrejection', handleUnhandledRejection);
    };
  }, []);

  const isNetworkError = (error: any): boolean => {
    if (!error) return false;
    
    const networkErrorMessages = [
      'Network Error',
      'Failed to fetch',
      'ERR_NETWORK',
      'ERR_INTERNET_DISCONNECTED',
      'ERR_CONNECTION_REFUSED',
      'ECONNREFUSED',
      'ENOTFOUND',
      'ETIMEDOUT',
    ];

    const errorMessage = error.message || error.toString();
    return networkErrorMessages.some(msg => 
      errorMessage.toLowerCase().includes(msg.toLowerCase())
    );
  };

  const addNetworkError = (error: any) => {
    const networkError: NetworkError = {
      id: `net_err_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      message: error.message || '网络连接失败',
      timestamp: new Date(),
      retryCount: 0,
      canRetry: true,
    };

    setNetworkErrors(prev => {
      // 避免重复添加相同的错误
      const exists = prev.some(err => err.message === networkError.message);
      if (exists) return prev;
      
      // 最多保留5个错误
      const newErrors = [networkError, ...prev].slice(0, 5);
      return newErrors;
    });
  };

  const removeNetworkError = (errorId: string) => {
    setNetworkErrors(prev => prev.filter(err => err.id !== errorId));
  };

  const retryNetworkError = (errorId: string) => {
    setNetworkErrors(prev => prev.map(err => {
      if (err.id === errorId) {
        return {
          ...err,
          retryCount: err.retryCount + 1,
          canRetry: err.retryCount < 3, // 最多重试3次
        };
      }
      return err;
    }));

    // 这里可以触发重试逻辑
    setTimeout(() => {
      removeNetworkError(errorId);
    }, 2000);
  };

  const clearAllErrors = () => {
    setNetworkErrors([]);
  };

  return (
    <>
      {children}
      
      {/* 离线状态模态框 */}
      <Modal
        title={
          <Space>
            <CloseCircleOutlined style={{ color: '#ff4d4f' }} />
            网络连接已断开
          </Space>
        }
        open={showOfflineModal}
        footer={null}
        closable={false}
        centered
      >
        <Space direction="vertical" style={{ width: '100%' }}>
          <Text>
            您的网络连接已断开，请检查网络设置后重试。
          </Text>
          <Button 
            type="primary" 
            icon={<ReloadOutlined />}
            onClick={() => window.location.reload()}
            block
          >
            重新加载页面
          </Button>
        </Space>
      </Modal>

      {/* 网络错误提示 */}
      {networkErrors.length > 0 && (
        <div
          style={{
            position: 'fixed',
            top: '20px',
            right: '20px',
            zIndex: 9999,
            maxWidth: '400px',
          }}
        >
          <Space direction="vertical" style={{ width: '100%' }}>
            {networkErrors.map(error => (
              <Alert
                key={error.id}
                message="网络错误"
                description={
                  <Space direction="vertical" size="small">
                    <Text>{error.message}</Text>
                    <Text type="secondary" style={{ fontSize: '12px' }}>
                      {error.timestamp.toLocaleTimeString()}
                    </Text>
                  </Space>
                }
                type="error"
                showIcon
                closable
                onClose={() => removeNetworkError(error.id)}
                action={
                  <Space direction="vertical" size="small">
                    {error.canRetry && (
                      <Button
                        size="small"
                        type="primary"
                        ghost
                        icon={<ReloadOutlined />}
                        onClick={() => retryNetworkError(error.id)}
                      >
                        重试 ({3 - error.retryCount})
                      </Button>
                    )}
                  </Space>
                }
              />
            ))}
            
            {networkErrors.length > 1 && (
              <Button
                size="small"
                type="link"
                onClick={clearAllErrors}
                style={{ padding: 0 }}
              >
                清除所有错误
              </Button>
            )}
          </Space>
        </div>
      )}

      {/* 网络状态指示器 */}
      {!isOnline && (
        <div
          style={{
            position: 'fixed',
            bottom: '20px',
            left: '20px',
            zIndex: 9999,
          }}
        >
          <Alert
            message={
              <Space>
                <WifiOutlined />
                离线模式
              </Space>
            }
            description="网络连接已断开，部分功能可能无法使用"
            type="warning"
            showIcon={false}
            banner
          />
        </div>
      )}
    </>
  );
};

export default NetworkErrorHandler;
