{"name": "email-system-backend", "version": "1.0.0", "description": "邮箱系统后端API服务", "main": "dist/index.js", "scripts": {"start": "node dist/index.js", "dev": "nodemon src/index.ts", "build": "tsc", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["email", "api", "nodejs", "express", "typescript"], "author": "", "license": "ISC", "dependencies": {"bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "express-rate-limit": "^7.5.0", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "multer": "^2.0.1", "nodemailer": "^7.0.3", "uuid": "^11.1.0", "winston": "^3.17.0"}, "devDependencies": {"@types/bcryptjs": "^3.0.0", "@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/jsonwebtoken": "^9.0.9", "@types/multer": "^1.4.13", "@types/node": "^24.0.1", "@types/nodemailer": "^6.4.17", "@types/uuid": "^10.0.0", "nodemon": "^3.1.10", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}