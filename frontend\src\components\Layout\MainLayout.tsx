import React from "react";
import { Layout, Menu, Avatar, Dropdown, Button, Typography } from "antd";
import {
  MailOutlined,
  InboxOutlined,
  SendOutlined,
  EditOutlined,
  DeleteOutlined,
  StarOutlined,
  UserOutlined,
  SettingOutlined,
  LogoutOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
} from "@ant-design/icons";
import { useAuthStore } from "../../store/authStore";
import { useFolders } from "../../hooks/useFolders";
import { useUIStore } from "../../store/uiStore";
import { Outlet, useNavigate } from "react-router-dom";

const { Header, Sider, Content } = Layout;
const { Text } = Typography;

const MainLayout: React.FC = () => {
  const navigate = useNavigate();
  const { user, logout } = useAuthStore();

  // 使用新的hooks
  const { data: folders = [] } = useFolders();
  const { currentFolder, setCurrentFolder } = useUIStore();
  const [collapsed, setCollapsed] = React.useState(false);

  const handleLogout = () => {
    logout();
    navigate("/login");
  };

  const userMenuItems = [
    {
      key: "profile",
      icon: <UserOutlined />,
      label: "个人资料",
      onClick: () => navigate("/profile"),
    },
    {
      key: "settings",
      icon: <SettingOutlined />,
      label: "设置",
      onClick: () => navigate("/settings"),
    },
    {
      type: "divider" as const,
    },
    {
      key: "logout",
      icon: <LogoutOutlined />,
      label: "退出登录",
      onClick: handleLogout,
    },
  ];

  const getFolderIcon = (type: string) => {
    switch (type) {
      case "inbox":
        return <InboxOutlined />;
      case "sent":
        return <SendOutlined />;
      case "draft":
        return <EditOutlined />;
      case "trash":
        return <DeleteOutlined />;
      case "starred":
        return <StarOutlined />;
      default:
        return <MailOutlined />;
    }
  };

  const sidebarMenuItems = [
    {
      key: "compose",
      icon: <EditOutlined />,
      label: "写邮件",
      onClick: () => navigate("/compose"),
      className: "compose-button",
    },
    {
      type: "divider" as const,
    },
    ...folders.map((folder: any) => ({
      key: folder.id,
      icon: getFolderIcon(folder.type),
      label: (
        <div className="flex justify-between items-center">
          <span>{folder.name}</span>
          {folder.unreadCount > 0 && (
            <span className="bg-primary-500 text-white text-xs px-2 py-1 rounded-full">
              {folder.unreadCount}
            </span>
          )}
        </div>
      ),
      onClick: () => {
        setCurrentFolder(folder);
        navigate(`/folder/${folder.id}`);
      },
    })),
  ];

  return (
    <Layout className="min-h-screen">
      <Header className="bg-white shadow-sm border-b border-gray-200 px-4 flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            type="text"
            icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
            onClick={() => setCollapsed(!collapsed)}
            className="text-gray-600"
          />
          <div className="flex items-center space-x-2">
            <MailOutlined className="text-2xl text-primary-600" />
            <Text className="text-xl font-semibold text-gray-900">
              {import.meta.env.VITE_APP_NAME || "邮箱系统"}
            </Text>
          </div>
        </div>

        <div className="flex items-center space-x-4">
          <Dropdown menu={{ items: userMenuItems }} placement="bottomRight">
            <div className="flex items-center space-x-2 cursor-pointer hover:bg-gray-50 px-3 py-2 rounded-lg">
              <Avatar
                size="small"
                src={user?.avatarUrl}
                icon={<UserOutlined />}
                className="bg-primary-500"
              />
              <div className="hidden md:block">
                <Text className="text-sm font-medium text-gray-900">
                  {user?.displayName}
                </Text>
                <br />
                <Text className="text-xs text-gray-500">{user?.email}</Text>
              </div>
            </div>
          </Dropdown>
        </div>
      </Header>

      <Layout>
        <Sider
          trigger={null}
          collapsible
          collapsed={collapsed}
          width={280}
          className="bg-white shadow-sm border-r border-gray-200"
        >
          <div className="p-4">
            <Button
              type="primary"
              icon={<EditOutlined />}
              size="large"
              className="w-full mb-4"
              onClick={() => navigate("/compose")}
            >
              {!collapsed && "写邮件"}
            </Button>
          </div>

          <Menu
            mode="inline"
            selectedKeys={currentFolder ? [currentFolder.id] : []}
            className="border-none"
            items={folders.map((folder) => ({
              key: folder.id,
              icon: getFolderIcon(folder.type),
              label: !collapsed ? (
                <div className="flex justify-between items-center">
                  <span>{folder.name}</span>
                  {folder.unreadCount > 0 && (
                    <span className="bg-primary-500 text-white text-xs px-2 py-1 rounded-full">
                      {folder.unreadCount}
                    </span>
                  )}
                </div>
              ) : null,
              onClick: () => {
                setCurrentFolder(folder);
                navigate(`/folder/${folder.id}`);
              },
            }))}
          />
        </Sider>

        <Layout>
          <Content className="bg-gray-50">
            <Outlet />
          </Content>
        </Layout>
      </Layout>
    </Layout>
  );
};

export default MainLayout;
