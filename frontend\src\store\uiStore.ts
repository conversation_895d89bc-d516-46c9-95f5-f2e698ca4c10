import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import type { Email, Folder, SearchParams } from '../types';

// 简化的UI状态管理，主要负责UI交互状态
interface UIState {
  // 当前选中的项目
  currentFolder: Folder | null;
  currentEmail: Email | null;
  
  // 选择状态
  selectedEmails: string[];
  
  // UI状态
  isComposerOpen: boolean;
  composerMode: 'new' | 'reply' | 'forward' | null;
  replyToEmail: Email | null;
  
  // 搜索状态
  searchParams: SearchParams | null;
  isSearchMode: boolean;
  
  // 视图状态
  viewMode: 'list' | 'grid';
  sortBy: 'date' | 'sender' | 'subject';
  sortOrder: 'asc' | 'desc';
  
  // 侧边栏状态
  isSidebarCollapsed: boolean;
  
  // Actions
  setCurrentFolder: (folder: Folder | null) => void;
  setCurrentEmail: (email: Email | null) => void;
  selectEmail: (id: string) => void;
  unselectEmail: (id: string) => void;
  selectAllEmails: (emailIds: string[]) => void;
  clearSelection: () => void;
  toggleEmailSelection: (id: string) => void;
  
  // 编写邮件相关
  openComposer: (mode?: 'new' | 'reply' | 'forward', email?: Email) => void;
  closeComposer: () => void;
  
  // 搜索相关
  setSearchParams: (params: SearchParams | null) => void;
  enterSearchMode: () => void;
  exitSearchMode: () => void;
  
  // 视图相关
  setViewMode: (mode: 'list' | 'grid') => void;
  setSortBy: (sortBy: 'date' | 'sender' | 'subject') => void;
  setSortOrder: (order: 'asc' | 'desc') => void;
  toggleSortOrder: () => void;
  
  // 侧边栏
  toggleSidebar: () => void;
  setSidebarCollapsed: (collapsed: boolean) => void;
}

export const useUIStore = create<UIState>()(
  persist(
    (set, get) => ({
      // 初始状态
      currentFolder: null,
      currentEmail: null,
      selectedEmails: [],
      isComposerOpen: false,
      composerMode: null,
      replyToEmail: null,
      searchParams: null,
      isSearchMode: false,
      viewMode: 'list',
      sortBy: 'date',
      sortOrder: 'desc',
      isSidebarCollapsed: false,

      // 基础操作
      setCurrentFolder: (folder) => {
        set({ 
          currentFolder: folder,
          searchParams: null,
          isSearchMode: false,
        });
      },

      setCurrentEmail: (email) => {
        set({ currentEmail: email });
      },

      // 选择操作
      selectEmail: (id) => {
        const state = get();
        if (!state.selectedEmails.includes(id)) {
          set({ selectedEmails: [...state.selectedEmails, id] });
        }
      },

      unselectEmail: (id) => {
        const state = get();
        set({ selectedEmails: state.selectedEmails.filter(emailId => emailId !== id) });
      },

      selectAllEmails: (emailIds) => {
        set({ selectedEmails: emailIds });
      },

      clearSelection: () => {
        set({ selectedEmails: [] });
      },

      toggleEmailSelection: (id) => {
        const state = get();
        if (state.selectedEmails.includes(id)) {
          state.unselectEmail(id);
        } else {
          state.selectEmail(id);
        }
      },

      // 编写邮件
      openComposer: (mode = 'new', email) => {
        set({
          isComposerOpen: true,
          composerMode: mode,
          replyToEmail: email || null,
        });
      },

      closeComposer: () => {
        set({
          isComposerOpen: false,
          composerMode: null,
          replyToEmail: null,
        });
      },

      // 搜索
      setSearchParams: (params) => {
        set({ 
          searchParams: params,
          isSearchMode: !!params,
        });
      },

      enterSearchMode: () => {
        set({ isSearchMode: true });
      },

      exitSearchMode: () => {
        set({ 
          isSearchMode: false,
          searchParams: null,
        });
      },

      // 视图设置
      setViewMode: (mode) => {
        set({ viewMode: mode });
      },

      setSortBy: (sortBy) => {
        set({ sortBy });
      },

      setSortOrder: (order) => {
        set({ sortOrder: order });
      },

      toggleSortOrder: () => {
        const state = get();
        set({ sortOrder: state.sortOrder === 'asc' ? 'desc' : 'asc' });
      },

      // 侧边栏
      toggleSidebar: () => {
        const state = get();
        set({ isSidebarCollapsed: !state.isSidebarCollapsed });
      },

      setSidebarCollapsed: (collapsed) => {
        set({ isSidebarCollapsed: collapsed });
      },
    }),
    {
      name: 'email-ui-storage',
      partialize: (state) => ({
        viewMode: state.viewMode,
        sortBy: state.sortBy,
        sortOrder: state.sortOrder,
        isSidebarCollapsed: state.isSidebarCollapsed,
      }),
    }
  )
);
