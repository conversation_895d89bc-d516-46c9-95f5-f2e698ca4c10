# 邮件系统部署脚本
param(
    [string]$Action = "start",
    [string]$Profile = "basic",
    [switch]$Build = $false,
    [switch]$Logs = $false
)

Write-Host "📧 邮件系统部署工具" -ForegroundColor Blue
Write-Host "===================" -ForegroundColor Blue

# 检查Docker环境
function Test-Docker {
    try {
        docker version | Out-Null
        docker-compose version | Out-Null
        Write-Host "✅ Docker 环境检查通过" -ForegroundColor Green
        return $true
    } catch {
        Write-Host "❌ Docker 或 Docker Compose 未安装" -ForegroundColor Red
        return $false
    }
}

# 生成SSL证书
function New-SSLCertificates {
    Write-Host "🔐 生成SSL证书..." -ForegroundColor Yellow
    
    if (-not (Test-Path "ssl")) {
        New-Item -ItemType Directory -Path "ssl" | Out-Null
    }
    
    if (-not (Test-Path "ssl/mail.crt")) {
        # 生成自签名证书（生产环境请使用真实证书）
        $certScript = @"
openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
    -keyout ssl/mail.key \
    -out ssl/mail.crt \
    -subj "/C=CN/ST=Beijing/L=Beijing/O=Email System/CN=mail.yourdomain.com"
"@
        
        try {
            Invoke-Expression $certScript
            Write-Host "✅ SSL证书生成完成" -ForegroundColor Green
        } catch {
            Write-Host "⚠️  SSL证书生成失败，将使用默认配置" -ForegroundColor Yellow
        }
    } else {
        Write-Host "✅ SSL证书已存在" -ForegroundColor Green
    }
}

# 创建必要的目录
function New-Directories {
    Write-Host "📁 创建必要目录..." -ForegroundColor Yellow
    
    $directories = @(
        "ssl",
        "logs",
        "uploads",
        "mail-data"
    )
    
    foreach ($dir in $directories) {
        if (-not (Test-Path $dir)) {
            New-Item -ItemType Directory -Path $dir | Out-Null
            Write-Host "  ✓ 创建目录: $dir" -ForegroundColor Gray
        }
    }
    
    Write-Host "✅ 目录创建完成" -ForegroundColor Green
}

# 启动邮件系统
function Start-MailSystem {
    Write-Host "🚀 启动邮件系统..." -ForegroundColor Cyan
    
    $composeFiles = @("-f", "docker-compose.mail.yml")
    
    # 根据配置文件选择服务
    switch ($Profile) {
        "basic" {
            $services = @("mysql", "postfix", "dovecot", "backend", "frontend", "redis")
        }
        "full" {
            $composeFiles += @("--profile", "webmail", "--profile", "filtering")
            $services = @()  # 启动所有服务
        }
        "testing" {
            $composeFiles += @("--profile", "testing")
            $services = @("mysql", "backend", "frontend", "mailhog")
        }
        default {
            $services = @("mysql", "postfix", "dovecot", "backend", "frontend")
        }
    }
    
    try {
        if ($Build) {
            Write-Host "🏗️  构建镜像..." -ForegroundColor Yellow
            docker-compose @composeFiles build
        }
        
        if ($services.Count -gt 0) {
            docker-compose @composeFiles up -d @services
        } else {
            docker-compose @composeFiles up -d
        }
        
        Write-Host "✅ 邮件系统启动完成" -ForegroundColor Green
        Show-ServiceStatus
        
    } catch {
        Write-Host "❌ 邮件系统启动失败: $_" -ForegroundColor Red
        exit 1
    }
}

# 停止邮件系统
function Stop-MailSystem {
    Write-Host "🛑 停止邮件系统..." -ForegroundColor Yellow
    
    try {
        docker-compose -f docker-compose.mail.yml down
        Write-Host "✅ 邮件系统已停止" -ForegroundColor Green
    } catch {
        Write-Host "❌ 停止邮件系统失败: $_" -ForegroundColor Red
    }
}

# 重启邮件系统
function Restart-MailSystem {
    Write-Host "🔄 重启邮件系统..." -ForegroundColor Yellow
    Stop-MailSystem
    Start-Sleep -Seconds 5
    Start-MailSystem
}

# 显示服务状态
function Show-ServiceStatus {
    Write-Host "`n📊 服务状态:" -ForegroundColor Magenta
    docker-compose -f docker-compose.mail.yml ps
    
    Write-Host "`n🌐 访问地址:" -ForegroundColor Cyan
    Write-Host "  前端应用: http://localhost" -ForegroundColor Gray
    Write-Host "  后端API: http://localhost:5000" -ForegroundColor Gray
    Write-Host "  Roundcube: http://localhost:8080 (如果启用)" -ForegroundColor Gray
    Write-Host "  MailHog: http://localhost:8025 (测试模式)" -ForegroundColor Gray
    
    Write-Host "`n📧 邮件服务端口:" -ForegroundColor Cyan
    Write-Host "  SMTP: 25, 587 (提交), 465 (SMTPS)" -ForegroundColor Gray
    Write-Host "  IMAP: 143, 993 (IMAPS)" -ForegroundColor Gray
    Write-Host "  POP3: 110, 995 (POP3S)" -ForegroundColor Gray
}

# 显示日志
function Show-Logs {
    Write-Host "📋 显示系统日志..." -ForegroundColor Yellow
    docker-compose -f docker-compose.mail.yml logs -f
}

# 清理系统
function Clear-MailSystem {
    Write-Host "🧹 清理邮件系统..." -ForegroundColor Yellow
    
    $confirm = Read-Host "确认删除所有数据? (y/N)"
    if ($confirm -eq "y" -or $confirm -eq "Y") {
        docker-compose -f docker-compose.mail.yml down -v --remove-orphans
        docker system prune -f
        Write-Host "✅ 系统清理完成" -ForegroundColor Green
    } else {
        Write-Host "❌ 清理操作已取消" -ForegroundColor Yellow
    }
}

# 创建邮箱用户
function New-MailUser {
    $username = Read-Host "输入用户名"
    $email = Read-Host "输入邮箱地址"
    $password = Read-Host "输入密码" -AsSecureString
    $plainPassword = [Runtime.InteropServices.Marshal]::PtrToStringAuto([Runtime.InteropServices.Marshal]::SecureStringToBSTR($password))
    
    $sql = @"
CALL CreateUserMailbox('$username', '$email', '$plainPassword', '$username', 'yourdomain.com', 1073741824);
"@
    
    try {
        docker-compose -f docker-compose.mail.yml exec mysql mysql -u root -proot_password email_system -e "$sql"
        Write-Host "✅ 用户创建成功: $email" -ForegroundColor Green
    } catch {
        Write-Host "❌ 用户创建失败: $_" -ForegroundColor Red
    }
}

# 主函数
function Main {
    if (-not (Test-Docker)) {
        Write-Host "请先安装 Docker 和 Docker Compose" -ForegroundColor Red
        exit 1
    }
    
    switch ($Action.ToLower()) {
        "start" {
            New-Directories
            New-SSLCertificates
            Start-MailSystem
        }
        "stop" {
            Stop-MailSystem
        }
        "restart" {
            Restart-MailSystem
        }
        "status" {
            Show-ServiceStatus
        }
        "logs" {
            Show-Logs
        }
        "clean" {
            Clear-MailSystem
        }
        "user" {
            New-MailUser
        }
        default {
            Write-Host "用法: .\deploy-mail-system.ps1 -Action <start|stop|restart|status|logs|clean|user>" -ForegroundColor Yellow
            Write-Host "选项:" -ForegroundColor Yellow
            Write-Host "  -Profile <basic|full|testing>  选择部署配置" -ForegroundColor Gray
            Write-Host "  -Build                         重新构建镜像" -ForegroundColor Gray
            Write-Host "  -Logs                          显示日志" -ForegroundColor Gray
        }
    }
}

# 执行主函数
Main
