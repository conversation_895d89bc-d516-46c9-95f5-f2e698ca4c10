import { QueryClient } from '@tanstack/react-query';
import { queryKeys, getRelatedQueryKeys } from './queryKeys';
import { showErrorMessage } from '../utils/errorHandler';
import { PERFORMANCE_CONFIG, getPerformanceConfig, getRetryDelay } from '../config/performance';
import { performanceMonitor } from '../utils/performance';

// 创建增强的QueryClient实例
export const createQueryClient = () => {
  const perfConfig = getPerformanceConfig();

  return new QueryClient({
    defaultOptions: {
      queries: {
        retry: (failureCount, error: any) => {
          // 对于认证错误不重试
          if (error?.response?.status === 401 || error?.response?.status === 403) {
            return false;
          }
          // 使用性能配置的重试次数
          return failureCount < perfConfig.QUERY.RETRY.DEFAULT;
        },
        retryDelay: getRetryDelay,
        refetchOnWindowFocus: false,
        refetchOnReconnect: true,
        staleTime: perfConfig.QUERY.STALE_TIME.MEDIUM,
        gcTime: perfConfig.QUERY.CACHE_TIME.MEDIUM,
        onError: (error: any) => {
          performanceMonitor.startMeasure('query-error-handling');
          showErrorMessage(error, '数据获取失败');
          performanceMonitor.endMeasure('query-error-handling');
        },
      },
      mutations: {
        retry: (failureCount, error: any) => {
          // 对于客户端错误不重试
          if (error?.response?.status >= 400 && error?.response?.status < 500) {
            return false;
          }
          // 使用性能配置的重试次数
          return failureCount < perfConfig.QUERY.RETRY.DEFAULT;
        },
        retryDelay: getRetryDelay,
        onError: (error: any) => {
          performanceMonitor.startMeasure('mutation-error-handling');
          showErrorMessage(error, '操作失败');
          performanceMonitor.endMeasure('mutation-error-handling');
        },
      },
    },
  });
};

// 缓存管理工具类
export class CacheManager {
  constructor(private queryClient: QueryClient) {}

  // 失效邮件相关缓存
  invalidateEmailCaches(emailId?: string, folderId?: string) {
    const keys = getRelatedQueryKeys.onEmailChange(emailId, folderId);
    keys.forEach(key => {
      this.queryClient.invalidateQueries({ queryKey: key });
    });
  }

  // 失效文件夹相关缓存
  invalidateFolderCaches(folderId?: string) {
    const keys = getRelatedQueryKeys.onFolderChange(folderId);
    keys.forEach(key => {
      this.queryClient.invalidateQueries({ queryKey: key });
    });
  }

  // 失效认证相关缓存
  invalidateAuthCaches() {
    const keys = getRelatedQueryKeys.onAuthChange();
    keys.forEach(key => {
      this.queryClient.invalidateQueries({ queryKey: key });
    });
  }

  // 预取邮件数据
  prefetchEmail(emailId: string) {
    return this.queryClient.prefetchQuery({
      queryKey: queryKeys.emails.detail(emailId),
      queryFn: async () => {
        const { apiService } = await import('../services/api');
        const response = await apiService.getEmail(emailId);
        return response.data.data;
      },
      staleTime: 5 * 60 * 1000,
    });
  }

  // 预取文件夹数据
  prefetchFolder(folderId: string) {
    return this.queryClient.prefetchQuery({
      queryKey: queryKeys.folders.detail(folderId),
      queryFn: async () => {
        const { apiService } = await import('../services/api');
        const response = await apiService.getFolder(folderId);
        return response.data.data;
      },
      staleTime: 15 * 60 * 1000,
    });
  }

  // 预取邮件列表
  prefetchEmails(params: { folderId?: string; page?: number; limit?: number }) {
    return this.queryClient.prefetchQuery({
      queryKey: queryKeys.emails.list(params),
      queryFn: async () => {
        const { apiService } = await import('../services/api');
        const response = await apiService.getEmails(params);
        return response.data.data;
      },
      staleTime: 2 * 60 * 1000,
    });
  }

  // 乐观更新邮件状态
  optimisticUpdateEmail(emailId: string, updates: any) {
    // 更新邮件详情
    this.queryClient.setQueryData(
      queryKeys.emails.detail(emailId),
      (oldData: any) => {
        if (!oldData) return oldData;
        return { ...oldData, ...updates };
      }
    );

    // 更新邮件列表中的邮件
    this.queryClient.setQueriesData(
      { queryKey: queryKeys.emails.lists() },
      (oldData: any) => {
        if (!oldData) return oldData;
        
        if (oldData.pages) {
          // 无限查询数据结构
          return {
            ...oldData,
            pages: oldData.pages.map((page: any) => ({
              ...page,
              data: page.data.map((email: any) =>
                email.id === emailId ? { ...email, ...updates } : email
              ),
            })),
          };
        } else {
          // 普通查询数据结构
          return {
            ...oldData,
            data: oldData.data.map((email: any) =>
              email.id === emailId ? { ...email, ...updates } : email
            ),
          };
        }
      }
    );
  }

  // 乐观删除邮件
  optimisticDeleteEmail(emailId: string) {
    // 移除邮件详情
    this.queryClient.removeQueries({ queryKey: queryKeys.emails.detail(emailId) });

    // 从邮件列表中移除
    this.queryClient.setQueriesData(
      { queryKey: queryKeys.emails.lists() },
      (oldData: any) => {
        if (!oldData) return oldData;
        
        if (oldData.pages) {
          // 无限查询数据结构
          return {
            ...oldData,
            pages: oldData.pages.map((page: any) => ({
              ...page,
              data: page.data.filter((email: any) => email.id !== emailId),
            })),
          };
        } else {
          // 普通查询数据结构
          return {
            ...oldData,
            data: oldData.data.filter((email: any) => email.id !== emailId),
          };
        }
      }
    );
  }

  // 乐观添加邮件
  optimisticAddEmail(email: any, folderId?: string) {
    // 添加到相应文件夹的邮件列表
    this.queryClient.setQueriesData(
      { queryKey: queryKeys.emails.lists() },
      (oldData: any) => {
        if (!oldData) return oldData;
        
        // 只更新匹配的文件夹列表
        const shouldUpdate = !folderId || 
          (oldData.queryKey && oldData.queryKey.includes(folderId));
        
        if (!shouldUpdate) return oldData;
        
        if (oldData.pages) {
          // 无限查询数据结构 - 添加到第一页
          const newPages = [...oldData.pages];
          if (newPages[0]) {
            newPages[0] = {
              ...newPages[0],
              data: [email, ...newPages[0].data],
            };
          }
          return { ...oldData, pages: newPages };
        } else {
          // 普通查询数据结构
          return {
            ...oldData,
            data: [email, ...oldData.data],
          };
        }
      }
    );
  }

  // 清除所有缓存
  clearAllCaches() {
    this.queryClient.clear();
  }

  // 清除特定类型的缓存
  clearCachesByType(type: 'emails' | 'folders' | 'auth' | 'stats') {
    switch (type) {
      case 'emails':
        this.queryClient.removeQueries({ queryKey: queryKeys.emails.all });
        break;
      case 'folders':
        this.queryClient.removeQueries({ queryKey: queryKeys.folders.all });
        break;
      case 'auth':
        this.queryClient.removeQueries({ queryKey: queryKeys.auth.all });
        break;
      case 'stats':
        this.queryClient.removeQueries({ queryKey: queryKeys.stats.all });
        break;
    }
  }

  // 获取缓存统计信息
  getCacheStats() {
    const cache = this.queryClient.getQueryCache();
    const queries = cache.getAll();
    
    const stats = {
      totalQueries: queries.length,
      activeQueries: queries.filter(q => q.getObserversCount() > 0).length,
      staleQueries: queries.filter(q => q.isStale()).length,
      errorQueries: queries.filter(q => q.state.status === 'error').length,
      loadingQueries: queries.filter(q => q.state.status === 'pending').length,
    };

    return stats;
  }

  // 手动垃圾回收
  garbageCollect() {
    this.queryClient.getQueryCache().clear();
    this.queryClient.getMutationCache().clear();
  }
}

// 导出默认实例
export const queryClient = createQueryClient();
export const cacheManager = new CacheManager(queryClient);
