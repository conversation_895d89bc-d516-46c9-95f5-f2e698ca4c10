apiVersion: v1
kind: ConfigMap
metadata:
  name: email-config
  namespace: email-system
data:
  # 前端配置
  VITE_API_BASE_URL: "https://api.yourdomain.com/api"
  VITE_WS_URL: "wss://api.yourdomain.com"
  VITE_APP_NAME: "邮箱系统"
  VITE_MAX_FILE_SIZE: "10485760"
  
  # 后端配置
  NODE_ENV: "production"
  PORT: "5000"
  LOG_LEVEL: "info"
  LOG_FORMAT: "json"
  
  # 数据库配置
  DATABASE_URL: "******************************************************/emaildb"
  REDIS_URL: "redis://redis-service:6379"
  
  # 邮件服务配置
  SMTP_HOST: "smtp.yourdomain.com"
  SMTP_PORT: "587"
  IMAP_HOST: "imap.yourdomain.com"
  IMAP_PORT: "993"
  
  # 文件上传配置
  MAX_FILE_SIZE: "10485760"
  UPLOAD_PATH: "/app/uploads"
  
  # 安全配置
  CORS_ORIGIN: "https://yourdomain.com,https://www.yourdomain.com"
  RATE_LIMIT_WINDOW: "15"
  RATE_LIMIT_MAX: "100"
  BCRYPT_ROUNDS: "12"
  
  # 监控配置
  ENABLE_METRICS: "true"
  METRICS_PORT: "9090"
  HEALTH_CHECK_PATH: "/health"

---
apiVersion: v1
kind: Secret
metadata:
  name: email-secrets
  namespace: email-system
type: Opaque
data:
  # Base64编码的敏感信息
  JWT_SECRET: eW91ci1zdXBlci1zZWN1cmUtcHJvZHVjdGlvbi1qd3Qtc2VjcmV0LWtleS1oZXJl
  SMTP_USER: ********************************
  SMTP_PASS: eW91ci1zbXRwLXBhc3N3b3Jk
  IMAP_USER: ****************************
  IMAP_PASS: eW91ci1pbWFwLXBhc3N3b3Jk
  POSTGRES_PASSWORD: ZW1haWxwYXNz
  REDIS_PASSWORD: cmVkaXNwYXNz
