-- MySQL 邮件系统用户初始化脚本

-- 创建 Postfix 用户
CREATE USER IF NOT EXISTS 'postfix'@'%' IDENTIFIED BY 'postfix_password';
GRANT SELECT ON email_system.mail_domains TO 'postfix'@'%';
GRANT SELECT ON email_system.mail_accounts TO 'postfix'@'%';
GRANT SELECT ON email_system.mail_aliases TO 'postfix'@'%';
GRANT SELECT ON email_system.mail_transports TO 'postfix'@'%';

-- 创建 Dovecot 用户
CREATE USER IF NOT EXISTS 'dovecot'@'%' IDENTIFIED BY 'dovecot_password';
GRANT SELECT ON email_system.mail_accounts TO 'dovecot'@'%';
GRANT SELECT ON email_system.mail_domains TO 'dovecot'@'%';
GRANT UPDATE ON email_system.mail_accounts TO 'dovecot'@'%';

-- 创建应用用户
CREATE USER IF NOT EXISTS 'emailapp'@'%' IDENTIFIED BY 'emailapp_password';
GRANT ALL PRIVILEGES ON email_system.* TO 'emailapp'@'%';

-- 创建 Roundcube 数据库和用户
CREATE DATABASE IF NOT EXISTS roundcube DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER IF NOT EXISTS 'roundcube'@'%' IDENTIFIED BY 'roundcube_password';
GRANT ALL PRIVILEGES ON roundcube.* TO 'roundcube'@'%';

-- 刷新权限
FLUSH PRIVILEGES;

-- 插入测试数据
USE email_system;

-- 插入测试域名
INSERT IGNORE INTO mail_domains (domain, description, status) VALUES
('yourdomain.com', '主域名', 'active'),
('test.com', '测试域名', 'active');

-- 获取域名ID
SET @domain_id = (SELECT id FROM mail_domains WHERE domain = 'yourdomain.com');

-- 插入测试用户
INSERT IGNORE INTO users (username, email, password_hash, display_name, status, email_verified) VALUES
('admin', '<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj3bp.Gm.F5e', '系统管理员', 'active', 1),
('user1', '<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj3bp.Gm.F5e', '测试用户1', 'active', 1),
('user2', '<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj3bp.Gm.F5e', '测试用户2', 'active', 1);

-- 获取用户ID
SET @admin_id = (SELECT id FROM users WHERE username = 'admin');
SET @user1_id = (SELECT id FROM users WHERE username = 'user1');
SET @user2_id = (SELECT id FROM users WHERE username = 'user2');

-- 创建邮箱账户
INSERT IGNORE INTO mail_accounts (user_id, domain_id, email, password, quota, status) VALUES
(@admin_id, @domain_id, '<EMAIL>', '{PLAIN}admin123', **********, 'active'),
(@user1_id, @domain_id, '<EMAIL>', '{PLAIN}user123', **********, 'active'),
(@user2_id, @domain_id, '<EMAIL>', '{PLAIN}user123', **********, 'active');

-- 创建默认文件夹
INSERT IGNORE INTO mail_folders (user_id, name, display_name, type, sort_order) VALUES
-- 管理员文件夹
(@admin_id, 'INBOX', '收件箱', 'inbox', 1),
(@admin_id, 'Sent', '已发送', 'sent', 2),
(@admin_id, 'Drafts', '草稿箱', 'drafts', 3),
(@admin_id, 'Trash', '垃圾箱', 'trash', 4),
(@admin_id, 'Spam', '垃圾邮件', 'spam', 5),
-- 用户1文件夹
(@user1_id, 'INBOX', '收件箱', 'inbox', 1),
(@user1_id, 'Sent', '已发送', 'sent', 2),
(@user1_id, 'Drafts', '草稿箱', 'drafts', 3),
(@user1_id, 'Trash', '垃圾箱', 'trash', 4),
(@user1_id, 'Spam', '垃圾邮件', 'spam', 5),
-- 用户2文件夹
(@user2_id, 'INBOX', '收件箱', 'inbox', 1),
(@user2_id, 'Sent', '已发送', 'sent', 2),
(@user2_id, 'Drafts', '草稿箱', 'drafts', 3),
(@user2_id, 'Trash', '垃圾箱', 'trash', 4),
(@user2_id, 'Spam', '垃圾邮件', 'spam', 5);

-- 插入测试邮件别名
INSERT IGNORE INTO mail_aliases (domain_id, source, destination, status) VALUES
(@domain_id, '<EMAIL>', '<EMAIL>', 'active'),
(@domain_id, '<EMAIL>', '<EMAIL>', 'active'),
(@domain_id, '<EMAIL>', '<EMAIL>', 'active');

-- 插入示例邮件
INSERT IGNORE INTO emails (
    message_id, subject, from_address, from_name, to_addresses, 
    date_sent, date_received, body_text, body_html, priority
) VALUES
(
    '<EMAIL>',
    '欢迎使用邮件系统',
    '<EMAIL>',
    '系统管理员',
    '[{"address":"<EMAIL>","name":"测试用户1"}]',
    NOW(),
    NOW(),
    '欢迎使用我们的邮件系统！这是一个功能完整的邮件解决方案。',
    '<h1>欢迎使用我们的邮件系统！</h1><p>这是一个功能完整的邮件解决方案。</p>',
    'normal'
),
(
    '<EMAIL>',
    '系统维护通知',
    '<EMAIL>',
    '系统管理员',
    '[{"address":"<EMAIL>","name":"测试用户1"},{"address":"<EMAIL>","name":"测试用户2"}]',
    NOW() - INTERVAL 1 DAY,
    NOW() - INTERVAL 1 DAY,
    '系统将在今晚进行维护，预计维护时间2小时。',
    '<h2>系统维护通知</h2><p>系统将在今晚进行维护，预计维护时间2小时。</p>',
    'high'
);

-- 获取邮件ID和文件夹ID
SET @email1_id = (SELECT id FROM emails WHERE message_id = '<EMAIL>');
SET @email2_id = (SELECT id FROM emails WHERE message_id = '<EMAIL>');
SET @user1_inbox = (SELECT id FROM mail_folders WHERE user_id = @user1_id AND type = 'inbox');
SET @user2_inbox = (SELECT id FROM mail_folders WHERE user_id = @user2_id AND type = 'inbox');

-- 关联邮件到用户
INSERT IGNORE INTO user_emails (user_id, email_id, folder_id, is_read, is_starred) VALUES
(@user1_id, @email1_id, @user1_inbox, 0, 0),
(@user1_id, @email2_id, @user1_inbox, 1, 1),
(@user2_id, @email2_id, @user2_inbox, 0, 0);

-- 更新文件夹统计
UPDATE mail_folders SET 
    email_count = (SELECT COUNT(*) FROM user_emails WHERE folder_id = mail_folders.id AND is_deleted = 0),
    unread_count = (SELECT COUNT(*) FROM user_emails WHERE folder_id = mail_folders.id AND is_read = 0 AND is_deleted = 0);

-- 显示创建结果
SELECT 'Database initialization completed' as status;
SELECT COUNT(*) as user_count FROM users;
SELECT COUNT(*) as domain_count FROM mail_domains;
SELECT COUNT(*) as account_count FROM mail_accounts;
SELECT COUNT(*) as folder_count FROM mail_folders;
SELECT COUNT(*) as email_count FROM emails;
