// 用户相关类型
export interface User {
  id: string;
  email: string;
  username: string;
  displayName: string;
  avatarUrl?: string;
  passwordHash: string;
  createdAt: Date;
  updatedAt: Date;
  isActive: boolean;
}

// 用户创建请求
export interface CreateUserRequest {
  email: string;
  username: string;
  displayName: string;
  password: string;
}

// 登录请求
export interface LoginRequest {
  email: string;
  password: string;
}

// 邮件相关类型
export interface Email {
  id: string;
  messageId: string;
  userId: string;
  folderId: string;
  subject: string;
  senderEmail: string;
  senderName: string;
  recipients: string[];
  ccRecipients?: string[];
  bccRecipients?: string[];
  contentText: string;
  contentHtml: string;
  attachments: Attachment[];
  isRead: boolean;
  isStarred: boolean;
  isDeleted: boolean;
  receivedAt?: Date;
  sentAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}

// 附件类型
export interface Attachment {
  id: string;
  filename: string;
  contentType: string;
  size: number;
  url: string;
}

// 文件夹类型
export interface Folder {
  id: string;
  userId: string;
  name: string;
  type: 'inbox' | 'sent' | 'draft' | 'trash' | 'custom';
  parentId?: string;
  emailCount: number;
  unreadCount: number;
  createdAt: Date;
  updatedAt: Date;
}

// 联系人类型
export interface Contact {
  id: string;
  userId: string;
  name: string;
  email: string;
  phone?: string;
  company?: string;
  notes?: string;
  createdAt: Date;
  updatedAt: Date;
}

// API响应类型
export interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
  error?: string;
}

// 分页响应类型
export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  hasNext: boolean;
  hasPrev: boolean;
}

// JWT载荷类型
export interface JwtPayload {
  userId: string;
  email: string;
  username: string;
  iat?: number;
  exp?: number;
}

// 邮件发送请求
export interface SendEmailRequest {
  to: string[];
  cc?: string[];
  bcc?: string[];
  subject: string;
  content: string;
  attachments?: Express.Multer.File[];
}

// 搜索参数
export interface SearchParams {
  query?: string;
  folder?: string;
  from?: string;
  to?: string;
  subject?: string;
  dateFrom?: string;
  dateTo?: string;
  hasAttachment?: boolean;
  isRead?: boolean;
  isStarred?: boolean;
}

// Express扩展类型
declare global {
  namespace Express {
    interface Request {
      user?: User;
    }
  }
}

// 错误类型枚举
export enum ErrorType {
  VALIDATION = 'VALIDATION',
  AUTHENTICATION = 'AUTHENTICATION',
  AUTHORIZATION = 'AUTHORIZATION',
  NOT_FOUND = 'NOT_FOUND',
  CONFLICT = 'CONFLICT',
  RATE_LIMIT = 'RATE_LIMIT',
  SERVER_ERROR = 'SERVER_ERROR',
  DATABASE_ERROR = 'DATABASE_ERROR',
  NETWORK_ERROR = 'NETWORK_ERROR',
}

// 错误代码枚举
export enum ErrorCode {
  // 验证错误
  INVALID_INPUT = 'INVALID_INPUT',
  MISSING_REQUIRED_FIELD = 'MISSING_REQUIRED_FIELD',
  INVALID_EMAIL_FORMAT = 'INVALID_EMAIL_FORMAT',
  INVALID_PASSWORD_FORMAT = 'INVALID_PASSWORD_FORMAT',

  // 认证错误
  INVALID_CREDENTIALS = 'INVALID_CREDENTIALS',
  TOKEN_EXPIRED = 'TOKEN_EXPIRED',
  TOKEN_INVALID = 'TOKEN_INVALID',
  TOKEN_MISSING = 'TOKEN_MISSING',

  // 授权错误
  INSUFFICIENT_PERMISSIONS = 'INSUFFICIENT_PERMISSIONS',
  ACCOUNT_DISABLED = 'ACCOUNT_DISABLED',

  // 资源错误
  RESOURCE_NOT_FOUND = 'RESOURCE_NOT_FOUND',
  RESOURCE_ALREADY_EXISTS = 'RESOURCE_ALREADY_EXISTS',

  // 服务器错误
  INTERNAL_SERVER_ERROR = 'INTERNAL_SERVER_ERROR',
  DATABASE_CONNECTION_ERROR = 'DATABASE_CONNECTION_ERROR',
  EXTERNAL_SERVICE_ERROR = 'EXTERNAL_SERVICE_ERROR',
}

// 增强的错误类
export class AppError extends Error {
  public statusCode: number;
  public isOperational: boolean;
  public errorType: ErrorType;
  public errorCode: ErrorCode;
  public details?: any;
  public timestamp: Date;

  constructor(
    message: string,
    statusCode: number,
    errorType: ErrorType = ErrorType.SERVER_ERROR,
    errorCode: ErrorCode = ErrorCode.INTERNAL_SERVER_ERROR,
    details?: any
  ) {
    super(message);
    this.statusCode = statusCode;
    this.isOperational = true;
    this.errorType = errorType;
    this.errorCode = errorCode;
    this.details = details;
    this.timestamp = new Date();

    Error.captureStackTrace(this, this.constructor);
  }
}

// 预定义错误类
export class ValidationError extends AppError {
  constructor(message: string, details?: any) {
    super(message, 400, ErrorType.VALIDATION, ErrorCode.INVALID_INPUT, details);
  }
}

export class AuthenticationError extends AppError {
  constructor(message: string, errorCode: ErrorCode = ErrorCode.INVALID_CREDENTIALS) {
    super(message, 401, ErrorType.AUTHENTICATION, errorCode);
  }
}

export class AuthorizationError extends AppError {
  constructor(message: string, errorCode: ErrorCode = ErrorCode.INSUFFICIENT_PERMISSIONS) {
    super(message, 403, ErrorType.AUTHORIZATION, errorCode);
  }
}

export class NotFoundError extends AppError {
  constructor(message: string) {
    super(message, 404, ErrorType.NOT_FOUND, ErrorCode.RESOURCE_NOT_FOUND);
  }
}

export class ConflictError extends AppError {
  constructor(message: string) {
    super(message, 409, ErrorType.CONFLICT, ErrorCode.RESOURCE_ALREADY_EXISTS);
  }
}

export class RateLimitError extends AppError {
  constructor(message: string = '请求过于频繁，请稍后再试') {
    super(message, 429, ErrorType.RATE_LIMIT, ErrorCode.INVALID_INPUT);
  }
}

// 数据库存储接口（内存存储）
export interface MemoryStore {
  users: Map<string, User>;
  emails: Map<string, Email>;
  folders: Map<string, Folder>;
  contacts: Map<string, Contact>;
}

// 邮件服务配置
export interface EmailServiceConfig {
  smtp: {
    host: string;
    port: number;
    user: string;
    pass: string;
  };
  imap: {
    host: string;
    port: number;
    user: string;
    pass: string;
  };
}
