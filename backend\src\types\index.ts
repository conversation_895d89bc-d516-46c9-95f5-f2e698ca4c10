// 用户相关类型
export interface User {
  id: string;
  email: string;
  username: string;
  displayName: string;
  avatarUrl?: string;
  passwordHash: string;
  createdAt: Date;
  updatedAt: Date;
  isActive: boolean;
}

// 用户创建请求
export interface CreateUserRequest {
  email: string;
  username: string;
  displayName: string;
  password: string;
}

// 登录请求
export interface LoginRequest {
  email: string;
  password: string;
}

// 邮件相关类型
export interface Email {
  id: string;
  messageId: string;
  userId: string;
  folderId: string;
  subject: string;
  senderEmail: string;
  senderName: string;
  recipients: string[];
  ccRecipients?: string[];
  bccRecipients?: string[];
  contentText: string;
  contentHtml: string;
  attachments: Attachment[];
  isRead: boolean;
  isStarred: boolean;
  isDeleted: boolean;
  receivedAt?: Date;
  sentAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}

// 附件类型
export interface Attachment {
  id: string;
  filename: string;
  contentType: string;
  size: number;
  url: string;
}

// 文件夹类型
export interface Folder {
  id: string;
  userId: string;
  name: string;
  type: 'inbox' | 'sent' | 'draft' | 'trash' | 'custom';
  parentId?: string;
  emailCount: number;
  unreadCount: number;
  createdAt: Date;
  updatedAt: Date;
}

// 联系人类型
export interface Contact {
  id: string;
  userId: string;
  name: string;
  email: string;
  phone?: string;
  company?: string;
  notes?: string;
  createdAt: Date;
  updatedAt: Date;
}

// API响应类型
export interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
  error?: string;
}

// 分页响应类型
export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  hasNext: boolean;
  hasPrev: boolean;
}

// JWT载荷类型
export interface JwtPayload {
  userId: string;
  email: string;
  username: string;
  iat?: number;
  exp?: number;
}

// 邮件发送请求
export interface SendEmailRequest {
  to: string[];
  cc?: string[];
  bcc?: string[];
  subject: string;
  content: string;
  attachments?: Express.Multer.File[];
}

// 搜索参数
export interface SearchParams {
  query?: string;
  folder?: string;
  from?: string;
  to?: string;
  subject?: string;
  dateFrom?: string;
  dateTo?: string;
  hasAttachment?: boolean;
  isRead?: boolean;
  isStarred?: boolean;
}

// Express扩展类型
declare global {
  namespace Express {
    interface Request {
      user?: User;
    }
  }
}

// 错误类型
export class AppError extends Error {
  public statusCode: number;
  public isOperational: boolean;

  constructor(message: string, statusCode: number) {
    super(message);
    this.statusCode = statusCode;
    this.isOperational = true;

    Error.captureStackTrace(this, this.constructor);
  }
}

// 数据库存储接口（内存存储）
export interface MemoryStore {
  users: Map<string, User>;
  emails: Map<string, Email>;
  folders: Map<string, Folder>;
  contacts: Map<string, Contact>;
}

// 邮件服务配置
export interface EmailServiceConfig {
  smtp: {
    host: string;
    port: number;
    user: string;
    pass: string;
  };
  imap: {
    host: string;
    port: number;
    user: string;
    pass: string;
  };
}
