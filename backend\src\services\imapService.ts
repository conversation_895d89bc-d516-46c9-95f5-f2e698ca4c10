import Imap from 'imap';
import { simpleParser } from 'mailparser';
import { logger } from '../utils/logger';
import { memoryDB } from '../data/memoryDB';

class ImapService {
  private imap: Imap;

  constructor() {
    this.imap = new Imap({
      user: process.env.IMAP_USER || '',
      password: process.env.IMAP_PASS || '',
      host: process.env.IMAP_HOST || '',
      port: parseInt(process.env.IMAP_PORT || '993'),
      tls: true,
      tlsOptions: { rejectUnauthorized: false }
    });

    this.setupListeners();
  }

  private setupListeners() {
    this.imap.on('error', (err) => {
      logger.error('IMAP connection error', { error: err });
    });
  }

  async fetchNewEmails(userId: string) {
    return new Promise((resolve, reject) => {
      this.imap.once('ready', () => {
        this.imap.openBox('INBOX', false, (err, box) => {
          if (err) return reject(err);

          // 获取未读邮件
          this.imap.search(['UNSEEN'], (err, results) => {
            if (err) return reject(err);
            if (results.length === 0) return resolve([]);

            const fetch = this.imap.fetch(results, { bodies: '', markSeen: true });
            const emails: any[] = [];

            fetch.on('message', (msg) => {
              msg.on('body', (stream) => {
                simpleParser(stream, async (err, parsed) => {
                  if (err) return logger.error('Error parsing email', { error: err });
                  
                  try {
                    // 保存到数据库
                    const email = await this.saveEmailToDatabase(userId, parsed);
                    emails.push(email);
                  } catch (error) {
                    logger.error('Error saving email', { error });
                  }
                });
              });
            });

            fetch.once('end', () => {
              this.imap.end();
              resolve(emails);
            });
          });
        });
      });

      this.imap.connect();
    });
  }

  private async saveEmailToDatabase(userId: string, parsedEmail: any) {
    // 从解析的邮件中提取信息
    const emailData = {
      messageId: parsedEmail.messageId,
      userId,
      folderId: 'inbox',
      subject: parsedEmail.subject || '(无主题)',
      senderEmail: parsedEmail.from?.value[0]?.address || '',
      senderName: parsedEmail.from?.value[0]?.name || '',
      recipients: parsedEmail.to?.value.map((to: any) => to.address) || [],
      ccRecipients: parsedEmail.cc?.value.map((cc: any) => cc.address) || [],
      bccRecipients: [],
      contentText: parsedEmail.text || '',
      contentHtml: parsedEmail.html || parsedEmail.text || '',
      attachments: parsedEmail.attachments || [],
      isRead: false,
      isStarred: false,
      isDeleted: false,
      sentAt: parsedEmail.date || new Date(),
      receivedAt: new Date(),
    };

    return await memoryDB.createEmail(emailData);
  }

  // 定期检查新邮件
  startPolling(userId: string, interval = 60000) {
    setInterval(() => {
      this.fetchNewEmails(userId)
        .then(emails => {
          if (emails.length > 0) {
            logger.info(`Retrieved ${emails.length} new emails for user ${userId}`);
            // 这里可以添加通知逻辑
          }
        })
        .catch(err => {
          logger.error('Error polling emails', { error: err });
        });
    }, interval);
  }
}

export default new ImapService();