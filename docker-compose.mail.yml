version: '3.8'

# 完整邮件系统 - Postfix + Dovecot + MySQL
services:
  # MySQL 数据库
  mysql:
    image: mysql:8.0
    container_name: email-mysql
    environment:
      MYSQL_ROOT_PASSWORD: root_password
      MYSQL_DATABASE: email_system
      MYSQL_USER: postfix
      MYSQL_PASSWORD: postfix_password
    volumes:
      - mysql_data:/var/lib/mysql
      - ./database/email_system.sql:/docker-entrypoint-initdb.d/01-schema.sql
      - ./mail-server/mysql/init.sql:/docker-entrypoint-initdb.d/02-users.sql
    ports:
      - "3306:3306"
    networks:
      - mail-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      timeout: 20s
      retries: 10

  # Postfix 邮件发送服务器
  postfix:
    image: postfix:latest
    container_name: email-postfix
    hostname: mail.yourdomain.com
    environment:
      - POSTFIX_myhostname=mail.yourdomain.com
      - POSTFIX_mydomain=yourdomain.com
      - POSTFIX_myorigin=yourdomain.com
    volumes:
      - ./mail-server/postfix/main.cf:/etc/postfix/main.cf
      - ./mail-server/postfix/master.cf:/etc/postfix/master.cf
      - ./mail-server/postfix/mysql-virtual-mailbox-domains.cf:/etc/postfix/mysql-virtual-mailbox-domains.cf
      - ./mail-server/postfix/mysql-virtual-mailbox-maps.cf:/etc/postfix/mysql-virtual-mailbox-maps.cf
      - ./mail-server/postfix/mysql-virtual-alias-maps.cf:/etc/postfix/mysql-virtual-alias-maps.cf
      - postfix_spool:/var/spool/postfix
      - mail_data:/var/mail/vhosts
      - ./ssl:/etc/ssl/certs
    ports:
      - "25:25"     # SMTP
      - "587:587"   # Submission
      - "465:465"   # SMTPS
    depends_on:
      mysql:
        condition: service_healthy
    networks:
      - mail-network
    restart: unless-stopped

  # Dovecot 邮件接收服务器
  dovecot:
    image: dovecot:latest
    container_name: email-dovecot
    environment:
      - DOVECOT_HOSTNAME=mail.yourdomain.com
    volumes:
      - ./mail-server/dovecot/dovecot.conf:/etc/dovecot/dovecot.conf
      - ./mail-server/dovecot/auth-sql.conf.ext:/etc/dovecot/conf.d/auth-sql.conf.ext
      - ./mail-server/dovecot/dovecot-sql.conf.ext:/etc/dovecot/dovecot-sql.conf.ext
      - mail_data:/var/mail/vhosts
      - dovecot_run:/var/run/dovecot
      - ./ssl:/etc/ssl/certs
    ports:
      - "143:143"   # IMAP
      - "993:993"   # IMAPS
      - "110:110"   # POP3
      - "995:995"   # POP3S
    depends_on:
      mysql:
        condition: service_healthy
    networks:
      - mail-network
    restart: unless-stopped

  # 后端API服务
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: email-backend
    environment:
      - NODE_ENV=production
      - PORT=5000
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_USER=postfix
      - DB_PASSWORD=postfix_password
      - DB_NAME=email_system
      - SMTP_HOST=postfix
      - SMTP_PORT=587
      - SMTP_USER=<EMAIL>
      - SMTP_PASS=system_password
      - IMAP_HOST=dovecot
      - IMAP_PORT=993
      - IMAP_SECURE=true
      - JWT_SECRET=your-production-jwt-secret
    volumes:
      - uploads:/app/uploads
      - logs:/app/logs
    ports:
      - "5000:5000"
    depends_on:
      mysql:
        condition: service_healthy
      postfix:
        condition: service_started
      dovecot:
        condition: service_started
    networks:
      - mail-network
    restart: unless-stopped

  # 前端应用
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: email-frontend
    environment:
      - NODE_ENV=production
    ports:
      - "80:80"
      - "443:443"
    depends_on:
      - backend
    networks:
      - mail-network
    restart: unless-stopped

  # Redis 缓存
  redis:
    image: redis:7-alpine
    container_name: email-redis
    command: redis-server --appendonly yes --requirepass redis_password
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - mail-network
    restart: unless-stopped

  # 邮件管理界面 (可选)
  roundcube:
    image: roundcube/roundcubemail:latest
    container_name: email-roundcube
    environment:
      - ROUNDCUBEMAIL_DB_TYPE=mysql
      - ROUNDCUBEMAIL_DB_HOST=mysql
      - ROUNDCUBEMAIL_DB_USER=postfix
      - ROUNDCUBEMAIL_DB_PASSWORD=postfix_password
      - ROUNDCUBEMAIL_DB_NAME=roundcube
      - ROUNDCUBEMAIL_DEFAULT_HOST=dovecot
      - ROUNDCUBEMAIL_SMTP_SERVER=postfix
      - ROUNDCUBEMAIL_SMTP_PORT=587
    volumes:
      - roundcube_data:/var/www/html
    ports:
      - "8080:80"
    depends_on:
      mysql:
        condition: service_healthy
      dovecot:
        condition: service_started
    networks:
      - mail-network
    restart: unless-stopped
    profiles:
      - webmail

  # 邮件监控 (可选)
  mailhog:
    image: mailhog/mailhog:latest
    container_name: email-mailhog
    ports:
      - "1025:1025"  # SMTP
      - "8025:8025"  # Web UI
    networks:
      - mail-network
    restart: unless-stopped
    profiles:
      - testing

  # 垃圾邮件过滤 (可选)
  spamassassin:
    image: spamassassin:latest
    container_name: email-spamassassin
    volumes:
      - spamassassin_data:/var/lib/spamassassin
    networks:
      - mail-network
    restart: unless-stopped
    profiles:
      - filtering

  # 病毒扫描 (可选)
  clamav:
    image: clamav/clamav:latest
    container_name: email-clamav
    volumes:
      - clamav_data:/var/lib/clamav
    networks:
      - mail-network
    restart: unless-stopped
    profiles:
      - filtering

# 网络配置
networks:
  mail-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# 数据卷
volumes:
  mysql_data:
    driver: local
  redis_data:
    driver: local
  mail_data:
    driver: local
  postfix_spool:
    driver: local
  dovecot_run:
    driver: local
  uploads:
    driver: local
  logs:
    driver: local
  roundcube_data:
    driver: local
  spamassassin_data:
    driver: local
  clamav_data:
    driver: local
