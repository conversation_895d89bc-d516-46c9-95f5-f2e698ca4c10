import React from 'react';
import { <PERSON>, <PERSON>, Col, <PERSON>ati<PERSON>, Typography, Button, Space } from 'antd';
import {
  MailOutlined,
  InboxOutlined,
  SendOutlined,
  EditOutlined,
  StarOutlined,
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { useFolders } from '../../hooks/useFolders';

const { Title, Paragraph } = Typography;

const Dashboard: React.FC = () => {
  const navigate = useNavigate();
  const { data: folders = [] } = useFolders();

  const getStatistics = () => {
    const inbox = folders.find((f: any) => f.type === 'inbox');
    const sent = folders.find((f: any) => f.type === 'sent');
    const draft = folders.find((f: any) => f.type === 'draft');
    
    return {
      totalEmails: inbox?.emailCount || 0,
      unreadEmails: inbox?.unreadCount || 0,
      sentEmails: sent?.emailCount || 0,
      draftEmails: draft?.emailCount || 0,
    };
  };

  const stats = getStatistics();

  const quickActions = [
    {
      title: '撰写邮件',
      icon: <EditOutlined />,
      description: '创建新邮件',
      action: () => navigate('/compose'),
      type: 'primary' as const,
    },
    {
      title: '查看收件箱',
      icon: <InboxOutlined />,
      description: '查看收到的邮件',
      action: () => navigate('/folder/inbox'),
      type: 'default' as const,
    },
    {
      title: '已发送',
      icon: <SendOutlined />,
      description: '查看已发送的邮件',
      action: () => navigate('/folder/sent'),
      type: 'default' as const,
    },
    {
      title: '草稿箱',
      icon: <EditOutlined />,
      description: '查看草稿邮件',
      action: () => navigate('/folder/draft'),
      type: 'default' as const,
    },
  ];

  return (
    <div className="p-6">
      <div className="mb-8">
        <Title level={2} className="mb-2">
          <MailOutlined className="mr-3" />
          邮箱系统仪表板
        </Title>
        <Paragraph className="text-gray-600">
          欢迎使用邮箱系统！这里是您的邮件管理中心。
        </Paragraph>
      </div>

      {/* 统计卡片 */}
      <Row gutter={[16, 16]} className="mb-8">
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="总邮件数"
              value={stats.totalEmails}
              prefix={<MailOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="未读邮件"
              value={stats.unreadEmails}
              prefix={<InboxOutlined />}
              valueStyle={{ color: '#f5222d' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="已发送"
              value={stats.sentEmails}
              prefix={<SendOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="草稿"
              value={stats.draftEmails}
              prefix={<EditOutlined />}
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 快速操作 */}
      <Card title="快速操作" className="mb-8">
        <Row gutter={[16, 16]}>
          {quickActions.map((action, index) => (
            <Col xs={24} sm={12} md={6} key={index}>
              <Card
                hoverable
                className="text-center h-full"
                onClick={action.action}
              >
                <div className="text-4xl mb-4 text-primary-500">
                  {action.icon}
                </div>
                <Title level={4} className="mb-2">
                  {action.title}
                </Title>
                <Paragraph className="text-gray-600 mb-4">
                  {action.description}
                </Paragraph>
                <Button type={action.type} block>
                  立即使用
                </Button>
              </Card>
            </Col>
          ))}
        </Row>
      </Card>

      {/* 最近活动 */}
      <Card title="最近活动" className="mb-8">
        <div className="text-center py-8">
          <StarOutlined className="text-6xl text-gray-300 mb-4" />
          <Title level={4} className="text-gray-500">
            暂无最近活动
          </Title>
          <Paragraph className="text-gray-400">
            开始使用邮箱系统，这里将显示您的最近活动记录。
          </Paragraph>
          <Space>
            <Button type="primary" onClick={() => navigate('/compose')}>
              发送第一封邮件
            </Button>
            <Button onClick={() => navigate('/folder/inbox')}>
              查看收件箱
            </Button>
          </Space>
        </div>
      </Card>
    </div>
  );
};

export default Dashboard;
