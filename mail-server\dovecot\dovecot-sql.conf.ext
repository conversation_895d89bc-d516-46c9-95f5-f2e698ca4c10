# Dovecot SQL 数据库配置

# ================================
# 数据库连接
# ================================
driver = mysql
connect = host=localhost dbname=email_system user=dovecot password=dovecot_password

# ================================
# 默认密码方案
# ================================
default_pass_scheme = PLAIN

# ================================
# 密码查询
# ================================
password_query = \
  SELECT email as user, password, \
    CONCAT('/var/mail/vhosts/', domain, '/', email, '/') as userdb_home, \
    CONCAT('maildir:/var/mail/vhosts/', domain, '/', email, '/') as userdb_mail, \
    5000 as userdb_uid, 5000 as userdb_gid, \
    quota as userdb_quota_rule \
  FROM mail_accounts ma \
  JOIN mail_domains md ON ma.domain_id = md.id \
  WHERE ma.email = '%u' AND ma.status = 'active' AND md.status = 'active'

# ================================
# 用户查询
# ================================
user_query = \
  SELECT \
    CONCAT('/var/mail/vhosts/', domain, '/', email, '/') as home, \
    CONCAT('maildir:/var/mail/vhosts/', domain, '/', email, '/') as mail, \
    5000 as uid, 5000 as gid, \
    quota as quota_rule \
  FROM mail_accounts ma \
  JOIN mail_domains md ON ma.domain_id = md.id \
  WHERE ma.email = '%u' AND ma.status = 'active' AND md.status = 'active'

# ================================
# 用户迭代查询 (用于 doveadm)
# ================================
iterate_query = \
  SELECT email as username \
  FROM mail_accounts ma \
  JOIN mail_domains md ON ma.domain_id = md.id \
  WHERE ma.status = 'active' AND md.status = 'active'
