import React from 'react';
// import { useForm } from 'react-hook-form';
import { Link, useNavigate } from 'react-router-dom';
import { Button, Input, Form, Alert, Card, Typography } from 'antd';
import { UserOutlined, LockOutlined, MailOutlined } from '@ant-design/icons';
import { useAuthStore } from '../../store/authStore';
// import type { LoginRequest } from '../../types';

const { Title, Text } = Typography;

interface LoginFormData {
  email: string;
  password: string;
}

const LoginForm: React.FC = () => {
  const navigate = useNavigate();
  const { login, isLoading, error, clearError } = useAuthStore();
  const [form] = Form.useForm();

  const onFinish = async (values: LoginFormData) => {
    try {
      clearError();
      await login(values);
      navigate('/');
    } catch (error) {
      // 错误已经在store中处理
      console.error(error);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div className="text-center">
          <MailOutlined className="text-6xl text-primary-600 mb-4" />
          <Title level={2} className="text-gray-900">
            登录到邮箱系统
          </Title>
          <Text className="text-gray-600">
            请输入您的邮箱和密码
          </Text>
        </div>

        <Card className="shadow-lg">
          {error && (
            <Alert
              message={error}
              type="error"
              showIcon
              closable
              onClose={clearError}
              className="mb-4"
            />
          )}

          <Form
            form={form}
            name="login"
            onFinish={onFinish}
            layout="vertical"
            size="large"
          >
            <Form.Item
              name="email"
              label="邮箱地址"
              rules={[
                { required: true, message: '请输入邮箱地址' },
                { type: 'email', message: '请输入有效的邮箱地址' },
              ]}
            >
              <Input
                prefix={<UserOutlined />}
                placeholder="请输入邮箱"
                autoComplete="email"
              />
            </Form.Item>

            <Form.Item
              name="password"
              label="密码"
              rules={[
                { required: true, message: '请输入密码' },
                { min: 6, message: '密码至少6位' },
              ]}
            >
              <Input.Password
                prefix={<LockOutlined />}
                placeholder="请输入密码"
                autoComplete="current-password"
              />
            </Form.Item>

            <Form.Item>
              <Button
                type="primary"
                htmlType="submit"
                loading={isLoading}
                className="w-full"
                size="large"
              >
                {isLoading ? '登录中...' : '登录'}
              </Button>
            </Form.Item>
          </Form>

          <div className="text-center space-y-2">
            <div>
              <Link
                to="/forgot-password"
                className="text-primary-600 hover:text-primary-500"
              >
                忘记密码？
              </Link>
            </div>
            <div>
              <Text className="text-gray-600">
                还没有账户？{' '}
                <Link
                  to="/register"
                  className="text-primary-600 hover:text-primary-500 font-medium"
                >
                  立即注册
                </Link>
              </Text>
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default LoginForm;
