import { useQuery, useMutation, useQueryClient, useInfiniteQuery } from '@tanstack/react-query';
import { apiService } from '../services/api';
import { queryKeys, queryOptions, getRelatedQueryKeys } from '../lib/queryKeys';
import { showErrorMessage, showErrorNotification } from '../utils/errorHandler';
import { message } from 'antd';
import type { Email, ComposeEmailRequest, SearchParams, PaginatedResponse } from '../types';

// 获取邮件列表（无限滚动）
export const useInfiniteEmails = (params: {
  folderId?: string;
  search?: SearchParams;
}) => {
  return useInfiniteQuery({
    queryKey: queryKeys.emails.list(params),
    queryFn: async ({ pageParam = 1 }) => {
      const response = await apiService.getEmails({
        ...params,
        page: pageParam,
        limit: 20,
      });
      return response.data.data;
    },
    getNextPageParam: (lastPage: PaginatedResponse<Email>) => {
      return lastPage.hasNext ? lastPage.page + 1 : undefined;
    },
    initialPageParam: 1,
    ...queryOptions.shortTerm,
  });
};

// 获取邮件列表（分页）
export const useEmails = (params: {
  folderId?: string;
  page?: number;
  limit?: number;
  search?: SearchParams;
}) => {
  return useQuery({
    queryKey: queryKeys.emails.list(params),
    queryFn: async () => {
      const response = await apiService.getEmails(params);
      return response.data.data;
    },
    ...queryOptions.shortTerm,
  });
};

// 获取单个邮件详情
export const useEmail = (emailId: string, enabled = true) => {
  return useQuery({
    queryKey: queryKeys.emails.detail(emailId),
    queryFn: async () => {
      const response = await apiService.getEmail(emailId);
      return response.data.data;
    },
    enabled: enabled && !!emailId,
    ...queryOptions.shortTerm,
  });
};

// 搜索邮件
export const useSearchEmails = (params: SearchParams, enabled = true) => {
  return useQuery({
    queryKey: queryKeys.emails.search(params),
    queryFn: async () => {
      const response = await apiService.searchEmails(params);
      return response.data.data;
    },
    enabled: enabled && !!(params.query || params.sender || params.subject),
    ...queryOptions.shortTerm,
  });
};

// 发送邮件
export const useSendEmail = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (emailData: ComposeEmailRequest) => {
      const response = await apiService.sendEmail(emailData);
      return response.data.data;
    },
    onSuccess: (newEmail) => {
      // 成功后失效相关查询
      getRelatedQueryKeys.onEmailChange(newEmail.id, newEmail.folderId).forEach(key => {
        queryClient.invalidateQueries({ queryKey: key });
      });

      message.success('邮件发送成功');
    },
    onError: (error) => {
      showErrorNotification(error, '邮件发送失败');
    },
  });
};

// 更新邮件状态
export const useUpdateEmail = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ id, updates }: { id: string; updates: Partial<Email> }) => {
      const response = await apiService.updateEmail(id, updates);
      return response.data.data;
    },
    onMutate: async ({ id, updates }) => {
      // 乐观更新
      await queryClient.cancelQueries({ queryKey: queryKeys.emails.detail(id) });
      
      const previousEmail = queryClient.getQueryData(queryKeys.emails.detail(id));
      
      if (previousEmail) {
        queryClient.setQueryData(queryKeys.emails.detail(id), {
          ...previousEmail,
          ...updates,
        });
      }

      // 更新列表中的邮件
      queryClient.setQueriesData(
        { queryKey: queryKeys.emails.lists() },
        (oldData: any) => {
          if (!oldData) return oldData;
          
          if (oldData.pages) {
            // 无限查询数据结构
            return {
              ...oldData,
              pages: oldData.pages.map((page: any) => ({
                ...page,
                data: page.data.map((email: Email) =>
                  email.id === id ? { ...email, ...updates } : email
                ),
              })),
            };
          } else {
            // 普通查询数据结构
            return {
              ...oldData,
              data: oldData.data.map((email: Email) =>
                email.id === id ? { ...email, ...updates } : email
              ),
            };
          }
        }
      );

      return { previousEmail };
    },
    onError: (error, { id }, context) => {
      // 回滚乐观更新
      if (context?.previousEmail) {
        queryClient.setQueryData(queryKeys.emails.detail(id), context.previousEmail);
      }
      showErrorMessage(error, '更新邮件失败');
    },
    onSettled: (data, error, { id }) => {
      // 重新获取数据确保一致性
      queryClient.invalidateQueries({ queryKey: queryKeys.emails.detail(id) });
      queryClient.invalidateQueries({ queryKey: queryKeys.emails.lists() });
    },
  });
};

// 删除邮件
export const useDeleteEmail = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (emailId: string) => {
      await apiService.deleteEmail(emailId);
      return emailId;
    },
    onMutate: async (emailId) => {
      // 乐观更新 - 从列表中移除邮件
      queryClient.setQueriesData(
        { queryKey: queryKeys.emails.lists() },
        (oldData: any) => {
          if (!oldData) return oldData;
          
          if (oldData.pages) {
            // 无限查询数据结构
            return {
              ...oldData,
              pages: oldData.pages.map((page: any) => ({
                ...page,
                data: page.data.filter((email: Email) => email.id !== emailId),
              })),
            };
          } else {
            // 普通查询数据结构
            return {
              ...oldData,
              data: oldData.data.filter((email: Email) => email.id !== emailId),
            };
          }
        }
      );

      return { emailId };
    },
    onSuccess: (emailId) => {
      // 移除详情缓存
      queryClient.removeQueries({ queryKey: queryKeys.emails.detail(emailId) });
      
      // 失效相关查询
      getRelatedQueryKeys.onEmailChange(emailId).forEach(key => {
        queryClient.invalidateQueries({ queryKey: key });
      });

      message.success('邮件删除成功');
    },
    onError: (error, emailId, context) => {
      // 重新获取数据
      queryClient.invalidateQueries({ queryKey: queryKeys.emails.lists() });
      showErrorMessage(error, '删除邮件失败');
    },
  });
};

// 批量操作邮件
export const useBatchUpdateEmails = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ 
      emailIds, 
      updates 
    }: { 
      emailIds: string[]; 
      updates: Partial<Email> 
    }) => {
      // 这里假设后端支持批量更新，如果不支持可以并发调用单个更新
      const promises = emailIds.map(id => apiService.updateEmail(id, updates));
      const responses = await Promise.all(promises);
      return responses.map(response => response.data.data);
    },
    onSuccess: (updatedEmails) => {
      // 失效相关查询
      getRelatedQueryKeys.onEmailChange().forEach(key => {
        queryClient.invalidateQueries({ queryKey: key });
      });

      message.success(`成功更新 ${updatedEmails.length} 封邮件`);
    },
    onError: (error) => {
      showErrorMessage(error, '批量更新邮件失败');
    },
  });
};

// 预取邮件详情
export const usePrefetchEmail = () => {
  const queryClient = useQueryClient();

  return (emailId: string) => {
    queryClient.prefetchQuery({
      queryKey: queryKeys.emails.detail(emailId),
      queryFn: async () => {
        const response = await apiService.getEmail(emailId);
        return response.data.data;
      },
      ...queryOptions.shortTerm,
    });
  };
};
