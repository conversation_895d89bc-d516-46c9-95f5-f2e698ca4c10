/**
 * 虚拟滚动列表组件
 * 优化大量数据的渲染性能
 */

import React, { useState, useEffect, useCallback, useMemo, useRef } from 'react';
import { VIRTUAL_SCROLL_CONFIG } from '../../config/performance';
import { throttle } from '../../utils/performance';

interface VirtualListProps<T> {
  items: T[];
  itemHeight: number;
  containerHeight: number;
  renderItem: (item: T, index: number) => React.ReactNode;
  overscan?: number;
  onScroll?: (scrollTop: number) => void;
  className?: string;
  style?: React.CSSProperties;
}

interface VirtualListState {
  scrollTop: number;
  startIndex: number;
  endIndex: number;
  visibleItems: any[];
}

function VirtualList<T>({
  items,
  itemHeight = VIRTUAL_SCROLL_CONFIG.ITEM_HEIGHT,
  containerHeight,
  renderItem,
  overscan = VIRTUAL_SCROLL_CONFIG.OVERSCAN,
  onScroll,
  className,
  style,
}: VirtualListProps<T>) {
  const [state, setState] = useState<VirtualListState>({
    scrollTop: 0,
    startIndex: 0,
    endIndex: 0,
    visibleItems: [],
  });

  const containerRef = useRef<HTMLDivElement>(null);
  const totalHeight = items.length * itemHeight;

  // 计算可见项目范围
  const calculateVisibleRange = useCallback((scrollTop: number) => {
    const visibleStart = Math.floor(scrollTop / itemHeight);
    const visibleEnd = Math.min(
      visibleStart + Math.ceil(containerHeight / itemHeight),
      items.length - 1
    );

    const startIndex = Math.max(0, visibleStart - overscan);
    const endIndex = Math.min(items.length - 1, visibleEnd + overscan);

    return { startIndex, endIndex };
  }, [itemHeight, containerHeight, overscan, items.length]);

  // 更新可见项目
  const updateVisibleItems = useCallback((scrollTop: number) => {
    const { startIndex, endIndex } = calculateVisibleRange(scrollTop);
    const visibleItems = items.slice(startIndex, endIndex + 1);

    setState(prevState => ({
      ...prevState,
      scrollTop,
      startIndex,
      endIndex,
      visibleItems,
    }));
  }, [calculateVisibleRange, items]);

  // 节流的滚动处理函数
  const throttledScrollHandler = useMemo(
    () => throttle((scrollTop: number) => {
      updateVisibleItems(scrollTop);
      onScroll?.(scrollTop);
    }, 16), // 60fps
    [updateVisibleItems, onScroll]
  );

  // 滚动事件处理
  const handleScroll = useCallback((event: React.UIEvent<HTMLDivElement>) => {
    const scrollTop = event.currentTarget.scrollTop;
    throttledScrollHandler(scrollTop);
  }, [throttledScrollHandler]);

  // 初始化和数据变化时更新
  useEffect(() => {
    updateVisibleItems(state.scrollTop);
  }, [items, updateVisibleItems]);

  // 渲染可见项目
  const renderVisibleItems = () => {
    return state.visibleItems.map((item, index) => {
      const actualIndex = state.startIndex + index;
      const top = actualIndex * itemHeight;

      return (
        <div
          key={actualIndex}
          style={{
            position: 'absolute',
            top,
            left: 0,
            right: 0,
            height: itemHeight,
          }}
        >
          {renderItem(item, actualIndex)}
        </div>
      );
    });
  };

  return (
    <div
      ref={containerRef}
      className={className}
      style={{
        height: containerHeight,
        overflow: 'auto',
        position: 'relative',
        ...style,
      }}
      onScroll={handleScroll}
    >
      {/* 总高度占位符 */}
      <div style={{ height: totalHeight, position: 'relative' }}>
        {renderVisibleItems()}
      </div>
    </div>
  );
}

export default React.memo(VirtualList) as typeof VirtualList;
